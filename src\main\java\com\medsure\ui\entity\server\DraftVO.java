package com.medsure.ui.entity.server;

import java.io.Serializable;

public class DraftVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long patientId;
    private Long draftId;
    private Long patientAlertId;
    private Double duration;

    private String encounterReason;
    private String reasonCode;


    private String encounterDescription;
    private String encounterDateTime;
    private String encounterEndDateTime;
//	private String encounterResult;

    private String addedByUser;
    private Long userId;
    private String review;
    private String durationInMS;

    private String draftCreatedDate;
    private String draftModifiedDate;

    public Long getPatientAlertId() {
        return patientAlertId;
    }

    public void setPatientAlertId(Long patientAlertId) {
        this.patientAlertId = patientAlertId;
    }

    public String getAddedByUser() {
        return addedByUser;
    }

    public void setAddedByUser(String addedByUser) {
        this.addedByUser = addedByUser;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public Long getDraftId() {
        return draftId;
    }

    public void setDraftId(Long draftId) {
        this.draftId = draftId;
    }

    public String getEncounterReason() {
        return encounterReason;
    }

    public void setEncounterReason(String encounterReason) {
        this.encounterReason = encounterReason;
    }

    public String getEncounterDescription() {
        return encounterDescription;
    }

    public void setEncounterDescription(String encounterDescription) {
        this.encounterDescription = encounterDescription;
    }

    public String getEncounterDateTime() {
        return encounterDateTime;
    }

    public void setEncounterDateTime(String encounterDateTime) {
        this.encounterDateTime = encounterDateTime;
    }

    public String getEncounterEndDateTime() {
        return encounterEndDateTime;
    }

    public void setEncounterEndDateTime(String encounterEndDateTime) {
        this.encounterEndDateTime = encounterEndDateTime;
    }

    /**
     * @return the duration
     */
    public Double getDuration() {
        return duration;
    }

    /**
     * @param duration the duration to set
     */
    public void setDuration(Double duration) {
        this.duration = duration;
    }

    /**
     * @return the enMins
     */

    public String getReview() {
        return review;
    }

    public void setReview(String review) {
        this.review = review;
    }

    public String getDurationInMS() {
        return durationInMS;
    }

    public void setDurationInMS(String durationInMS) {
        this.durationInMS = durationInMS;
    }

    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getDraftCreatedDate() {
        return draftCreatedDate;
    }

    public void setDraftCreatedDate(String draftCreatedDate) {
        this.draftCreatedDate = draftCreatedDate;
    }

    public String getDraftModifiedDate() {
        return draftModifiedDate;
    }

    public void setDraftModifiedDate(String draftModifiedDate) {
        this.draftModifiedDate = draftModifiedDate;
    }
}
