package com.medsure.model;

import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "WATCHRX_PATIENT_ENCOUNTER_DRAFTS")
public class WatchRxEncounterDrafts extends Auditable<String> implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    private Long draftId;

    @Column(name = "ENCOUNTER_REASON")
    private String encounterReason;

    @Column(name = "ENCOUNTER_DESCRIPTION", columnDefinition = "LONGTEXT")
    private String encounterDescription;

    @Column(name = "DURATION" ,columnDefinition = "DOUBLE DEFAULT 0.0")
    private Double duration;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_DATE", nullable = false, length = 19)
    private Date encounterDatetime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_END_DATE", nullable = true, length = 19)
    private Date encounterEndDatetime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "DRAFT_CREATED_DATE")
    private Date draftCreatedDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "DRAFT_MODIFIED_DATE")
    private Date draftModifiedDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "FK_PATIENT_ID", nullable = false)
    private WatchrxPatient watchrxPatient;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "FK_USER_ID", nullable = false)
    private WatchrxUser watchrxUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "FK_PATIENT_ALERT_ID", nullable = true)
    private WatchrxPatientAlert watchrxPatientAlert;

    @Column(name = "REVIEW", length = 1024)
    private String review;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ENCOUNTER_REASON_CODE", referencedColumnName = "CODE")
    private WatchRxEncounterReasons watchRxEncounterReasons;

    public WatchRxEncounterReasons getWatchRxEncounterReasons() {
        return watchRxEncounterReasons;
    }

    public void setWatchRxEncounterReasons(WatchRxEncounterReasons watchRxEncounterReasons) {
        this.watchRxEncounterReasons = watchRxEncounterReasons;
    }

    public String getReview() {
        return review;
    }

    public void setReview(String review) {
        this.review = review;
    }

    public WatchrxPatientAlert getWatchrxPatientAlert() {
        return watchrxPatientAlert;
    }

    public void setWatchrxPatientAlert(WatchrxPatientAlert watchrxPatientAlert) {
        this.watchrxPatientAlert = watchrxPatientAlert;
    }

    public WatchrxUser getWatchrxUser() {
        return watchrxUser;
    }

    public void setWatchrxUser(WatchrxUser watchrxUser) {
        this.watchrxUser = watchrxUser;
    }

    public Long getDraftId() {
        return draftId;
    }

    public void setDraftId(Long draftId) {
        this.draftId = draftId;
    }

    public String getEncounterReason() {
        return encounterReason;
    }

    public void setEncounterReason(String encounterReason) {
        this.encounterReason = encounterReason;
    }

    public String getEncounterDescription() {
        return encounterDescription;
    }

    public void setEncounterDescription(String encounterDescription) {
        this.encounterDescription = encounterDescription;
    }

    public Date getEncounterDatetime() {
        return encounterDatetime;
    }

    public void setEncounterDatetime(Date encounterDatetime) {
        this.encounterDatetime = encounterDatetime;
    }

    public Date getEncounterEndDatetime() {
        return encounterEndDatetime;
    }

    public void setEncounterEndDatetime(Date encounterEndDatetime) {
        this.encounterEndDatetime = encounterEndDatetime;
    }

    public Date getDraftCreatedDate() {
        return draftCreatedDate;
    }

    public void setDraftCreatedDate(Date draftCreatedDate) {
        this.draftCreatedDate = draftCreatedDate;
    }

    public Date getDraftModifiedDate() {
        return draftModifiedDate;
    }

    public void setDraftModifiedDate(Date draftModifiedDate) {
        this.draftModifiedDate = draftModifiedDate;
    }

    public WatchrxPatient getWatchrxPatient() {
        return watchrxPatient;
    }

    public void setWatchrxPatient(WatchrxPatient watchrxPatient) {
        this.watchrxPatient = watchrxPatient;
    }

    public Double getDuration() {
        return duration;
    }

    public void setDuration(Double duration) {
        this.duration = duration;
    }

}
