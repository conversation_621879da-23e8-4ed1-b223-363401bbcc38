/**
 * 
 */

package com.medsure.service.impl;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Formatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.logging.Logger;

import javax.mail.MessagingException;
import javax.mail.internet.AddressException;
import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.medsure.common.Constants;
import com.medsure.dao.AddressDAO;
import com.medsure.dao.CaseManagerPatientTimerDAO;
import com.medsure.dao.ClinicianDAO;
import com.medsure.dao.ClinicianScheduleDAO;
import com.medsure.dao.ClinicianWatchAssignmentDAO;
import com.medsure.dao.EmailGroupMembersDAO;
import com.medsure.dao.EmailMessagesDAO;
import com.medsure.dao.EmailReceiversDAO;
import com.medsure.dao.EncountersNewDAO;
import com.medsure.dao.GroupDAO;
import com.medsure.dao.GroupUserAssignmentDAO;
import com.medsure.dao.PatientAlertDAO;
import com.medsure.dao.PatientCareGiverAssignmentDAO;
import com.medsure.dao.PatientClinicianAssignmentDAO;
import com.medsure.dao.PatientDAO;
import com.medsure.dao.PatientDataDto;
import com.medsure.dao.PatientWatchAssignmentDAO;
import com.medsure.dao.PhysicianCaseManagerAssignmentDAO;
import com.medsure.dao.PhysicianDAO;
import com.medsure.dao.SecCGInviteDAO;
import com.medsure.dao.TasksDAO;
import com.medsure.dao.UserDAO;
import com.medsure.dao.WatchDAO;
import com.medsure.dao.WatchRxCalendarTaskDAO;
import com.medsure.dao.WatchRxCarePlanQuestionsDAO;
import com.medsure.dao.WatchRxCarePlanTabsConfigDAO;
import com.medsure.dao.WatchRxClinicianDeviceDAO;
import com.medsure.dao.WatchRxPatientPhoneCommunicationDAO;
import com.medsure.dao.WatchRxPatientRpmCcmDataDetailsDAO;
import com.medsure.dao.WatchRxPhysicianBillingDAO;
import com.medsure.dao.WatchrxMailingAddressDAO;
import com.medsure.dao.WatchrxWatchBuyingAllocationDAO;
import com.medsure.exception.ClinicianNotFoundException;
import com.medsure.exception.WatchNotFoundException;
import com.medsure.exception.WatchRxServiceException;
import com.medsure.factory.WatchRxFactory;
import com.medsure.model.WatchRxCalendarTask;
import com.medsure.model.WatchRxCarePlanQuestions;
import com.medsure.model.WatchRxCarePlanTabsConfig;
import com.medsure.model.WatchRxClinicianDevice;
import com.medsure.model.WatchRxClinicianPatientTimerReport;
import com.medsure.model.WatchRxPhysician;
import com.medsure.model.WatchRxTasks;
import com.medsure.model.WatchrxAddress;
import com.medsure.model.WatchrxClinician;
import com.medsure.model.WatchrxClinicianWatchAssignmnt;
import com.medsure.model.WatchrxGroup;
import com.medsure.model.WatchrxGroupUserAssignment;
import com.medsure.model.WatchrxMailingAddress;
import com.medsure.model.WatchrxPatient;
import com.medsure.model.WatchrxPatientCareGiverAssignmnt;
import com.medsure.model.WatchrxPatientClinicianAssignmnt;
import com.medsure.model.WatchrxPatientWatchAssignmnt;
import com.medsure.model.WatchrxPhysicianCaseManagerAssignment;
import com.medsure.model.WatchrxSecCGInvite;
import com.medsure.model.WatchrxStorePlanSubscription;
import com.medsure.model.WatchrxUser;
import com.medsure.model.WatchrxWatch;
import com.medsure.model.WatchrxWatchBuyingAllocation;
import com.medsure.service.ClinicianService;
import com.medsure.service.GroupRoleService;
import com.medsure.service.UserService;
import com.medsure.test.caregiver.CareGiverTest;
import com.medsure.test.patient.PatientTest;
import com.medsure.ui.entity.access.Group;
import com.medsure.ui.entity.caregiver.request.Login;
import com.medsure.ui.entity.diyva.DiyvaDialog;
import com.medsure.ui.entity.jwt.JwtRequest;
import com.medsure.ui.entity.server.AddressVO;
import com.medsure.ui.entity.server.AssignPatientsToCareGiverVO;
import com.medsure.ui.entity.server.AssignToPatientVO;
import com.medsure.ui.entity.server.AsssignCMToPatientVO;
import com.medsure.ui.entity.server.CareGiverMinimalVO;
import com.medsure.ui.entity.server.CaregiverListVO;
import com.medsure.ui.entity.server.CaseManagerPatientsAssignVO;
import com.medsure.ui.entity.server.CaseManagerVO;
import com.medsure.ui.entity.server.ClinicianGCMVO;
import com.medsure.ui.entity.server.ClinicianPatientTimerVO;
import com.medsure.ui.entity.server.ClinicianVO;
import com.medsure.ui.entity.server.CriticalAlertsCountVO;
import com.medsure.ui.entity.server.LoggedUserNotificationVO;
import com.medsure.ui.entity.server.PatientClinician;
import com.medsure.ui.entity.server.PatientCountVO;
import com.medsure.ui.entity.server.PatientMinimalListVO;
import com.medsure.ui.entity.server.PatientMinimalVO;
import com.medsure.ui.entity.server.PatientWatchCaregiverInfoVO;
import com.medsure.ui.entity.server.PatientWatchCaregiverListVO;
import com.medsure.ui.entity.server.PhysicianCaseManagerAssignVO;
import com.medsure.ui.entity.server.PhysicianListVO;
import com.medsure.ui.entity.server.PhysicianVO;
import com.medsure.ui.entity.server.RPMPhysicianCaseManagersResponseVO;
import com.medsure.ui.entity.server.RegistrationVO;
import com.medsure.ui.entity.server.StatusVO;
import com.medsure.ui.entity.server.TaskListVO;
import com.medsure.ui.entity.server.TasksVO;
import com.medsure.ui.entity.server.WatchrxCaseManagerPatientsAssignmentResponseListVO;
import com.medsure.ui.entity.server.WatchrxCaseManagerPatientsAssignmentResponseVO;
import com.medsure.ui.entity.server.WatchrxPhysicianCaseManagerAssignmentResponseListVO;
import com.medsure.ui.entity.server.WatchrxPhysicianCaseManagerAssignmentResponseVO;
import com.medsure.ui.entity.server.Clinician.ClinicianOrgDetails;
import com.medsure.ui.entity.server.Clinician.ClinicianOrgDropDown;
import com.medsure.ui.entity.server.Clinician.ClinicianResponse;
import com.medsure.ui.entity.server.Clinician.ClinicianResponseVO;
import com.medsure.ui.entity.server.Clinician.ClinicianWithAssociatedWatchesAndPatientsVO;
import com.medsure.ui.entity.server.Clinician.PatientDetailsVO;
import com.medsure.ui.entity.server.Clinician.WatchDetailsVO;
import com.medsure.ui.entity.server.calendar.WatchRxCalendarTaskVo;
import com.medsure.ui.util.WatchRxUtils;
import com.medsure.util.DropdownUtils;
import com.medsure.util.OTPGenerator;
import com.medsure.util.SendMail;

/**
 * <AUTHOR>
 *
 */
@Component(value = "clinicianService")
public class ClinicianServiceImpl implements ClinicianService {

	@Autowired
	ClinicianDAO clinicianDAO;

	@Autowired
	AddressDAO addressDAO;

	@Autowired
	WatchrxMailingAddressDAO mailingAddressDAO;

	@Autowired
	WatchRxPhysicianBillingDAO billingDao;

	@Autowired
	UserDAO userDAO;

	@Autowired
	WatchDAO watchDAO;

	@Autowired
	PatientDAO patientDAO;

	@Autowired
	PatientClinicianAssignmentDAO patientClinicianAssignmentDAO;

	@Autowired
	ClinicianWatchAssignmentDAO clinicianWatchAssignmentDAO;

	@Autowired
	WatchrxWatchBuyingAllocationDAO buyingAllocationDAO;

	@Autowired
	PatientWatchAssignmentDAO patientWatchAssignmentDAO;

	@Autowired
	ClinicianScheduleDAO clinicianScheduleDAO;

	@Autowired
	SecCGInviteDAO secCGInviteDAO;

	@Autowired
	PhysicianDAO physicianDAO;

	@Autowired
	PhysicianCaseManagerAssignmentDAO physicianCaseManagerAssignmentDAO;

	@Autowired
	TasksDAO tasksDAO;

	@Autowired
	EncountersNewDAO encountersNewDAO;

	@Autowired
	PatientAlertDAO patientAlertDAO;

	@Autowired
	WatchRxPatientPhoneCommunicationDAO watchRxPatientPhoneCommunicationDAO;

	@Autowired
	EmailReceiversDAO emailReceiversDAO;

	@Autowired
	PatientCareGiverAssignmentDAO patientCareGiverAssignmentDAO;

	@Autowired
	SendMail email;

	@Autowired
	PatientServiceImpl patImpl;

	@Autowired
	EmailGroupMembersDAO emailGroupMembersDAO;

	@Autowired
	EmailMessagesDAO emailMessagesDAO;

	@Autowired
	UserService userService;

	@Autowired
	GroupRoleService grpRolService;

	@Autowired
	private GroupUserAssignmentDAO grpUsrDao;

	@Autowired
	private GroupDAO grpDao;

	@Autowired
	private WatchRxCalendarTaskDAO calendarTaskDAO;

	@Autowired
	WatchRxPatientRpmCcmDataDetailsDAO watchRxPatientRpmCcmDataDetailsDAO;

	@Autowired
	private WatchRxCarePlanTabsConfigDAO carePlanTabsConfigDAO;

	@Autowired
	private WatchRxCarePlanQuestionsDAO watchRxCarePlanQuestionsDAO;

	@Autowired
	private CaseManagerPatientTimerDAO caseManagerPatientTimerDAO;

	@Autowired
	private PatientDataProcessor patientDataProcessor;

	@Autowired
	private WatchRxClinicianDeviceDAO watchRxClinicianDeviceDAO;

	SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	SimpleDateFormat formater1 = new SimpleDateFormat("MM-dd-yyyy HH:mm:ss");

	SimpleDateFormat calendarFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	SimpleDateFormat inputFormat = new SimpleDateFormat("MM-dd-yyyy hh:mm a");

	private static final Logger log = Logger.getLogger(ClinicianServiceImpl.class.getName());

	@Override
	public void registerClinician(RegistrationVO clinicianVO) {

		log.info("Inside ClinicianServiceImpl registerClinician:::: ");

		WatchrxClinician watchrxClinician = new WatchrxClinician();
		WatchrxUser watchrxUser = new WatchrxUser();

		watchrxUser.setImgPath(null);
		watchrxUser.setFirstName(clinicianVO.getFirstName());
		watchrxUser.setLastName(clinicianVO.getLastName());
		watchrxUser.setCreatedDate(new java.util.Date());
		watchrxUser.setUpdatedDate(watchrxUser.getCreatedDate());
		watchrxUser.setPassword(clinicianVO.getPassword());
		watchrxUser.setEmail(clinicianVO.getUserName());
		watchrxUser.setUserName(clinicianVO.getUserName());
		watchrxUser.setUserWork(clinicianVO.getUserWork());
		watchrxUser.setUserWorkTYPE(clinicianVO.getUserWorkType());
		watchrxUser.setOtpEnabled(clinicianVO.getOtpEnabled());

		watchrxUser.setStatus(Constants.Status.INACTIVE);
		Integer userType = null;
		switch (clinicianVO.getUserRole().toUpperCase()) {
		case "CAREGIVER":
			userType = Constants.UserType.CAREGIVER;
			break;
		case "PHYSICIAN":
			userType = Constants.UserType.PHYSICIAN;
			break;
		case "CASEMANAGER":
			userType = Constants.UserType.CASEMANAGER;
			break;
		case "ADMIN":
			userType = Constants.UserType.COORDINATOR;
			break;
		}
		watchrxUser.setUserType(userType);
		watchrxUser = userDAO.save(watchrxUser);
		Group grp = new Group();
		grp.setGroupIds(clinicianVO.getGroups());
		grp.setUserId(watchrxUser.getUserId());
		grpRolService.assignUserToGroups(grp);
		if (userType != 2) {
			if (userType == Constants.UserType.PHYSICIAN) {
				WatchRxPhysician watchRxPhysician = new WatchRxPhysician();
				watchRxPhysician.setWatchrxUser(watchrxUser);
				watchRxPhysician.setWatchrxAddress(null);
				watchRxPhysician.setFirstName(clinicianVO.getFirstName());
				watchRxPhysician.setAltPhoneNumber(null);
				watchRxPhysician.setCreatedDate(new Date());
				watchRxPhysician.setStatus(Constants.Status.INACTIVE);
				watchRxPhysician.setLastName(clinicianVO.getLastName());
				watchRxPhysician.setShiftId(Constants.Shift.MORNIG);
				watchRxPhysician.setHospitalName(null);
				watchRxPhysician.setPhoneNumber(clinicianVO.getPhoneNumber());
				watchRxPhysician.setUpdatedDate(watchRxPhysician.getCreatedDate());
				watchRxPhysician = physicianDAO.save(watchRxPhysician);
				log.info("Saved Physician");
			} else {
				watchrxClinician.setWatchrxUser(watchrxUser);
				watchrxClinician.setClinicianType(1);
				watchrxClinician.setWatchrxAddress(null);
				watchrxClinician.setFirstName(clinicianVO.getFirstName());
				watchrxClinician.setAltPhoneNumber(null);
				watchrxClinician.setHospitalName(null);
				watchrxClinician.setLastName(clinicianVO.getLastName());
				watchrxClinician.setPhoneNumber(clinicianVO.getPhoneNumber());
				watchrxClinician.setShiftId(Constants.Shift.MORNIG);
				watchrxClinician.setStatus(Constants.Status.INACTIVE);
				watchrxClinician.setCreatedDate(new Date());
				watchrxClinician.setUpdatedDate(watchrxClinician.getCreatedDate());
				watchrxClinician = clinicianDAO.save(watchrxClinician);
				log.info("Saved Clinician");
			}
		}
	}

	@Override
	public void saveClinician(ClinicianVO clinicianVO) {

		WatchrxClinician watchrxClinician = null;
		WatchrxAddress watchrxAddress = null;
		WatchrxUser watchrxUser = null;
		log.info("Inside ClinicianServiceImpl saveClinician:::: ");

		if (clinicianVO.getClinicianId() != null) {
			watchrxClinician = clinicianDAO.getById(clinicianVO.getClinicianId());
			log.info("Clinician Id::::" + clinicianVO.getClinicianId().toString());
			watchrxAddress = watchrxClinician.getWatchrxAddress();
			watchrxUser = watchrxClinician.getWatchrxUser();
		} else {
			watchrxClinician = new WatchrxClinician();
			watchrxAddress = new WatchrxAddress();
			watchrxUser = new WatchrxUser();
		}
		if (clinicianVO.getAddress() != null) {
			log.info("Address :::" + clinicianVO.getAddress().toString());
			if (watchrxAddress == null) {
				watchrxAddress = new WatchrxAddress();
			}
			watchrxAddress.setAddress1(clinicianVO.getAddress().getAddress1());

			watchrxAddress.setAddress2(clinicianVO.getAddress().getAddress2());

			watchrxAddress.setState(clinicianVO.getAddress().getState());

			watchrxAddress.setCity(clinicianVO.getAddress().getCity());

			watchrxAddress.setZip(clinicianVO.getAddress().getZip());

			watchrxAddress.setCountry(clinicianVO.getAddress().getCountry());

			watchrxAddress = addressDAO.save(watchrxAddress);
		}
		watchrxUser.setUserName(clinicianVO.getUserName());
		watchrxUser.setEmail(clinicianVO.getUserName());
		watchrxUser.setPassword(clinicianVO.getPassword());
		watchrxUser.setUserType(clinicianVO.getRoleType());
		watchrxUser.setCreatedDate(new Date());
		watchrxUser.setUpdatedDate(new Date());
		log.info("Picture path ClinicianService: " + clinicianVO.getPicPath());
		log.info("File Modified CliniciaService: " + clinicianVO.isFileModified());

		if (clinicianVO.isFileModified()) {
			watchrxUser.setImgPath(clinicianVO.getPicPath());
			System.out.println("Image Path:::" + watchrxUser.getImgPath());
		} /// else {
			// watchrxUser.setImgPath("");
			// }
		watchrxUser.setFirstName(clinicianVO.getFirstName());
		watchrxUser.setLastName(clinicianVO.getLastName());
		watchrxUser = userDAO.save(watchrxUser);
		watchrxClinician.setWatchrxUser(watchrxUser);
		watchrxClinician.setClinicianType(clinicianVO.getSpeciality());
		if (watchrxAddress != null) {
			watchrxClinician.setWatchrxAddress(watchrxAddress);
		}
		watchrxClinician.setFirstName(clinicianVO.getFirstName());
		watchrxClinician.setAltPhoneNumber(clinicianVO.getAltPhoneNumber());
		watchrxClinician.setHospitalName(clinicianVO.getHospital());
		watchrxClinician.setLastName(clinicianVO.getLastName());
		watchrxClinician.setPhoneNumber(clinicianVO.getPhoneNumber());

		watchrxClinician.setShiftId(clinicianVO.getShift());
		watchrxClinician.setStatus(Constants.Status.ACTIVE);
		watchrxClinician.setCreatedDate(new Date());
		watchrxClinician.setUpdatedDate(new Date());
		watchrxClinician = clinicianDAO.save(watchrxClinician);

		watchrxClinician = clinicianDAO.getById(watchrxClinician.getClinicianId());
		WatchrxMailingAddress mailingAddress = new WatchrxMailingAddress();
		mailingAddress.setAddress1(clinicianVO.getAddress().getAddress1());
		mailingAddress.setAddress2(clinicianVO.getAddress().getAddress2());
		mailingAddress.setState(clinicianVO.getAddress().getState());
		mailingAddress.setCity(clinicianVO.getAddress().getCity());
		mailingAddress.setZip(clinicianVO.getAddress().getZip());
		mailingAddress.setCountry(clinicianVO.getAddress().getCountry());
		mailingAddress.setPhoneNumber(clinicianVO.getPhoneNumber());
		mailingAddress.setClinician(watchrxClinician);
		mailingAddress.setName(clinicianVO.getFirstName() + " " + clinicianVO.getLastName());

		Set<WatchrxMailingAddress> mailAddressList = watchrxClinician.getWatchrxMailingAddresses();
		if (mailAddressList == null || mailAddressList.size() == 0 || !mailAddressList.contains(mailingAddress)) {
			mailingAddressDAO.save(mailingAddress);
		}

		log.info("Saved Clinician");
	}

	@Override
	public List<ClinicianVO> getClinicianList() {
		List<WatchrxClinician> clinicians = clinicianDAO.getAll();
		List<ClinicianVO> clinicianList = new ArrayList<ClinicianVO>();
		Map<Integer, String> refData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.CLINICIAN_TYPE);
		Map<Integer, String> shiftRefData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.SHIFT);
		for (WatchrxClinician watchrxClinician : clinicians) {
			ClinicianVO clinicianVO = getClinicianVO(watchrxClinician);
			clinicianVO.setSpecialityName(refData.get(clinicianVO.getSpeciality()));
			clinicianVO.setShiftName(shiftRefData.get(clinicianVO.getShift()));
			clinicianList.add(clinicianVO);
		}
		// TODO Auto-generated method stub
		return clinicianList;
	}

	@Override
	public List<ClinicianVO> getCaregiverInfoByuser(Long caregiverId) {
		List<WatchrxClinician> clinicians = new ArrayList<WatchrxClinician>();
		clinicians.add(clinicianDAO.getById(caregiverId));
		List<ClinicianVO> clinicianList = new ArrayList<ClinicianVO>();
		Map<Integer, String> refData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.CLINICIAN_TYPE);
		Map<Integer, String> shiftRefData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.SHIFT);
		for (WatchrxClinician watchrxClinician : clinicians) {
			ClinicianVO clinicianVO = getClinicianVO(watchrxClinician);
			clinicianVO.setSpecialityName(refData.get(clinicianVO.getSpeciality()));
			clinicianVO.setShiftName(shiftRefData.get(clinicianVO.getShift()));
			clinicianList.add(clinicianVO);
		}
		// TODO Auto-generated method stub
		return clinicianList;
	}

	@Override
	public List<ClinicianWithAssociatedWatchesAndPatientsVO> getClinicianListWithInActiveByClinicianId(
			Long clinicianId) {

		List<WatchrxClinician> clinicians = clinicianDAO.findByProperty("clinicianId", clinicianId);
		List<ClinicianWithAssociatedWatchesAndPatientsVO> clinicianList = new ArrayList<ClinicianWithAssociatedWatchesAndPatientsVO>();
		Map<Integer, String> refData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.CLINICIAN_TYPE);
		Map<Integer, String> shiftRefData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.SHIFT);
		for (WatchrxClinician watchrxClinician : clinicians) {
			ClinicianWithAssociatedWatchesAndPatientsVO clinicianVO = getClinicianWithAssociatedWatchesAndPatientsVO(
					watchrxClinician);
			clinicianVO.setSpecialityName(refData.get(clinicianVO.getSpeciality()));
			clinicianVO.setShiftName(shiftRefData.get(clinicianVO.getShift()));

			clinicianVO.setPatientsWithWatchesHavingSecondaryRelToClinician(new ArrayList<PatientDetailsVO>());
			clinicianVO.setPatientsWithWatchesHavingPrimaryRelToClinician(new ArrayList<PatientDetailsVO>());
			clinicianVO.setAllWatches(new ArrayList<WatchDetailsVO>());// new
																		// ArrayList<WatchDetailsVO>()

			Set<WatchrxClinicianWatchAssignmnt> watchrxClinicianWatchAssignments = watchrxClinician
					.getWatchrxClinicianWatchAssignmnts();
			for (WatchrxClinicianWatchAssignmnt watchrxClinicianWatchAssignmnt : watchrxClinicianWatchAssignments) {
				WatchrxWatch watch = watchrxClinicianWatchAssignmnt.getWatchrxWatch();
				WatchDetailsVO watchDet = new WatchDetailsVO();
				watchDet.setClinician(watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName());
				watchDet.setImeiNumber(watch.getWatchImeiNumber());
				watchDet.setIsActive(watch.getIsWatchActive());
				if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship() == null
						|| watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("P")) {
					watchDet.setIsPrimary(true);
				} else if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("S")) {
					watchDet.setIsPrimary(false);
				}
				watchDet.setPhoneNumber(watch.getWatchPhoneNumber());
				watchDet.setWatchId(watch.getWatchId());
				for (WatchrxPatientWatchAssignmnt watchrxPatientWatchAssignmnt : watch
						.getWatchrxPatientWatchAssignmnts()) {
					PatientDetailsVO patientDetailsVO = new PatientDetailsVO();
					watchDet.setPatient(watchrxPatientWatchAssignmnt.getWatchrxPatient().getFirstName() + " "
							+ watchrxPatientWatchAssignmnt.getWatchrxPatient().getLastName());
					patientDetailsVO.setAddress(watchrxPatientWatchAssignmnt.getWatchrxPatient().getWatchrxAddress()
							.getAddressCollectionAsString());
					patientDetailsVO
							.setAltPhoneNumber(watchrxPatientWatchAssignmnt.getWatchrxPatient().getAltPhoneNum());
					patientDetailsVO
							.setClinicianName(watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName());
					patientDetailsVO.setFirstName(watchrxPatientWatchAssignmnt.getWatchrxPatient().getFirstName());
					patientDetailsVO.setLastName(watchrxPatientWatchAssignmnt.getWatchrxPatient().getLastName());
					patientDetailsVO.setPatientId(watchrxPatientWatchAssignmnt.getWatchrxPatient().getPatientId());
					patientDetailsVO.setPhoneNumber(watchrxPatientWatchAssignmnt.getWatchrxPatient().getPhoneNumber());
					patientDetailsVO.setWatchImei(watch.getWatchImeiNumber());
					patientDetailsVO.setWatchNumber(watch.getWatchPhoneNumber());
					if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship() == null
							|| watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("P")) {
						clinicianVO.getPatientsWithWatchesHavingPrimaryRelToClinician().add(patientDetailsVO);
					} else if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("S")) {
						clinicianVO.getPatientsWithWatchesHavingSecondaryRelToClinician().add(patientDetailsVO);
					}
				}

				if (watch.getWatchImeiNumber() != null) {
					List<WatchrxWatchBuyingAllocation> buyingAllocations = buyingAllocationDAO
							.findByProperty("watchAllocatedByAdmin.watchImeiNumber", watch.getWatchImeiNumber());
					for (WatchrxWatchBuyingAllocation buyingAllocation : buyingAllocations) {

						WatchrxStorePlanSubscription subscription = buyingAllocation.getSubscription();
						if (subscription != null) {
							watchDet.setSubscribedPlanName(subscription.getPlanOrder().getPlan().getStripeName());
							watchDet.setPlanActiveUntil(Long.valueOf(subscription.getValidUntil().getTime()));
							watchDet.setSubscribedPlanState(subscription.getState());
						}
					}
				}

				clinicianVO.getAllWatches().add(watchDet);

			}

			clinicianList.add(clinicianVO);
		}
		// TODO Auto-generated method stub
		return clinicianList;

	}

	@Override
	public List<ClinicianWithAssociatedWatchesAndPatientsVO> changeWatchStatusByWatchId(Long clinicianId, Long watchId,
			Boolean watchState) throws WatchNotFoundException, ClinicianNotFoundException {

		WatchrxWatch watchToModify = watchDAO.getById(watchId);
		WatchrxClinician clinicianOwingWatch = clinicianDAO.getById(clinicianId);

		if (clinicianOwingWatch == null) {
			throw new ClinicianNotFoundException("Clinician with id " + clinicianId + " not found");
		}

		if (watchToModify != null) {
			watchToModify.setIsWatchActive(watchState);
			watchToModify = watchDAO.save(watchToModify);
			watchToModify = watchDAO.getById(watchToModify.getWatchId());

			CareGiverTest sdetails = new CareGiverTest();
			Gson gson = new Gson();
			JsonObject jo = new JsonObject();
			String patGCMid = null;

			List<WatchrxPatientWatchAssignmnt> assignments = patientWatchAssignmentDAO
					.findByProperty("watchrxWatch.watchId", watchToModify.getWatchId());
			if (assignments != null && assignments.size() > 0) {
				WatchrxPatient patient = assignments.get(0).getWatchrxPatient();
				patGCMid = patient.getGcmRegistrationId();

				try {
					if (patGCMid != null) {
						if (!watchState) {
							jo.addProperty("messageType", "WatchInActive");
						} else {
							jo.addProperty("messageType", "WatchActive");
						}
						String jsonStr = gson.toJson(jo);
						sdetails.sendMessageToPatient("message", jsonStr, patGCMid, patient.getPlatform());
					}
				} catch (IOException ex) {
					log.info("Unable to send gcm");
					log.info(ex.getMessage());
				} catch (IllegalArgumentException ex) {
					log.info("Unable to send gcm");
					log.info(ex.getMessage());
				}

			}

		} else {
			throw new WatchNotFoundException("Watch with id " + watchId + " not found");
		}

		List<WatchrxClinician> clinicians = clinicianDAO.findByProperty("clinicianId", clinicianId);
		List<ClinicianWithAssociatedWatchesAndPatientsVO> clinicianList = new ArrayList<ClinicianWithAssociatedWatchesAndPatientsVO>();
		Map<Integer, String> refData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.CLINICIAN_TYPE);
		Map<Integer, String> shiftRefData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.SHIFT);
		for (WatchrxClinician watchrxClinician : clinicians) {
			ClinicianWithAssociatedWatchesAndPatientsVO clinicianVO = getClinicianWithAssociatedWatchesAndPatientsVO(
					watchrxClinician);
			clinicianVO.setSpecialityName(refData.get(clinicianVO.getSpeciality()));
			clinicianVO.setShiftName(shiftRefData.get(clinicianVO.getShift()));

			clinicianVO.setPatientsWithWatchesHavingSecondaryRelToClinician(new ArrayList<PatientDetailsVO>());
			clinicianVO.setPatientsWithWatchesHavingPrimaryRelToClinician(new ArrayList<PatientDetailsVO>());
			clinicianVO.setAllWatches(new ArrayList<WatchDetailsVO>());// new
																		// ArrayList<WatchDetailsVO>()

			Set<WatchrxClinicianWatchAssignmnt> watchrxClinicianWatchAssignments = watchrxClinician
					.getWatchrxClinicianWatchAssignmnts();
			for (WatchrxClinicianWatchAssignmnt watchrxClinicianWatchAssignmnt : watchrxClinicianWatchAssignments) {
				WatchrxWatch watch = watchrxClinicianWatchAssignmnt.getWatchrxWatch();
				WatchDetailsVO watchDet = new WatchDetailsVO();
				watchDet.setClinician(watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName());
				watchDet.setImeiNumber(watch.getWatchImeiNumber());
				watchDet.setIsActive(watch.getIsWatchActive());
				if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship() == null
						|| watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("P")) {
					watchDet.setIsPrimary(true);
				} else if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("S")) {
					watchDet.setIsPrimary(false);
				}
				watchDet.setPhoneNumber(watch.getWatchPhoneNumber());
				watchDet.setWatchId(watch.getWatchId());
				for (WatchrxPatientWatchAssignmnt watchrxPatientWatchAssignmnt : watch
						.getWatchrxPatientWatchAssignmnts()) {
					PatientDetailsVO patientDetailsVO = new PatientDetailsVO();
					watchDet.setPatient(watchrxPatientWatchAssignmnt.getWatchrxPatient().getFirstName() + " "
							+ watchrxPatientWatchAssignmnt.getWatchrxPatient().getLastName());
					patientDetailsVO.setAddress(watchrxPatientWatchAssignmnt.getWatchrxPatient().getWatchrxAddress()
							.getAddressCollectionAsString());
					patientDetailsVO
							.setAltPhoneNumber(watchrxPatientWatchAssignmnt.getWatchrxPatient().getAltPhoneNum());
					patientDetailsVO
							.setClinicianName(watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName());
					patientDetailsVO.setFirstName(watchrxPatientWatchAssignmnt.getWatchrxPatient().getFirstName());
					patientDetailsVO.setLastName(watchrxPatientWatchAssignmnt.getWatchrxPatient().getLastName());
					patientDetailsVO.setPatientId(watchrxPatientWatchAssignmnt.getWatchrxPatient().getPatientId());
					patientDetailsVO.setPhoneNumber(watchrxPatientWatchAssignmnt.getWatchrxPatient().getPhoneNumber());
					patientDetailsVO.setWatchImei(watch.getWatchImeiNumber());
					patientDetailsVO.setWatchNumber(watch.getWatchPhoneNumber());
					if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship() == null
							|| watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("P")) {
						clinicianVO.getPatientsWithWatchesHavingPrimaryRelToClinician().add(patientDetailsVO);
					} else if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("S")) {
						clinicianVO.getPatientsWithWatchesHavingSecondaryRelToClinician().add(patientDetailsVO);
					}
				}

				if (watch.getWatchImeiNumber() != null) {
					List<WatchrxWatchBuyingAllocation> buyingAllocations = buyingAllocationDAO
							.findByProperty("watchAllocatedByAdmin.watchImeiNumber", watch.getWatchImeiNumber());
					for (WatchrxWatchBuyingAllocation buyingAllocation : buyingAllocations) {

						WatchrxStorePlanSubscription subscription = buyingAllocation.getSubscription();
						if (subscription != null) {
							watchDet.setSubscribedPlanName(subscription.getPlanOrder().getPlan().getStripeName());
							watchDet.setPlanActiveUntil(Long.valueOf(subscription.getValidUntil().getTime()));
							watchDet.setSubscribedPlanState(subscription.getState());
						}
					}
				}

				clinicianVO.getAllWatches().add(watchDet);

			}

			clinicianList.add(clinicianVO);
		}
		// TODO Auto-generated method stub
		return clinicianList;

	}

	@Override
	public List<ClinicianWithAssociatedWatchesAndPatientsVO> getClinicianListWithInActive() {
		List<WatchrxClinician> clinicians = clinicianDAO.getAllWithInActive();
		List<ClinicianWithAssociatedWatchesAndPatientsVO> clinicianList = new ArrayList<ClinicianWithAssociatedWatchesAndPatientsVO>();
		Map<Integer, String> refData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.CLINICIAN_TYPE);
		Map<Integer, String> shiftRefData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.SHIFT);
		for (WatchrxClinician watchrxClinician : clinicians) {
			ClinicianWithAssociatedWatchesAndPatientsVO clinicianVO = getClinicianWithAssociatedWatchesAndPatientsVO(
					watchrxClinician);
			clinicianVO.setSpecialityName(refData.get(clinicianVO.getSpeciality()));
			clinicianVO.setShiftName(shiftRefData.get(clinicianVO.getShift()));

			clinicianVO.setPatientsWithWatchesHavingSecondaryRelToClinician(new ArrayList<PatientDetailsVO>());
			clinicianVO.setPatientsWithWatchesHavingPrimaryRelToClinician(new ArrayList<PatientDetailsVO>());
			clinicianVO.setAllWatches(new ArrayList<WatchDetailsVO>());// new
																		// ArrayList<WatchDetailsVO>()

//			Set<WatchrxClinicianWatchAssignmnt> watchrxClinicianWatchAssignments = watchrxClinician
//					.getWatchrxClinicianWatchAssignmnts();
//			for (WatchrxClinicianWatchAssignmnt watchrxClinicianWatchAssignmnt : watchrxClinicianWatchAssignments) {
//				WatchrxWatch watch = watchrxClinicianWatchAssignmnt.getWatchrxWatch();
//				WatchDetailsVO watchDet = new WatchDetailsVO();
//				watchDet.setClinician(watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName());
//				watchDet.setImeiNumber(watch.getWatchImeiNumber());
//				watchDet.setIsActive(watch.getIsWatchActive());
//				if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship() == null
//						|| watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("P"))
//					watchDet.setIsPrimary(true);
//				else if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("S"))
//					watchDet.setIsPrimary(false);
//				watchDet.setPhoneNumber(watch.getWatchPhoneNumber());
//				watchDet.setWatchId(watch.getWatchId());
//				for (WatchrxPatientWatchAssignmnt watchrxPatientWatchAssignmnt : watch
//						.getWatchrxPatientWatchAssignmnts()) {
//					PatientDetailsVO patientDetailsVO = new PatientDetailsVO();
//					watchDet.setPatient(watchrxPatientWatchAssignmnt.getWatchrxPatient().getFirstName() + " "
//							+ watchrxPatientWatchAssignmnt.getWatchrxPatient().getLastName());
//					patientDetailsVO.setAddress(watchrxPatientWatchAssignmnt.getWatchrxPatient().getWatchrxAddress()
//							.getAddressCollectionAsString());
//					patientDetailsVO
//							.setAltPhoneNumber(watchrxPatientWatchAssignmnt.getWatchrxPatient().getAltPhoneNum());
//					patientDetailsVO
//							.setClinicianName(watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName());
//					patientDetailsVO.setFirstName(watchrxPatientWatchAssignmnt.getWatchrxPatient().getFirstName());
//					patientDetailsVO.setLastName(watchrxPatientWatchAssignmnt.getWatchrxPatient().getLastName());
//					patientDetailsVO.setPatientId(watchrxPatientWatchAssignmnt.getWatchrxPatient().getPatientId());
//					patientDetailsVO.setPhoneNumber(watchrxPatientWatchAssignmnt.getWatchrxPatient().getPhoneNumber());
//					patientDetailsVO.setWatchImei(watch.getWatchImeiNumber());
//					patientDetailsVO.setWatchNumber(watch.getWatchPhoneNumber());
//					if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship() == null
//							|| watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("P"))
//						clinicianVO.getPatientsWithWatchesHavingPrimaryRelToClinician().add(patientDetailsVO);
//					else if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("S"))
//						clinicianVO.getPatientsWithWatchesHavingSecondaryRelToClinician().add(patientDetailsVO);
//				}
//				clinicianVO.getAllWatches().add(watchDet);
//
//			}

			clinicianList.add(clinicianVO);
		}
		// TODO Auto-generated method stub
		return clinicianList;
	}

	@Override
	public ClinicianVO getClinician(Long clinicianId, Long roleType) {

		if (roleType == 5 || roleType == 4) {
			WatchrxClinician watchrxClinician = clinicianDAO.getById(clinicianId);
			ClinicianVO clinicianVO = getClinicianVO(watchrxClinician);
			return clinicianVO;
		} else if (roleType == 3) {
			WatchRxPhysician physician = physicianDAO.getById(clinicianId);
			ClinicianVO clinicianVO = getPhysicianVO(physician);
			return clinicianVO;
		}
		return null;

	}

	@Override
	public ClinicianVO getClinicianByUserName(String userName) {
		ClinicianVO clinicianVO = null;
		// TODO Auto-generated method stub
		List<WatchrxClinician> watchrxClinicianList = clinicianDAO.findByProperty("watchrxUser.userName", userName);
		if (watchrxClinicianList != null && watchrxClinicianList.size() > 0) {
			WatchrxClinician watchrxClinician = watchrxClinicianList.get(0);
			clinicianVO = getClinicianVO(watchrxClinician);
			clinicianVO.setPicPath(watchrxClinician.getWatchrxUser().getImgPath());
		}
		return clinicianVO;
	}

	@Override
	public void resetPasswordByUserName(Login loginDetails) {
		System.out.println("Inside ResetPasswordByUserName");
		List<WatchrxUser> watchrxClinicianList = userDAO.findByProperty("userName", loginDetails.getLoginId());
		if (watchrxClinicianList != null && watchrxClinicianList.size() > 0) {
			WatchrxUser user = watchrxClinicianList.get(0);
			user.setPassword(loginDetails.getPassword());
			user.setPasswordChangedTime(new Date());
			userDAO.save(user);
		} else {
			System.out.println("USER List is empty,, checking patient");
			List<WatchrxPatient> watchrxPatientList = patientDAO.findByProperty("email", loginDetails.getLoginId());
			if (watchrxPatientList != null && watchrxPatientList.size() > 0) {
				WatchrxPatient user = watchrxPatientList.get(0);
				user.setPassword(loginDetails.getPassword());
				user.setPasswordChangedTime(new Date());
				patientDAO.save(user);
			}
		}
	}

	@Override
	public String deleteClinician(Long clinicianId) {
		String msg = "";
		try {
			WatchrxClinician watchrxClinician = clinicianDAO.getById(clinicianId);

			List<WatchrxPatient> patients = patientDAO.findByProperty("watchrxClinician.clinicianId", clinicianId);
			if (!patients.isEmpty()) {
				msg = "Please unsassign patient.";
			}
			patientClinicianAssignmentDAO.deleteByProperty("watchrxClinician.clinicianId", clinicianId);
			patientCareGiverAssignmentDAO.deleteByProperty("watchrxClinician.clinicianId", clinicianId);
			clinicianScheduleDAO.deleteByProperty("watchrxClinician.clinicianId", clinicianId);
			clinicianWatchAssignmentDAO.deleteByProperty("watchrxClinician.clinicianId", clinicianId);
			physicianCaseManagerAssignmentDAO.deleteByProperty("watchrxClinician.clinicianId", clinicianId);
			mailingAddressDAO.deleteByProperty("clinician.clinicianId", clinicianId);
			emailGroupMembersDAO.deleteByProperty("watchrxUser.userId", clinicianId);
			buyingAllocationDAO.deleteByProperty("caregiver.clinicianId", clinicianId);

			// patientDAO.deleteByProperty("watchrxClinician.clinicianId", clinicianId);

			for (WatchrxPatient pat : patients) {
				patImpl.deletePatient(pat.getPatientId());
			}
			grpUsrDao.deleteByProperty("watchrxUser.userName", watchrxClinician.getWatchrxUser().getUserName());
			clinicianDAO.delete(clinicianId);
			if (watchrxClinician.getWatchrxAddress() != null) {
				addressDAO.delete(watchrxClinician.getWatchrxAddress().getAddressId());
			}
			emailGroupMembersDAO.deleteByProperty("watchrxUser.userId", watchrxClinician.getWatchrxUser().getUserId());
			emailMessagesDAO.deleteByProperty("sender.userId", watchrxClinician.getWatchrxUser().getUserId());
			emailReceiversDAO.deleteByProperty("receiver.userId", watchrxClinician.getWatchrxUser().getUserId());
			userDAO.delete(watchrxClinician.getWatchrxUser().getUserId());
		} catch (Exception e) {
			try {
				WatchrxUser watchrxClinician = clinicianDAO.getById(clinicianId).getWatchrxUser();
				watchrxClinician.setUserType(null);
				watchrxClinician.setUserName("invali@!d");
				watchrxClinician.setPassword("invali@!d");
				watchrxClinician.setStatus("N");
				userDAO.save(watchrxClinician);
			} catch (Exception e1) {
				e1.printStackTrace();
			}
		}
		return msg;
	}

	@Override
	public void deletePhysician(Long physicianId) {
		WatchRxPhysician physician = physicianDAO.getById(physicianId);
		List<WatchrxPatient> patients = patientDAO.findByProperty("watchrxPhysician.physicianId", physicianId);
		for (WatchrxPatient pat : patients) {
			patImpl.deletePatient(pat.getPatientId());
		}
		physicianCaseManagerAssignmentDAO.deleteByProperty("watchrxPhysician.physicianId", physicianId);
		physicianDAO.delete(physicianId);
		if (physician.getWatchrxAddress() != null) {
			addressDAO.delete(physician.getWatchrxAddress().getAddressId());
		}

		emailGroupMembersDAO.deleteByProperty("watchrxUser.userId", physician.getWatchrxUser().getUserId());
		emailMessagesDAO.deleteByProperty("sender.userId", physician.getWatchrxUser().getUserId());
		emailReceiversDAO.deleteByProperty("receiver.userId", physician.getWatchrxUser().getUserId());
		encountersNewDAO.deleteByProperty("watchrxUser.userId", physician.getWatchrxUser().getUserId());
		billingDao.deleteByProperty("watchrxPhysician.physicianId", physicianId);
		userDAO.delete(physician.getWatchrxUser().getUserId());
	}

	@Override
	@Transactional
	public ClinicianVO changeClinicianStatus(Long clinicianId) {

		WatchrxClinician watchrxClinician = clinicianDAO.getById(clinicianId);
		WatchrxUser usr = watchrxClinician.getWatchrxUser();
		if (usr != null) {
			if (usr.getStatus().equals("N")) {
				usr.setStatus("Y");
			} else {
				usr.setStatus("N");
			}
			usr.setLoggedInDateTime(new Date());
			usr.setLoggedOutDateTime(new Date());
			usr.setInvalidLoginAttempt(0);
			userDAO.save(usr);
		}
		if (watchrxClinician.getStatus().toUpperCase().equals("Y")) {
			watchrxClinician.setStatus("Y");
			watchrxClinician.getWatchrxUser().setStatus("N");
		} else if (watchrxClinician.getStatus().toUpperCase().equals("N")) {
			watchrxClinician.setStatus("Y");
			watchrxClinician.getWatchrxUser().setStatus("Y");
			String adminUsersEmail = userService.getUserById(Constants.Admin.ADMINID).getEmail();
			String body = "Hi " + watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName()
					+ ", Your account has been activated.<NAME_EMAIL> for any discrepencies observed.";
			String adminEmailBody = "Hi " + watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName()
					+ ", User account has been activated.";
			try {
				email.sendEmail(Constants.Email.ADMINEMAIL, watchrxClinician.getWatchrxUser().getUserName(),
						watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName(),
						"WatchRX Account Creation", body);
				email.sendEmail(Constants.Email.ADMINEMAIL, adminUsersEmail,
						watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName(),
						"WatchRX Account Creation", adminEmailBody);
			} catch (AddressException e) {
				e.printStackTrace();
			} catch (MessagingException e) {
				e.printStackTrace();
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
		} else {
			watchrxClinician.setStatus("N");
		}
		ClinicianVO clinicianVO = getClinicianVO(watchrxClinician);
		watchrxClinician = clinicianDAO.save(watchrxClinician);
		clinicianVO.setStatus(watchrxClinician.getStatus());
		return clinicianVO;

	}

	@Override
	@Transactional
	public ClinicianVO changeAdminStatus(Long clinicianId) {

		WatchrxUser usr = userDAO.getById(clinicianId);
		if (usr != null) {
			if (usr.getStatus().equals("N")) {
				usr.setStatus("Y");
			} else {
				usr.setStatus("N");
			}
			usr.setInvalidLoginAttempt(0);
			usr.setLoggedInDateTime(new Date());
			usr.setLoggedOutDateTime(new Date());
			userDAO.save(usr);
		}
		return new ClinicianVO();

	}

	@Override
	public String getCaregiverRelationship(Long clinicianId, Long watchId) {

		String caregiverRelationship = new String();
		List<WatchrxClinicianWatchAssignmnt> clinicianList = clinicianWatchAssignmentDAO
				.findByProperty("watchrxWatch.watchId", watchId);
		ClinicianVO clinician = getClinician(clinicianId, 5L);
		String name = clinician.getFirstName();
		System.out.println("Clinician Name: " + name);
		System.out.println("Watch: " + watchId);
		for (WatchrxClinicianWatchAssignmnt assgndClinician : clinicianList) {
			WatchrxClinician tmpClinician = assgndClinician.getWatchrxClinician();
			System.out.println("AssigndClinician Name: " + tmpClinician.getFirstName());
			System.out.println("AssigndWatchId: " + assgndClinician.getWatchrxWatch().getWatchId());
			if (tmpClinician.getFirstName().equalsIgnoreCase(name)) {
				System.out.println("Caregiver Relationship: " + assgndClinician.getclinicianWatchRelationship());
				caregiverRelationship = assgndClinician.getclinicianWatchRelationship();
			}
			if (caregiverRelationship == null || caregiverRelationship == "") {
				caregiverRelationship = Constants.CaregiverRole.PRIMARY;
				System.out.println("Caregiver Relationship: " + assgndClinician.getclinicianWatchRelationship());

			}
		}

		return caregiverRelationship;
	}

	private ClinicianVO getPhysicianVO(WatchRxPhysician watchrxClinician) {
		ClinicianVO clinicianVO = new ClinicianVO();
		clinicianVO.setAltPhoneNumber(
				watchrxClinician.getAltPhoneNumber() != null ? watchrxClinician.getAltPhoneNumber() : "");
		clinicianVO.setCreatedDate(watchrxClinician.getCreatedDate());
		clinicianVO.setClinicianId(watchrxClinician.getPhysicianId());
		clinicianVO.setFirstName(watchrxClinician.getFirstName());
		clinicianVO.setLastName(watchrxClinician.getLastName());
		clinicianVO.setName(watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName());
		if (watchrxClinician.getWatchrxUser() != null && watchrxClinician.getWatchrxUser().getImgPath() != null) {
			clinicianVO.setPicPath(WatchRxUtils.readTextFileOnly(watchrxClinician.getWatchrxUser().getImgPath()));
		} else {
			clinicianVO.setPicPath(null);
		}
		if (watchrxClinician.getPhoneNumber() != null) {
			clinicianVO.setPhoneNumber(watchrxClinician.getPhoneNumber());
		} else {
			clinicianVO.setPhoneNumber("Not available");
		}
		clinicianVO.setUpdatedDate(watchrxClinician.getUpdatedDate());
		if (watchrxClinician.getWatchrxAddress() != null) {
			clinicianVO.setAddress(getAddress(watchrxClinician.getWatchrxAddress()));
		} else {
			clinicianVO.setAddress(getEmptyAddress());
		}
		if (watchrxClinician.getWatchrxAddress() != null) {
			System.out.print("Country in WatchrxAddress: " + watchrxClinician.getWatchrxAddress().getCountry());
		}
		clinicianVO.setUserName(watchrxClinician.getWatchrxUser().getUserName());
		clinicianVO.setPassword(watchrxClinician.getWatchrxUser().getPassword());
		clinicianVO.setRoleType(watchrxClinician.getWatchrxUser().getUserType());
		clinicianVO.setStatus(watchrxClinician.getStatus());
		clinicianVO.setPlatformType(watchrxClinician.getPlatformType());
		return clinicianVO;
	}

	private ClinicianVO getClinicianVO(WatchrxClinician watchrxClinician) {
		ClinicianVO clinicianVO = new ClinicianVO();
		clinicianVO.setAltPhoneNumber(watchrxClinician.getAltPhoneNumber());
		clinicianVO.setCreatedDate(watchrxClinician.getCreatedDate());
		clinicianVO.setClinicianId(watchrxClinician.getClinicianId());
		clinicianVO.setSpeciality(watchrxClinician.getClinicianType());
		clinicianVO.setFirstName(watchrxClinician.getFirstName());
		clinicianVO.setLastName(watchrxClinician.getLastName());
		clinicianVO.setName(watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName());
		clinicianVO.setHospital(watchrxClinician.getHospitalName());
		clinicianVO.setPasswordExpired(watchrxClinician.getWatchrxUser().isPasswordExpired());
		if (watchrxClinician.getWatchrxUser() != null && watchrxClinician.getWatchrxUser().getImgPath() != null) {
			clinicianVO.setPicPath(WatchRxUtils.readTextFileOnly(watchrxClinician.getWatchrxUser().getImgPath()));
		} else {
			clinicianVO.setPicPath(null);
		}
		if (watchrxClinician.getPhoneNumber() != null) {
			clinicianVO.setPhoneNumber(watchrxClinician.getPhoneNumber());
		} else {
			clinicianVO.setPhoneNumber("Not available");
		}
		clinicianVO.setShift(watchrxClinician.getShiftId());
		clinicianVO.setUpdatedDate(watchrxClinician.getUpdatedDate());
		if (watchrxClinician.getWatchrxAddress() != null) {
			clinicianVO.setAddress(getAddress(watchrxClinician.getWatchrxAddress()));
		} else {
			clinicianVO.setAddress(getEmptyAddress());
		}
		if (watchrxClinician.getWatchrxAddress() != null) {
			System.out.print("Country in WatchrxAddress: " + watchrxClinician.getWatchrxAddress().getCountry());
		}
		clinicianVO.setUserName(watchrxClinician.getWatchrxUser().getUserName());
		clinicianVO.setPassword(watchrxClinician.getWatchrxUser().getPassword());
		clinicianVO.setRoleType(watchrxClinician.getWatchrxUser().getUserType());
		clinicianVO.setStatus(watchrxClinician.getStatus());
		clinicianVO.setStatus(watchrxClinician.getStatus());
		clinicianVO.setPlatformType(watchrxClinician.getPlatformType());
		clinicianVO.setIsDisplayReminder(watchrxClinician.getIsDisplayReminder());
		clinicianVO.setUserStatus(watchrxClinician.getWatchrxUser().getStatus());
		System.out.println("clinician::::::" + clinicianVO.toString());
		return clinicianVO;
	}

	private ClinicianWithAssociatedWatchesAndPatientsVO getClinicianWithAssociatedWatchesAndPatientsVO(
			WatchrxClinician watchrxClinician) {
		ClinicianWithAssociatedWatchesAndPatientsVO clinicianVO = new ClinicianWithAssociatedWatchesAndPatientsVO();
		clinicianVO.setAltPhoneNumber(watchrxClinician.getAltPhoneNumber());
		clinicianVO.setCreatedDate(watchrxClinician.getCreatedDate());
		clinicianVO.setClinicianId(watchrxClinician.getClinicianId());
		clinicianVO.setSpeciality(watchrxClinician.getClinicianType());
		clinicianVO.setFirstName(watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName());
		clinicianVO.setLastName(watchrxClinician.getLastName());
		clinicianVO.setHospital(watchrxClinician.getHospitalName());
		clinicianVO.setPicPath(WatchRxUtils.readTextFileOnly(watchrxClinician.getWatchrxUser().getImgPath()));
		clinicianVO.setPhoneNumber(watchrxClinician.getPhoneNumber());
		clinicianVO.setShift(watchrxClinician.getShiftId());
		clinicianVO.setUpdatedDate(watchrxClinician.getUpdatedDate());
		if (watchrxClinician.getWatchrxAddress() != null) {
			clinicianVO.setAddress(getAddress(watchrxClinician.getWatchrxAddress()));
		} else {
			clinicianVO.setAddress(null);
		}
		clinicianVO.setUserName(watchrxClinician.getWatchrxUser().getUserName());
		clinicianVO.setPassword(watchrxClinician.getWatchrxUser().getPassword());
		clinicianVO.setRoleType(watchrxClinician.getWatchrxUser().getUserType());
		clinicianVO.setStatus(watchrxClinician.getStatus());
		if (watchrxClinician.getWatchrxAddress() != null) {
			clinicianVO.setFullAddress(watchrxClinician.getWatchrxAddress().getAddressCollectionAsString());
		} else {
			clinicianVO.setFullAddress(null);
		}

		System.out.println("doctor::::::" + clinicianVO.toString());
		return clinicianVO;
	}

	private AddressVO getAddress(WatchrxAddress addressVO) {

		if (addressVO != null) {
			AddressVO address = new AddressVO();
			address.setAddress1(addressVO.getAddress1());
			address.setAddress2(addressVO.getAddress2());
			address.setAddressId(addressVO.getAddressId());
			address.setCity(addressVO.getCity());
			address.setState(addressVO.getState());
			address.setZip(addressVO.getZip());
			address.setCountry(addressVO.getCountry());
			return address;
		} else {
			return null;
		}

	}

	private AddressVO getEmptyAddress() {

		AddressVO address = new AddressVO();
		address.setAddress1("Address Not Available");
		address.setAddress2(" ");
		// address.setAddressId("");
		address.setCity(" ");
		address.setState(" ");
		address.setZip(" ");
		address.setCountry(" ");
		return address;

	}

	@Override
	public List<ClinicianVO> getClinicianList(int shiftId) {

		List<WatchrxClinician> clinicians = clinicianDAO.findByProperty("shiftId", shiftId);
		List<ClinicianVO> clinicianList = new ArrayList<ClinicianVO>();
		for (WatchrxClinician watchrxClinician : clinicians) {
			clinicianList.add(getClinicianVO(watchrxClinician));
		}
		// TODO Auto-generated method stub
		return clinicianList;

	}

	@Override
	public void saveGCMRegID(String gcmId, Long userId, String platfromType) {
		List<WatchRxClinicianDevice> existingDevices = watchRxClinicianDeviceDAO
				.findByUserIdAndGcmRegistrationId(userId, gcmId);
		if (existingDevices == null || existingDevices.isEmpty()) {
			WatchRxClinicianDevice newDevice = new WatchRxClinicianDevice();
			newDevice.setClinicianId(userId);
			newDevice.setGcmRegistrationId(gcmId);
			newDevice.setPlatformType(platfromType);
			watchRxClinicianDeviceDAO.save(newDevice);
		}

//		WatchrxClinician watchrxClinician = clinicianDAO.getById(careGiverId);
		List<WatchrxClinician> watchrxClinicians = clinicianDAO.findByProperty("watchrxUser.userId", userId);
		if (watchrxClinicians != null && watchrxClinicians.size() > 0) {
			WatchrxClinician watchrxClinician = watchrxClinicians.get(0);
			watchrxClinician.setGcmRegistrationId(gcmId);
			watchrxClinician.setPlatformType(platfromType);
			clinicianDAO.save(watchrxClinician);
		}

	}

	@Override
	public String getGCMRegID(Long careGiverId) {
		WatchrxClinician watchrxClinician = clinicianDAO.getById(careGiverId);
		return watchrxClinician.getGcmRegistrationId();
	}

	@Override
	public String getClinicianPlatformTypeByPatientId(Long patientId) {

		List<WatchrxPatientClinicianAssignmnt> patientClinicianList = patientClinicianAssignmentDAO
				.findByProperty("watchrxPatient.patientId", patientId);
		String platformType = null;
		try {
			// Date currentTime = sdf.parse(sdf.format(new Date()));

			for (WatchrxPatientClinicianAssignmnt watchrxPatientClinicianAssignmnt : patientClinicianList) {

				platformType = watchrxPatientClinicianAssignmnt.getWatchrxClinician().getPlatformType();
				log.info("platform id " + platformType);
				return platformType;

			}
		} catch (Exception e) {
		}

		return platformType;
	}

	@Override
	public List<ClinicianGCMVO> getAllClinicianByPatientId(Long patientId) {
		List<WatchrxClinician> patientClinicianList = patientClinicianAssignmentDAO
				.getClinicianGCMIdByPatientId(patientId);
		List<ClinicianGCMVO> nurseGcms = new ArrayList<ClinicianGCMVO>();
		for (WatchrxClinician watchrxClinician : patientClinicianList) {
			List<WatchRxClinicianDevice> devices = watchRxClinicianDeviceDAO
					.findByUserId(watchrxClinician.getWatchrxUser().getUserId());

			if (devices.isEmpty() && watchrxClinician.getGcmRegistrationId() != null) {
				ClinicianGCMVO vo = new ClinicianGCMVO();
				vo.setGcmId(watchrxClinician.getGcmRegistrationId());
				vo.setFirstName(watchrxClinician.getFirstName());
				vo.setLastName(watchrxClinician.getLastName());
				vo.setClinicianId(watchrxClinician.getClinicianId());
				nurseGcms.add(vo);
			} else {
				for (WatchRxClinicianDevice device : devices) {
					ClinicianGCMVO vo = new ClinicianGCMVO();
					vo.setGcmId(device.getGcmRegistrationId());
					vo.setFirstName(watchrxClinician.getFirstName());
					vo.setLastName(watchrxClinician.getLastName());
					vo.setClinicianId(watchrxClinician.getClinicianId());
					nurseGcms.add(vo);
				}
			}
		}
		return nurseGcms;
	}

	@Override
	public List<ClinicianGCMVO> getAllNurseGCMRegIdByPatientId(Long patientId) {
		List<WatchrxPatientClinicianAssignmnt> patientClinicianList = patientClinicianAssignmentDAO
				.findByProperty("watchrxPatient.patientId", patientId);
		List<ClinicianGCMVO> nurseGcms = new ArrayList<ClinicianGCMVO>();
		WatchrxClinician clinician = null;

		for (WatchrxPatientClinicianAssignmnt watchrxPatientClinicianAssignmnt : patientClinicianList) {
			int shiftValue = watchrxPatientClinicianAssignmnt.getWatchrxClinician().getShiftId().intValue();
			if (shiftValue == Constants.Shift.MORNIG || shiftValue == Constants.Shift.NIGHT
					|| shiftValue == Constants.Shift.NOON) {
				clinician = watchrxPatientClinicianAssignmnt.getWatchrxClinician();
				List<WatchRxClinicianDevice> devices = watchRxClinicianDeviceDAO
						.findByUserId(clinician.getWatchrxUser().getUserId());
				for (WatchRxClinicianDevice device : devices) {
					if (device.getGcmRegistrationId() != null) {
						ClinicianGCMVO clinicianGCMVO = new ClinicianGCMVO();
						clinicianGCMVO.setGcmId(device.getGcmRegistrationId());
						clinicianGCMVO.setFirstName(clinician.getFirstName());
						clinicianGCMVO.setLastName(clinician.getLastName());
						clinicianGCMVO.setClinicianId(clinician.getClinicianId());
						nurseGcms.add(clinicianGCMVO);
					}
				}
				if (devices.isEmpty() && clinician.getGcmRegistrationId() != null) {
					ClinicianGCMVO primaryVo = new ClinicianGCMVO();
					primaryVo.setGcmId(clinician.getGcmRegistrationId());
					primaryVo.setFirstName(clinician.getFirstName());
					primaryVo.setLastName(clinician.getLastName());
					primaryVo.setClinicianId(clinician.getClinicianId());
					nurseGcms.add(primaryVo);
				}
			}
		}
		return nurseGcms;

	}

	@Override
	public List<String> getNurseGCMRegIdByPatientId(Long patientId) {
		List<String> gcmIds = new ArrayList<>();
		List<WatchrxPatientClinicianAssignmnt> patientClinicianList = patientClinicianAssignmentDAO
				.findByProperty("watchrxPatient.patientId", patientId);
		new SimpleDateFormat("HH:mm");
		try {
			String gcmId = null;
			/*
			 * if (currentTime.before(sdf.parse(Constants.CareGiverShiftTime.NOON.
			 * split("-")[1])) &&
			 * currentTime.after(sdf.parse(Constants.CareGiverShiftTime.NOON.
			 * split("-")[0]))) { shift = Constants.Shift.NOON; }else if
			 * (currentTime.before(sdf.parse(Constants.CareGiverShiftTime.NIGHT.
			 * split("-")[1])) &&
			 * currentTime.after(sdf.parse(Constants.CareGiverShiftTime.NIGHT.
			 * split("-")[0]))) { shift = Constants.Shift.NIGHT; }
			 */
			for (WatchrxPatientClinicianAssignmnt watchrxPatientClinicianAssignmnt : patientClinicianList) {
				Long clinicianId = watchrxPatientClinicianAssignmnt.getWatchrxClinician().getClinicianId();
				Long userId = watchrxPatientClinicianAssignmnt.getWatchrxClinician().getWatchrxUser().getUserId();
				List<WatchRxClinicianDevice> devices = watchRxClinicianDeviceDAO.findByUserId(userId);
				for (WatchRxClinicianDevice device : devices) {
					if (device.getGcmRegistrationId() != null) {
						gcmIds.add(device.getGcmRegistrationId());
					}
				}
				if (devices.isEmpty()
						&& watchrxPatientClinicianAssignmnt.getWatchrxClinician().getGcmRegistrationId() != null) {
					gcmIds.add(watchrxPatientClinicianAssignmnt.getWatchrxClinician().getGcmRegistrationId());
				}

//				if (watchrxPatientClinicianAssignmnt.getWatchrxClinician().getShiftId()
//						.intValue() == Constants.Shift.MORNIG) {
//					gcmId = watchrxPatientClinicianAssignmnt.getWatchrxClinician().getGcmRegistrationId();
//					log.info("gcmId mornig" + gcmId);
//					if (gcmId != null) {
//						return gcmId;
//					}
//				}
//				if (watchrxPatientClinicianAssignmnt.getWatchrxClinician().getShiftId()
//						.intValue() == Constants.Shift.NIGHT) {
//					gcmId = watchrxPatientClinicianAssignmnt.getWatchrxClinician().getGcmRegistrationId();
//					log.info("gcmId night" + gcmId);
//					if (gcmId != null) {
//						return gcmId;
//					}
//				}
//				if (watchrxPatientClinicianAssignmnt.getWatchrxClinician().getShiftId()
//						.intValue() == Constants.Shift.NOON) {
//					gcmId = watchrxPatientClinicianAssignmnt.getWatchrxClinician().getGcmRegistrationId();
//					log.info("gcmId noon" + gcmId);
//					if (gcmId != null) {
//						return gcmId;
//					}
//				}
			}
		} catch (Exception e) {

		}
		return gcmIds;
	}

	@Override
	public String getClinicianId(Long patientId) {
		log.info("Patient id for which we need to find clinician is: " + patientId);
		List<WatchrxPatientClinicianAssignmnt> patientClinicianList = patientClinicianAssignmentDAO
				.findByProperty("watchrxPatient.patientId", patientId);
		log.info("no of patientclinician assignments found is: " + patientClinicianList.size());
		try {
			/*
			 * if (currentTime.before(sdf.parse(Constants.CareGiverShiftTime.NOON.
			 * split("-")[1])) &&
			 * currentTime.after(sdf.parse(Constants.CareGiverShiftTime.NOON.
			 * split("-")[0]))) { shift = Constants.Shift.NOON; }else if
			 * (currentTime.before(sdf.parse(Constants.CareGiverShiftTime.NIGHT.
			 * split("-")[1])) &&
			 * currentTime.after(sdf.parse(Constants.CareGiverShiftTime.NIGHT.
			 * split("-")[0]))) { shift = Constants.Shift.NIGHT; }
			 */
			Long clinicianId = null;
			for (WatchrxPatientClinicianAssignmnt watchrxPatientClinicianAssignmnt : patientClinicianList) {
				log.info("The clinician with assignment to patientid :" + patientId + "is clinicianId "
						+ watchrxPatientClinicianAssignmnt.getWatchrxClinician().getClinicianId());
				if (watchrxPatientClinicianAssignmnt.getWatchrxClinician().getShiftId()
						.intValue() == Constants.Shift.MORNIG) {
					clinicianId = watchrxPatientClinicianAssignmnt.getWatchrxClinician().getClinicianId();
					if (clinicianId != null) {
						return String.valueOf(clinicianId);
					}
				} else if (watchrxPatientClinicianAssignmnt.getWatchrxClinician().getShiftId()
						.intValue() == Constants.Shift.NIGHT) {
					clinicianId = watchrxPatientClinicianAssignmnt.getWatchrxClinician().getClinicianId();
					if (clinicianId != null) {
						return String.valueOf(clinicianId);
					}
				} else if (watchrxPatientClinicianAssignmnt.getWatchrxClinician().getShiftId()
						.intValue() == Constants.Shift.NOON) {
					clinicianId = watchrxPatientClinicianAssignmnt.getWatchrxClinician().getClinicianId();
					if (clinicianId != null) {
						return String.valueOf(clinicianId);
					}
				}
			}
		} catch (Exception e) {

		}
		return null;
	}

	@Override
	public Boolean isUsernameExists(ClinicianVO clinicianVO) {
		if (clinicianVO.getClinicianId() != null && clinicianVO.getClinicianId() > 0) {
			WatchrxClinician watchrxClinician = clinicianDAO.getById(clinicianVO.getClinicianId());
			if (StringUtils.equalsIgnoreCase(watchrxClinician.getWatchrxUser().getUserName(),
					clinicianVO.getUserName())) {
				return false;
			}
		}

		List<WatchrxClinician> watchrxClinicianList = clinicianDAO.findByProperty("watchrxUser.userName",
				clinicianVO.getUserName());
		if (watchrxClinicianList != null && watchrxClinicianList.size() > 0) {
			return true;
		} else {
			return false;
		}
	}

	@Override
	public Boolean isUserExists(RegistrationVO clinicianVO) {
		List<WatchrxClinician> watchrxClinicianList = clinicianDAO.findByProperty("watchrxUser.userName",
				clinicianVO.getUserName());
		if (watchrxClinicianList != null && watchrxClinicianList.size() > 0) {
			return true;
		} else {
			return false;
		}
	}

	@Override
	public List<PatientClinician> getPatientNurseAssignment() {
		List<WatchrxClinicianWatchAssignmnt> patientClinicianList = clinicianWatchAssignmentDAO.getAll();
		List<PatientClinician> list = new ArrayList<PatientClinician>();

		try {
			for (WatchrxClinicianWatchAssignmnt watchrxPatientClinicianAssignmnt : patientClinicianList) {
				PatientClinician clinicainPatient = new PatientClinician();
				clinicainPatient.setId(watchrxPatientClinicianAssignmnt.getClinicianWatchAssignmntId().toString());
				clinicainPatient.setNurseName(watchrxPatientClinicianAssignmnt.getWatchrxClinician().getFirstName()
						+ " " + watchrxPatientClinicianAssignmnt.getWatchrxClinician().getLastName());
				clinicainPatient.setWatchNo(watchrxPatientClinicianAssignmnt.getWatchrxWatch().getWatchImeiNumber());
				list.add(clinicainPatient);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}

	@Override
	public List<PatientClinician> getPatientWatchAssignment(String username) {
		List<Long> ids = new ArrayList<Long>();
		List<WatchrxPatientWatchAssignmnt> patientClinicianList = patientWatchAssignmentDAO.getAll();
		List<PatientClinician> list = new ArrayList<PatientClinician>();
		List<WatchrxClinician> watchrxClinicianList = clinicianDAO.findByProperty("watchrxUser.userName", username);
		if (watchrxClinicianList != null && watchrxClinicianList.size() > 0) {
			WatchrxClinician watchrxClinician = watchrxClinicianList.get(0);
			Set<WatchrxPatientClinicianAssignmnt> ss = watchrxClinician.getWatchrxPatientClinicianAssignmnts();
			for (WatchrxPatientClinicianAssignmnt watchrxPatientClinicianAssignmnt : ss) {
				ids.add(watchrxPatientClinicianAssignmnt.getWatchrxPatient().getPatientId());
			}
		}
		try {
			for (WatchrxPatientWatchAssignmnt watchrxPatientClinicianAssignmnt : patientClinicianList) {
				PatientClinician clinicainPatient = new PatientClinician();
				if (ids.contains(watchrxPatientClinicianAssignmnt.getWatchrxPatient().getPatientId())) {
					clinicainPatient.setId(watchrxPatientClinicianAssignmnt.getPatientWatchAssignmntId().toString());
					clinicainPatient
							.setNurseName(watchrxPatientClinicianAssignmnt.getWatchrxWatch().getWatchImeiNumber());
					clinicainPatient.setPatientName(watchrxPatientClinicianAssignmnt.getWatchrxPatient().getFirstName()
							+ " " + watchrxPatientClinicianAssignmnt.getWatchrxPatient().getLastName());
					list.add(clinicainPatient);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}

	@Override
	public List<ClinicianVO> getCaseManagersByPatient(Long patientId) throws WatchRxServiceException {
		try {
			List<ClinicianVO> caregivers = new ArrayList<ClinicianVO>();
			List<WatchrxPatientClinicianAssignmnt> patientClinician = patientClinicianAssignmentDAO
					.findByProperty("watchrxPatient.patientId", patientId);
			if (patientClinician.size() > 0) {
				for (WatchrxPatientClinicianAssignmnt tmpPatCli : patientClinician) {
					WatchrxClinician watchrxClinician = tmpPatCli.getWatchrxClinician();
					ClinicianVO clinician = getClinician(watchrxClinician.getClinicianId(), 5L);
					caregivers.add(clinician);
				}
			}
			return caregivers;
		} catch (Exception e) {
			throw new WatchRxServiceException("Error while fetchning patients.");
		}
	}

	@Override
	public List<ClinicianVO> getCareGiversByPatient(Long patientId) throws WatchRxServiceException {
		try {
			List<ClinicianVO> caregivers = new ArrayList<ClinicianVO>();
			List<WatchrxPatientCareGiverAssignmnt> patientCareGiver = patientCareGiverAssignmentDAO
					.findByProperty("watchrxPatient.patientId", patientId);
			if (patientCareGiver.size() > 0) {
				for (WatchrxPatientCareGiverAssignmnt tmpPatCli : patientCareGiver) {
					WatchrxClinician watchrxClinician = tmpPatCli.getWatchrxClinician();
					ClinicianVO clinician = getClinician(watchrxClinician.getClinicianId(), 5L);
					caregivers.add(clinician);
				}
			}
			return caregivers;
		} catch (Exception e) {
			throw new WatchRxServiceException("Error while fetchning patients.");
		}
	}

	@Override
	public void unassign(long id) {
		clinicianWatchAssignmentDAO.deleteByProperty("clinicianWatchAssignmntId", id);
	}

	@Override
	public void unassignPW(long id) {
		// TODO Auto-generated method stub
		patientWatchAssignmentDAO.deleteByProperty("patientWatchAssignmntId", id);

	}

	@Override
	public void unassignCaregiver(AssignToPatientVO data) {
		WatchrxClinician clinician = clinicianDAO.getById(data.getMorningClinicianId());

		List<WatchrxClinicianWatchAssignmnt> clinicianWatch = clinicianWatchAssignmentDAO
				.findByProperty("watchrxClinician.clinicianId", data.getMorningClinicianId());
		// List<WatchrxPatientClinicianAssignmnt> patientClinician =
		// patientClinicianAssignmentDAO.findByProperty("watchrxPatient.patientId",
		// data.getPatientId());
		List<WatchrxPatientClinicianAssignmnt> patientClinician = patientClinicianAssignmentDAO
				.findByProperty("watchrxClinician.clinicianId", data.getMorningClinicianId());
		List<WatchrxSecCGInvite> secondaryCaregivers = secCGInviteDAO.findByProperty("watchrxPatient.patientId",
				data.getPatientId());

		for (WatchrxClinicianWatchAssignmnt tmpClinicianWatch : clinicianWatch) {
			System.out.println("Clinician Watch Assignment size " + clinicianWatch.size());
			Long id = tmpClinicianWatch.getClinicianWatchAssignmntId();
			Long watchId = tmpClinicianWatch.getWatchrxWatch().getWatchId();
			if (watchId.equals(data.getWatchId())) {
				System.out.println("Deleting ClinicianWatchId: " + watchId);
				clinicianWatchAssignmentDAO.deleteByProperty("clinicianWatchAssignmntId", id);
				// clinicianWatchAssignmentDAO.deleteByProperty("watchrxWatch.watchId",
				// watchId);

			}
		}
		for (WatchrxPatientClinicianAssignmnt tmpPatientClinician : patientClinician) {
			System.out.println("Patient Clinician Assignment size " + patientClinician.size());
			Long assgnId = tmpPatientClinician.getPatientClinicianAssignmntId();
			Long tmpPatientId = tmpPatientClinician.getWatchrxPatient().getPatientId();
			if (tmpPatientId.equals(data.getPatientId())) {
				System.out.println("Deleting PatientClinicianId: " + tmpPatientId);
				patientClinicianAssignmentDAO.deleteByProperty("patientClinicianAssignmntId", assgnId);
				// patientClinicianAssignmentDAO.deleteByProperty("watchrxPatient.patientId",
				// tmpPatientId);
			}
		}

		for (WatchrxSecCGInvite tmpSecCG : secondaryCaregivers) {
			System.out.println("Secondary CaregiverInviet size " + secondaryCaregivers.size());
			Long patientId = tmpSecCG.getWatchrxPatient().getPatientId();
			String userName = clinician.getWatchrxUser().getUserName();
			if (patientId.equals(data.getPatientId()) && userName.equalsIgnoreCase(tmpSecCG.getEmail())) {
				System.out.println("Deleting Secondary Caregiver Invite");
				secCGInviteDAO.deleteByProperty("watchrxPatient.patientId", data.getPatientId());
			}
		}

	}

	@Override
	public List<WatchrxSecCGInvite> getSecCGInvite(String email) {

		List<WatchrxSecCGInvite> watchrxSecCGInviteList = secCGInviteDAO.findByProperty("email", email);

		if (watchrxSecCGInviteList != null && watchrxSecCGInviteList.size() > 0) {
			return watchrxSecCGInviteList;
		} else {
			return new ArrayList<WatchrxSecCGInvite>();
		}
	}

	@Override
	public Boolean AcceptSecondaryCaregiverInvite(AssignToPatientVO inviteReqData) {

		WatchrxClinician clinician = clinicianDAO.getById(inviteReqData.getMorningClinicianId());
		WatchrxWatch watch = watchDAO.getById(inviteReqData.getWatchId());
		WatchrxClinicianWatchAssignmnt secClinician = new WatchrxClinicianWatchAssignmnt();
		secClinician.setWatchrxClinician(clinician);
		secClinician.setWatchrxWatch(watch);
		secClinician.setclinicianWatchRelationship(Constants.CaregiverRole.SECONDARY);
		clinicianWatchAssignmentDAO.save(secClinician);
		WatchrxPatient patient = patientDAO.getById(inviteReqData.getPatientId());
		WatchrxPatientClinicianAssignmnt patientClinician = new WatchrxPatientClinicianAssignmnt();
		patientClinician.setWatchrxClinician(clinician);
		patientClinician.setWatchrxPatient(patient);
		patientClinicianAssignmentDAO.save(patientClinician);
		List<WatchrxSecCGInvite> watchrxSecCGInviteList = secCGInviteDAO.findByProperty("watchrxPatient.patientId",
				inviteReqData.getPatientId());
		if (watchrxSecCGInviteList.size() > 0) {
			String userName = clinician.getWatchrxUser().getUserName();
			System.out.println("Username: " + userName);
			System.out.println("InviteList size: " + watchrxSecCGInviteList.size());
			for (WatchrxSecCGInvite inviteReq : watchrxSecCGInviteList) {
				System.out.println("Inside for loop to change status");
				if (userName.equalsIgnoreCase(inviteReq.getEmail())) {
					inviteReq.setStatus(Constants.SecCGStatus.ACCEPT);
					secCGInviteDAO.save(inviteReq);
				}
			}
			return true;
		} else {
			return false;
		}

	}

	@Override
	public Boolean RejectSecondaryCaregiverInvite(AssignToPatientVO reqData) {
		WatchrxClinician clinician = clinicianDAO.getById(reqData.getMorningClinicianId());
		List<WatchrxSecCGInvite> watchrxSecCGInviteList = secCGInviteDAO.findByProperty("watchrxPatient.patientId",
				reqData.getPatientId());
		if (watchrxSecCGInviteList.size() > 0) {
			System.out.println("InviteList size: " + watchrxSecCGInviteList.size());
			String userName = clinician.getWatchrxUser().getUserName();
			System.out.println("Username: " + userName);
			for (WatchrxSecCGInvite inviteReq : watchrxSecCGInviteList) {
				System.out.println("Inside for loop to change status");
				if (userName.equalsIgnoreCase(inviteReq.getEmail())) {
					inviteReq.setStatus(Constants.SecCGStatus.REJECT);
					secCGInviteDAO.save(inviteReq);
				}
			}
			return true;
		} else {
			return false;
		}

	}

	@Override
	public List<AddressVO> getMailingAddressByClinicianID(Long id) {
		// TODO Auto-generated method stub
		WatchrxClinician watchrxClinician = clinicianDAO.getById(id);
		if (watchrxClinician == null) {
			return null;
		}
		Set<WatchrxMailingAddress> addresses = watchrxClinician.getWatchrxMailingAddresses();
		List<AddressVO> mailingAddresses = new ArrayList<AddressVO>();
		AddressVO currentAddressVO;
		for (WatchrxMailingAddress address : addresses) {
			currentAddressVO = new AddressVO();
			currentAddressVO.setAddress1(address.getAddress1());
			currentAddressVO.setAddress2(address.getAddress2());
			currentAddressVO.setAddressId(address.getAddressId());
			currentAddressVO.setCity(address.getCity());
			currentAddressVO.setCountry(address.getCountry());
			currentAddressVO.setState(address.getState());
			currentAddressVO.setZip(address.getZip());

			mailingAddresses.add(currentAddressVO);
		}

		return mailingAddresses;
	}

	@Override
	public List<AddressVO> getMailingAddressByUserID(Long id) {
		// TODO Auto-generated method stub

		List<WatchrxClinician> watchrxClinicians = clinicianDAO.findByProperty("watchrxUser.userId", id);
		WatchrxClinician watchrxClinician = watchrxClinicians.get(0);
		if (watchrxClinician == null) {
			return null;
		}
		Set<WatchrxMailingAddress> addresses = watchrxClinician.getWatchrxMailingAddresses();
		List<AddressVO> mailingAddresses = new ArrayList<AddressVO>();
		AddressVO currentAddressVO;
		for (WatchrxMailingAddress address : addresses) {
			currentAddressVO = new AddressVO();
			currentAddressVO.setAddress1(address.getAddress1());
			currentAddressVO.setAddress2(address.getAddress2());
			currentAddressVO.setAddressId(address.getAddressId());
			currentAddressVO.setCity(address.getCity());
			currentAddressVO.setCountry(address.getCountry());
			currentAddressVO.setState(address.getState());
			currentAddressVO.setZip(address.getZip());

			mailingAddresses.add(currentAddressVO);
		}

		return mailingAddresses;
	}

	@Override
	public Boolean haswatch(Long id) {
		// TODO Auto-generated method stub

		List<WatchrxClinicianWatchAssignmnt> watchrxClinicianWatchAssignmnts = clinicianWatchAssignmentDAO
				.findByProperty("watchrxClinician.clinicianId", id);
		List<WatchrxWatchBuyingAllocation> watchrxWatchBuyingAllocations = buyingAllocationDAO
				.findByProperty("caregiver.clinicianId", id);
		if ((watchrxClinicianWatchAssignmnts != null && watchrxClinicianWatchAssignmnts.size() > 0)
				|| (watchrxWatchBuyingAllocations != null && watchrxWatchBuyingAllocations.size() > 0)) {
			return true;
		} else {
			return false;
		}
	}

	@Override
	public List<ClinicianWithAssociatedWatchesAndPatientsVO> getClinicianListWithInActive(int index, int size) {

		List<WatchrxClinician> clinicians = clinicianDAO.getAllWithInActive(index, size);
		List<ClinicianWithAssociatedWatchesAndPatientsVO> clinicianList = new ArrayList<ClinicianWithAssociatedWatchesAndPatientsVO>();
		Map<Integer, String> refData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.CLINICIAN_TYPE);
		Map<Integer, String> shiftRefData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.SHIFT);
		for (WatchrxClinician watchrxClinician : clinicians) {
			ClinicianWithAssociatedWatchesAndPatientsVO clinicianVO = getClinicianWithAssociatedWatchesAndPatientsVO(
					watchrxClinician);
			clinicianVO.setSpecialityName(refData.get(clinicianVO.getSpeciality()));
			clinicianVO.setShiftName(shiftRefData.get(clinicianVO.getShift()));

			clinicianVO.setPatientsWithWatchesHavingSecondaryRelToClinician(new ArrayList<PatientDetailsVO>());
			clinicianVO.setPatientsWithWatchesHavingPrimaryRelToClinician(new ArrayList<PatientDetailsVO>());
			clinicianVO.setAllWatches(new ArrayList<WatchDetailsVO>());// new
																		// ArrayList<WatchDetailsVO>()

			Set<WatchrxClinicianWatchAssignmnt> watchrxClinicianWatchAssignments = watchrxClinician
					.getWatchrxClinicianWatchAssignmnts();
			for (WatchrxClinicianWatchAssignmnt watchrxClinicianWatchAssignmnt : watchrxClinicianWatchAssignments) {
				WatchrxWatch watch = watchrxClinicianWatchAssignmnt.getWatchrxWatch();
				WatchDetailsVO watchDet = new WatchDetailsVO();
				watchDet.setClinician(watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName());
				watchDet.setImeiNumber(watch.getWatchImeiNumber());
				watchDet.setIsActive(watch.getIsWatchActive());
				if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship() == null
						|| watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("P")) {
					watchDet.setIsPrimary(true);
				} else if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("S")) {
					watchDet.setIsPrimary(false);
				}
				watchDet.setPhoneNumber(watch.getWatchPhoneNumber());
				watchDet.setWatchId(watch.getWatchId());
				for (WatchrxPatientWatchAssignmnt watchrxPatientWatchAssignmnt : watch
						.getWatchrxPatientWatchAssignmnts()) {
					PatientDetailsVO patientDetailsVO = new PatientDetailsVO();
					watchDet.setPatient(watchrxPatientWatchAssignmnt.getWatchrxPatient().getFirstName() + " "
							+ watchrxPatientWatchAssignmnt.getWatchrxPatient().getLastName());
					patientDetailsVO.setAddress(watchrxPatientWatchAssignmnt.getWatchrxPatient().getWatchrxAddress()
							.getAddressCollectionAsString());
					patientDetailsVO
							.setAltPhoneNumber(watchrxPatientWatchAssignmnt.getWatchrxPatient().getAltPhoneNum());
					patientDetailsVO
							.setClinicianName(watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName());
					patientDetailsVO.setFirstName(watchrxPatientWatchAssignmnt.getWatchrxPatient().getFirstName());
					patientDetailsVO.setLastName(watchrxPatientWatchAssignmnt.getWatchrxPatient().getLastName());
					patientDetailsVO.setPatientId(watchrxPatientWatchAssignmnt.getWatchrxPatient().getPatientId());
					patientDetailsVO.setPhoneNumber(watchrxPatientWatchAssignmnt.getWatchrxPatient().getPhoneNumber());
					patientDetailsVO.setWatchImei(watch.getWatchImeiNumber());
					patientDetailsVO.setWatchNumber(watch.getWatchPhoneNumber());
					if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship() == null
							|| watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("P")) {
						clinicianVO.getPatientsWithWatchesHavingPrimaryRelToClinician().add(patientDetailsVO);
					} else if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("S")) {
						clinicianVO.getPatientsWithWatchesHavingSecondaryRelToClinician().add(patientDetailsVO);
					}
				}
				clinicianVO.getAllWatches().add(watchDet);

			}

			clinicianList.add(clinicianVO);
		}
		// TODO Auto-generated method stub
		return clinicianList;

	}

	@Override
	public Long getCount() {
		return clinicianDAO.getCount();

	}

	@Override
	public List<ClinicianWithAssociatedWatchesAndPatientsVO> getClinicianListWithInActive(String name) {

		List<WatchrxClinician> clinicians = clinicianDAO.getAllWithInActive(name);
		List<ClinicianWithAssociatedWatchesAndPatientsVO> clinicianList = new ArrayList<ClinicianWithAssociatedWatchesAndPatientsVO>();
		Map<Integer, String> refData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.CLINICIAN_TYPE);
		Map<Integer, String> shiftRefData = DropdownUtils.getRefDataByRefType(Constants.ReferenceType.SHIFT);
		for (WatchrxClinician watchrxClinician : clinicians) {
			ClinicianWithAssociatedWatchesAndPatientsVO clinicianVO = getClinicianWithAssociatedWatchesAndPatientsVO(
					watchrxClinician);
			clinicianVO.setSpecialityName(refData.get(clinicianVO.getSpeciality()));
			clinicianVO.setShiftName(shiftRefData.get(clinicianVO.getShift()));

			clinicianVO.setPatientsWithWatchesHavingSecondaryRelToClinician(new ArrayList<PatientDetailsVO>());
			clinicianVO.setPatientsWithWatchesHavingPrimaryRelToClinician(new ArrayList<PatientDetailsVO>());
			clinicianVO.setAllWatches(new ArrayList<WatchDetailsVO>());// new
																		// ArrayList<WatchDetailsVO>()

			Set<WatchrxClinicianWatchAssignmnt> watchrxClinicianWatchAssignments = watchrxClinician
					.getWatchrxClinicianWatchAssignmnts();
			for (WatchrxClinicianWatchAssignmnt watchrxClinicianWatchAssignmnt : watchrxClinicianWatchAssignments) {
				WatchrxWatch watch = watchrxClinicianWatchAssignmnt.getWatchrxWatch();
				WatchDetailsVO watchDet = new WatchDetailsVO();
				watchDet.setClinician(watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName());
				watchDet.setImeiNumber(watch.getWatchImeiNumber());
				watchDet.setIsActive(watch.getIsWatchActive());
				if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship() == null
						|| watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("P")) {
					watchDet.setIsPrimary(true);
				} else if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("S")) {
					watchDet.setIsPrimary(false);
				}
				watchDet.setPhoneNumber(watch.getWatchPhoneNumber());
				watchDet.setWatchId(watch.getWatchId());
				for (WatchrxPatientWatchAssignmnt watchrxPatientWatchAssignmnt : watch
						.getWatchrxPatientWatchAssignmnts()) {
					PatientDetailsVO patientDetailsVO = new PatientDetailsVO();
					watchDet.setPatient(watchrxPatientWatchAssignmnt.getWatchrxPatient().getFirstName() + " "
							+ watchrxPatientWatchAssignmnt.getWatchrxPatient().getLastName());
					patientDetailsVO.setAddress(watchrxPatientWatchAssignmnt.getWatchrxPatient().getWatchrxAddress()
							.getAddressCollectionAsString());
					patientDetailsVO
							.setAltPhoneNumber(watchrxPatientWatchAssignmnt.getWatchrxPatient().getAltPhoneNum());
					patientDetailsVO
							.setClinicianName(watchrxClinician.getFirstName() + " " + watchrxClinician.getLastName());
					patientDetailsVO.setFirstName(watchrxPatientWatchAssignmnt.getWatchrxPatient().getFirstName());
					patientDetailsVO.setLastName(watchrxPatientWatchAssignmnt.getWatchrxPatient().getLastName());
					patientDetailsVO.setPatientId(watchrxPatientWatchAssignmnt.getWatchrxPatient().getPatientId());
					patientDetailsVO.setPhoneNumber(watchrxPatientWatchAssignmnt.getWatchrxPatient().getPhoneNumber());
					patientDetailsVO.setWatchImei(watch.getWatchImeiNumber());
					patientDetailsVO.setWatchNumber(watch.getWatchPhoneNumber());
					if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship() == null
							|| watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("P")) {
						clinicianVO.getPatientsWithWatchesHavingPrimaryRelToClinician().add(patientDetailsVO);
					} else if (watchrxClinicianWatchAssignmnt.getclinicianWatchRelationship().equals("S")) {
						clinicianVO.getPatientsWithWatchesHavingSecondaryRelToClinician().add(patientDetailsVO);
					}
				}
				clinicianVO.getAllWatches().add(watchDet);

			}

			clinicianList.add(clinicianVO);
		}
		// TODO Auto-generated method stub
		return clinicianList;

	}

	@Override
	public ClinicianResponse getClinicianListWithInActiveNew(String userType) {
		Integer usertypeint = null;
		switch (userType) {
		case "ADMIN":
			usertypeint = Constants.UserType.ADMIN;
			break;
		case "COORDINATOR":
			usertypeint = Constants.UserType.COORDINATOR;
			break;
		case "PHYSICIAN":
			usertypeint = Constants.UserType.PHYSICIAN;
			break;
		case "CAREGIVER":
			usertypeint = Constants.UserType.CAREGIVER;
			break;
		case "CASEMANAGER":
			usertypeint = Constants.UserType.CASEMANAGER;
			break;
		}

		ClinicianResponse clinicianResponse = new ClinicianResponse();

		if (usertypeint == Constants.UserType.PHYSICIAN) {
			List<WatchRxPhysician> watchRxPhysicianList = physicianDAO.getAll();
			List<ClinicianResponseVO> clinicians = new ArrayList<ClinicianResponseVO>();
			for (WatchRxPhysician physician : watchRxPhysicianList) {
				ClinicianResponseVO clinicianResponseVO = new ClinicianResponseVO();
				AddressVO addressVO = new AddressVO();
				if (physician.getWatchrxAddress() != null) {
					addressVO.setAddress1(physician.getWatchrxAddress().getAddress1());
					addressVO.setAddress2(physician.getWatchrxAddress().getAddress2());
					addressVO.setAddressId(physician.getWatchrxAddress().getAddressId());
				} else {
					addressVO = null;
				}
				clinicianResponseVO.setClinicianId(physician.getPhysicianId());
				clinicianResponseVO.setUserName(physician.getWatchrxUser().getUserName());
				clinicianResponseVO.setFirstName(physician.getFirstName());
				clinicianResponseVO.setLastName(physician.getLastName());
				clinicianResponseVO.setPhoneNumber(physician.getPhoneNumber());
				clinicianResponseVO.setAltPhoneNumber(physician.getAltPhoneNumber());
				clinicianResponseVO.setHospital(physician.getHospitalName());
				clinicianResponseVO.setShift(physician.getShiftId());
				clinicianResponseVO.setAddress(addressVO);
				if (physician.getWatchrxUser() != null) {
					clinicianResponseVO.setEmail(physician.getWatchrxUser().getEmail());
				} else {
					clinicianResponseVO.setEmail(null);
				}

				clinicians.add(clinicianResponseVO);
			}
			clinicianResponse.setClinicianList(clinicians);
		} else {
			List<WatchrxClinician> watchrxClinicians = clinicianDAO.getAllUsersByType(usertypeint);
			List<ClinicianResponseVO> clinicians = new ArrayList<ClinicianResponseVO>();

			for (WatchrxClinician watchrxClinician : watchrxClinicians) {
				ClinicianResponseVO clinicianResponseVO = new ClinicianResponseVO();

				clinicianResponseVO.setClinicianId(watchrxClinician.getClinicianId());
				clinicianResponseVO.setUserName(watchrxClinician.getWatchrxUser().getUserName());
				clinicianResponseVO.setFirstName(watchrxClinician.getFirstName());
				clinicianResponseVO.setLastName(watchrxClinician.getLastName());
				clinicianResponseVO.setPhoneNumber(watchrxClinician.getPhoneNumber());
				clinicianResponseVO.setAltPhoneNumber(watchrxClinician.getAltPhoneNumber());
				clinicianResponseVO.setHospital(watchrxClinician.getHospitalName());
				clinicianResponseVO.setShift(watchrxClinician.getShiftId());
				if (watchrxClinician.getWatchrxUser() != null) {
					clinicianResponseVO.setEmail(watchrxClinician.getWatchrxUser().getEmail());
				} else {
					clinicianResponseVO.setEmail(null);
				}
				WatchrxAddress watchrxAddress = watchrxClinician.getWatchrxAddress();

				if (watchrxAddress != null) {
					AddressVO addressVO = new AddressVO();
					addressVO.setAddress1(watchrxAddress.getAddress1());
					addressVO.setAddress2(watchrxAddress.getAddress2());
					clinicianResponseVO.setAddress(addressVO);
				}
				clinicians.add(clinicianResponseVO);
			}
			clinicianResponse.setClinicianList(clinicians);
		}
		return clinicianResponse;
	}

	@Override
	public ClinicianResponse getClinicianListWithInActiveNew(String userType, Integer index, Integer pageSize) {
		Integer usertypeint = null;
		switch (userType) {
		case "ADMIN":
			usertypeint = Constants.UserType.ADMIN;
			break;
		case "COORDINATOR":
			usertypeint = Constants.UserType.COORDINATOR;
			break;
		case "PHYSICIAN":
			usertypeint = Constants.UserType.PHYSICIAN;
			break;
		case "CAREGIVER":
			usertypeint = Constants.UserType.CAREGIVER;
			break;
		case "CASEMANAGER":
			usertypeint = Constants.UserType.CASEMANAGER;
			break;
		}

		ClinicianResponse clinicianResponse = new ClinicianResponse();

		if (usertypeint == Constants.UserType.PHYSICIAN) {
			List<WatchRxPhysician> watchRxPhysicianList = physicianDAO.getAllPaginated(index, pageSize);
			List<ClinicianResponseVO> clinicians = new ArrayList<ClinicianResponseVO>();
			for (WatchRxPhysician physician : watchRxPhysicianList) {
				ClinicianResponseVO clinicianResponseVO = new ClinicianResponseVO();
				AddressVO addressVO = new AddressVO();
				if (physician.getWatchrxAddress() != null) {
					addressVO.setAddress1(physician.getWatchrxAddress().getAddress1());
					addressVO.setAddress2(physician.getWatchrxAddress().getAddress2());
					addressVO.setAddressId(physician.getWatchrxAddress().getAddressId());
				} else {
					addressVO = null;
				}

				clinicianResponseVO.setClinicianId(physician.getPhysicianId());
				clinicianResponseVO.setUserName(physician.getWatchrxUser().getUserName());
				clinicianResponseVO.setFirstName(physician.getFirstName());
				clinicianResponseVO.setLastName(physician.getLastName());
				clinicianResponseVO.setPhoneNumber(physician.getPhoneNumber());
				clinicianResponseVO.setAltPhoneNumber(physician.getAltPhoneNumber());
				clinicianResponseVO.setHospital(physician.getHospitalName());
				clinicianResponseVO.setShift(physician.getShiftId());
				clinicianResponseVO.setStatus(physician.getWatchrxUser().getStatus());
				clinicianResponseVO.setPatientCount(
						patientDAO.findByPropertyCount("watchrxPhysician.physicianId", physician.getPhysicianId()));
				clinicianResponseVO.setAddress(addressVO);
				if (physician.getWatchrxUser() != null) {
					clinicianResponseVO.setEmail(physician.getWatchrxUser().getEmail());
				} else {
					clinicianResponseVO.setEmail(null);
				}

				clinicians.add(clinicianResponseVO);
			}
			clinicianResponse.setClinicianList(clinicians);
		} else if (usertypeint == Constants.UserType.COORDINATOR) {

			List<WatchrxUser> watchrxClinicians = clinicianDAO.getAllUsersByAdminType(usertypeint, index, pageSize);
			List<ClinicianResponseVO> clinicians = new ArrayList<ClinicianResponseVO>();

			for (WatchrxUser watchrxClinician : watchrxClinicians) {
				ClinicianResponseVO clinicianResponseVO = new ClinicianResponseVO();
				clinicianResponseVO.setClinicianId(watchrxClinician.getUserId());
				clinicianResponseVO.setUserName(watchrxClinician.getUserName());
				clinicianResponseVO.setFirstName(watchrxClinician.getFirstName());
				clinicianResponseVO.setLastName(watchrxClinician.getLastName());
				clinicianResponseVO.setStatus(watchrxClinician.getStatus());
				clinicianResponseVO.setEmail(watchrxClinician.getEmail());
				clinicians.add(clinicianResponseVO);
			}
			clinicianResponse.setClinicianList(clinicians);

		} else {
			List<WatchrxClinician> watchrxClinicians = clinicianDAO.getAllUsersByType(usertypeint, index, pageSize);
			List<ClinicianResponseVO> clinicians = new ArrayList<ClinicianResponseVO>();

			for (WatchrxClinician watchrxClinician : watchrxClinicians) {
				ClinicianResponseVO clinicianResponseVO = new ClinicianResponseVO();
				clinicianResponseVO.setClinicianId(watchrxClinician.getClinicianId());
				clinicianResponseVO.setUserName(watchrxClinician.getWatchrxUser().getUserName());
				clinicianResponseVO.setFirstName(watchrxClinician.getFirstName());
				clinicianResponseVO.setLastName(watchrxClinician.getLastName());
				clinicianResponseVO.setPhoneNumber(watchrxClinician.getPhoneNumber());
				clinicianResponseVO.setAltPhoneNumber(watchrxClinician.getAltPhoneNumber());
				clinicianResponseVO.setHospital(watchrxClinician.getHospitalName());
				clinicianResponseVO.setShift(watchrxClinician.getShiftId());
				clinicianResponseVO.setStatus(watchrxClinician.getWatchrxUser().getStatus());
				if (watchrxClinician.getWatchrxUser() != null) {
					clinicianResponseVO.setEmail(watchrxClinician.getWatchrxUser().getEmail());
				} else {
					clinicianResponseVO.setEmail(null);
				}
				clinicianResponseVO
						.setPatientCount(patientDAO.patientTotalCountCaseManager(watchrxClinician.getClinicianId()));
				WatchrxAddress watchrxAddress = watchrxClinician.getWatchrxAddress();

				if (watchrxAddress != null) {
					AddressVO addressVO = new AddressVO();
					addressVO.setAddress1(watchrxAddress.getAddress1());
					addressVO.setAddress2(watchrxAddress.getAddress2());
					clinicianResponseVO.setAddress(addressVO);
				}
				clinicians.add(clinicianResponseVO);
			}
			clinicianResponse.setClinicianList(clinicians);
		}
		return clinicianResponse;
	}

	@Override
	public RPMPhysicianCaseManagersResponseVO getAllRPMPhysicianCaseManagers(Integer index, Integer pageSize) {
		List<WatchRxPhysician> watchRxPhysicianList = physicianDAO.getAll();
		List<WatchrxClinician> watchrxClinicianList = clinicianDAO.getAllUsersByType(Constants.UserType.CASEMANAGER,
				index, 500);
		// getGroupsAndRolesForUser
		List<PhysicianVO> physicianVOList = new ArrayList<PhysicianVO>();
		List<CaseManagerVO> clinicianVOList = new ArrayList<CaseManagerVO>();
		for (WatchRxPhysician physician : watchRxPhysicianList) {
			PhysicianVO physicianVO = new PhysicianVO();
			AddressVO addressVO = new AddressVO();
			if (physician.getWatchrxAddress() != null) {
				addressVO.setAddress1(physician.getWatchrxAddress().getAddress1());
				addressVO.setAddress2(physician.getWatchrxAddress().getAddress2());
				addressVO.setAddressId(physician.getWatchrxAddress().getAddressId());
			} else {
				addressVO = null;
			}
			if (physician.getWatchrxUser() != null) {
				physicianVO.setEmail(physician.getWatchrxUser().getEmail());
			} else {
				physicianVO.setEmail(null);
			}
			physicianVO.setAddress(addressVO);
			physicianVO.setAltPhoneNumber(physician.getAltPhoneNumber());
			physicianVO.setCreatedDate(formater1.format(physician.getCreatedDate()));
			physicianVO.setFirstName(physician.getFirstName());
			physicianVO.setGcmRegistrationId(physician.getGcmRegistrationId());
			physicianVO.setHospitalName(physician.getHospitalName());
			physicianVO.setLastName(physician.getLastName());
			physicianVO.setPhoneNumber(physician.getPhoneNumber());
			physicianVO.setPhysicianId(physician.getPhysicianId());
			physicianVO.setPlatformType(physician.getPlatformType());
			physicianVO.setShiftId(physician.getShiftId());
			physicianVO.setStatus(physician.getStatus());
			physicianVO.setUpdatedDate(formater1.format(physician.getUpdatedDate()));
			physicianVO.setWatchrxUserID(physician.getWatchrxUser().getUserId());
			physicianVOList.add(physicianVO);
		}
		for (WatchrxClinician clinician : watchrxClinicianList) {
			CaseManagerVO caseManagerVO = new CaseManagerVO();
			AddressVO addressVO = new AddressVO();
			if (clinician.getWatchrxAddress() != null) {
				addressVO.setAddress1(clinician.getWatchrxAddress().getAddress1());
				addressVO.setAddress2(clinician.getWatchrxAddress().getAddress2());
				addressVO.setAddressId(clinician.getWatchrxAddress().getAddressId());
			} else {
				addressVO = null;
			}
			if (clinician.getWatchrxUser() != null) {
				caseManagerVO.setEmail(clinician.getWatchrxUser().getEmail());
			} else {
				caseManagerVO.setEmail(null);
			}

			caseManagerVO.setAddress(addressVO);
			caseManagerVO.setAltPhoneNumber(clinician.getAltPhoneNumber());
			caseManagerVO.setCreatedDate(formater.format(clinician.getCreatedDate()));
			caseManagerVO.setFirstName(clinician.getFirstName());
			caseManagerVO.setGcmRegistrationId(clinician.getGcmRegistrationId());
			caseManagerVO.setHospitalName(clinician.getHospitalName());
			caseManagerVO.setLastName(clinician.getLastName());
			caseManagerVO.setPhoneNumber(clinician.getPhoneNumber());
			caseManagerVO.setClinicianId(clinician.getClinicianId());
			caseManagerVO.setPlatformType(clinician.getPlatformType());
			caseManagerVO.setShiftId(clinician.getShiftId());
			caseManagerVO.setStatus(clinician.getStatus());
			caseManagerVO.setUpdatedDate(formater.format(clinician.getUpdatedDate()));
			caseManagerVO.setUserID(clinician.getWatchrxUser().getUserId());
			List<WatchrxPhysicianCaseManagerAssignment> watchrxPhysicianCaseManagerAssignmentList = physicianCaseManagerAssignmentDAO
					.findByProperty("watchrxClinician.clinicianId", clinician.getClinicianId());
			List<String> associatedPhysicians = new ArrayList<String>();
			for (WatchrxPhysicianCaseManagerAssignment watchrxPhysicianCaseManagerAssignment : watchrxPhysicianCaseManagerAssignmentList) {
				associatedPhysicians.add(watchrxPhysicianCaseManagerAssignment.getWatchrxPhysician().getFirstName()
						+ " " + watchrxPhysicianCaseManagerAssignment.getWatchrxPhysician().getLastName());
			}
			caseManagerVO.setAssignedToPhysician(associatedPhysicians);
			clinicianVOList.add(caseManagerVO);
		}
		RPMPhysicianCaseManagersResponseVO rPMPhysicianCaseManagersResponseVO = new RPMPhysicianCaseManagersResponseVO();
		rPMPhysicianCaseManagersResponseVO.setClinicianVOList(clinicianVOList);
		rPMPhysicianCaseManagersResponseVO.setPhysicianVOList(physicianVOList);
		return rPMPhysicianCaseManagersResponseVO;
	}

	@Override
	public PatientWatchCaregiverListVO getPatientsListForCaseManager(CaseManagerVO watchrxClinician) {

		PatientWatchCaregiverListVO caseManagerPatientsAssignVOList = new PatientWatchCaregiverListVO();

		List<PatientWatchCaregiverInfoVO> data = new ArrayList<PatientWatchCaregiverInfoVO>();

		new ArrayList<WatchrxPatient>();
		new WatchrxPatient();

		log.info(">>>>>>>Received details : " + watchrxClinician.getClinicianId());
		WatchrxClinician clinician = clinicianDAO.getById(watchrxClinician.getClinicianId());
		if (clinician != null) {
			List<WatchrxPatientClinicianAssignmnt> watchrxPatientClinicianAssignmntList = patientClinicianAssignmentDAO
					.getAllPatientsForCaseManager(clinician.getClinicianId());

			if (watchrxPatientClinicianAssignmntList.size() > 0) {
				for (WatchrxPatientClinicianAssignmnt watchrxPatientClinicianAssignmnt : watchrxPatientClinicianAssignmntList) {

					PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
					List<ClinicianVO> clinicianList = new ArrayList<ClinicianVO>();

					String name = watchrxPatientClinicianAssignmnt.getWatchrxPatient().getFirstName() + " "
							+ watchrxPatientClinicianAssignmnt.getWatchrxPatient().getLastName();

					Long patientID = watchrxPatientClinicianAssignmnt.getWatchrxPatient().getPatientId();
					log.info(" >>>>>> for Patient :  " + patientID + " >>>>>>>");

					pwcInfo.setPatientId(patientID.toString());
					pwcInfo.setPatientName(name);
					if (watchrxPatientClinicianAssignmnt.getWatchrxPatient().getPhoneNumber() != null) {
						pwcInfo.setPatientPhoneNumber(
								watchrxPatientClinicianAssignmnt.getWatchrxPatient().getPhoneNumber().toString());
					}

					try {
						clinicianList = getCaseManagersByPatient(patientID);
					} catch (WatchRxServiceException e) {
						e.printStackTrace();
					}

					String clinicianNames = new String();

					if (clinicianList.size() > 0 && clinicianList.size() == 1) {
						log.info(" >>>>>> Inside list > 0");
						pwcInfo.setCaseManagerName(
								clinicianList.get(0).getFirstName() + " " + clinicianList.get(0).getLastName());
						pwcInfo.setCaseManagerId(clinicianList.get(0).getClinicianId());
					} else if (clinicianList.size() > 0) {
						log.info(" >>>>>> Inside else > 0 ");
						for (int i = 0; i < clinicianList.size(); i++) {
							if (i == 0) {
								clinicianNames = clinicianList.get(i).getFirstName() + " "
										+ clinicianList.get(0).getLastName();
							} else {
								clinicianNames = clinicianNames + " / " + clinicianList.get(i).getFirstName() + " "
										+ clinicianList.get(i).getLastName();
							}
						}
						pwcInfo.setCaseManagerName(clinicianNames);
						pwcInfo.setCaseManagerId(clinicianList.get(0).getClinicianId());
					}
					log.info(" >>>>>>>>>>>>>>>>>>>>>>>>>>>>> Before data add for Patient :  " + pwcInfo.getPatientId()
							+ " >>>>>>>");
					data.add(pwcInfo);
					caseManagerPatientsAssignVOList.setPatientsInfo(data);
				}

			} else {
				caseManagerPatientsAssignVOList.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "No Patients were assigned to the Case Manager";
				caseManagerPatientsAssignVOList.setMessages(messages);
				return caseManagerPatientsAssignVOList;
			}
		} else {
			caseManagerPatientsAssignVOList.setSuccess(false);
			String messages[] = new String[1];
			messages[0] = "Operation Failed";
			messages[1] = "Case Manager Id is received as Null";
			caseManagerPatientsAssignVOList.setMessages(messages);
			return caseManagerPatientsAssignVOList;
		}
		return caseManagerPatientsAssignVOList;
	}

	@Override
	public WatchrxCaseManagerPatientsAssignmentResponseListVO unAssignCaseManagerToPatients(
			CaseManagerPatientsAssignVO caseManagerPatientsAssignVO) {
		log.info(" >>>>>> Received details : " + caseManagerPatientsAssignVO.getCaseManagerId());
		WatchrxClinician clinician = clinicianDAO.getById(caseManagerPatientsAssignVO.getCaseManagerId());
		List<WatchrxPatientClinicianAssignmnt> watchrxPatientClinicianAssignmntList = patientClinicianAssignmentDAO
				.getAllPatientsForCaseManager(clinician.getClinicianId());

		List<Long> assignedPatients = new ArrayList<Long>();
		List<WatchrxCaseManagerPatientsAssignmentResponseVO> watchrxCaseManagerPatientsAssignmentResponseVOList = new ArrayList<WatchrxCaseManagerPatientsAssignmentResponseVO>();

		for (WatchrxPatientClinicianAssignmnt watchrxPatientClinicianAssignmnt : watchrxPatientClinicianAssignmntList) {
			assignedPatients.add(watchrxPatientClinicianAssignmnt.getWatchrxPatient().getPatientId());
		}

		for (Long patientId : caseManagerPatientsAssignVO.getPatientIds()) {
			boolean alreadyAssigned = false;

			if (assignedPatients.contains(patientId)) {
				alreadyAssigned = true;
			}

			if (alreadyAssigned == true) {
				log.info(" >>>>>> unassigned for patient  : " + patientId);
				WatchrxPatientClinicianAssignmnt caseManagerPatientPair = new WatchrxPatientClinicianAssignmnt();
				caseManagerPatientPair.setWatchrxClinician(clinician);
				WatchrxPatient watchrxPatient = new WatchrxPatient();
				watchrxPatient.setPatientId(patientId);
				caseManagerPatientPair.setWatchrxPatient(watchrxPatient);
				patientClinicianAssignmentDAO.unAssignCaseManagerToPatient(clinician.getClinicianId(), patientId);
				WatchrxCaseManagerPatientsAssignmentResponseVO watchrxCaseManagerPatientsAssignmentResponseVO = new WatchrxCaseManagerPatientsAssignmentResponseVO();
				watchrxCaseManagerPatientsAssignmentResponseVO.setPatientId(patientId);
				watchrxCaseManagerPatientsAssignmentResponseVO.setAlreadyAssigned(true);
				watchrxCaseManagerPatientsAssignmentResponseVOList.add(watchrxCaseManagerPatientsAssignmentResponseVO);
			} else {
				log.info(" >>>>>> already unassigned for patient  : " + patientId);
				WatchrxCaseManagerPatientsAssignmentResponseVO watchrxCaseManagerPatientsAssignmentResponseVO = new WatchrxCaseManagerPatientsAssignmentResponseVO();
				watchrxCaseManagerPatientsAssignmentResponseVO.setPatientId(patientId);
				watchrxCaseManagerPatientsAssignmentResponseVO.setAlreadyAssigned(false);
				watchrxCaseManagerPatientsAssignmentResponseVOList.add(watchrxCaseManagerPatientsAssignmentResponseVO);
			}
		}

		WatchrxCaseManagerPatientsAssignmentResponseListVO watchrxCaseManagerPatientsAssignmentResponseListVO = new WatchrxCaseManagerPatientsAssignmentResponseListVO();
		watchrxCaseManagerPatientsAssignmentResponseListVO.setWatchrxCaseManagerPatientsAssignmentResponseList(
				watchrxCaseManagerPatientsAssignmentResponseVOList);
		watchrxCaseManagerPatientsAssignmentResponseListVO.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		watchrxCaseManagerPatientsAssignmentResponseListVO.setMessages(messages);

		return watchrxCaseManagerPatientsAssignmentResponseListVO;
	}

	@Override
	public WatchrxCaseManagerPatientsAssignmentResponseListVO assignCaseManagerToPatients(
			CaseManagerPatientsAssignVO caseManagerPatientsAssignVO) {
		log.info(" >>>>>> Received details : " + caseManagerPatientsAssignVO.getCaseManagerId());

		WatchrxClinician clinician = clinicianDAO.getById(caseManagerPatientsAssignVO.getCaseManagerId());

		log.info(" >>>>>> Clinician details : " + clinician.getFirstName());
		List<WatchrxPatientClinicianAssignmnt> watchrxPatientClinicianAssignmntList = patientClinicianAssignmentDAO
				.getAllPatientsForCaseManager(clinician.getClinicianId());

		List<Long> assignedPatients = new ArrayList<Long>();
		List<WatchrxCaseManagerPatientsAssignmentResponseVO> watchrxCaseManagerPatientsAssignmentResponseVOList = new ArrayList<WatchrxCaseManagerPatientsAssignmentResponseVO>();

		for (WatchrxPatientClinicianAssignmnt watchrxPatientClinicianAssignmnt : watchrxPatientClinicianAssignmntList) {
			assignedPatients.add(watchrxPatientClinicianAssignmnt.getWatchrxPatient().getPatientId());
		}

		for (Long patientId : caseManagerPatientsAssignVO.getPatientIds()) {
			boolean alreadyAssigned = false;

			if (assignedPatients.contains(patientId)) {
				alreadyAssigned = true;
			}

			if (alreadyAssigned == false) {
				log.info(" >>>>>> for patient  : " + patientId);
				WatchrxPatientClinicianAssignmnt caseManagerPatientPair = new WatchrxPatientClinicianAssignmnt();
				caseManagerPatientPair.setWatchrxClinician(clinician);
				WatchrxPatient watchrxPatient = new WatchrxPatient();
				watchrxPatient.setPatientId(patientId);
				caseManagerPatientPair.setWatchrxPatient(watchrxPatient);
				patientClinicianAssignmentDAO.save(caseManagerPatientPair);
				WatchrxCaseManagerPatientsAssignmentResponseVO watchrxCaseManagerPatientsAssignmentResponseVO = new WatchrxCaseManagerPatientsAssignmentResponseVO();
				watchrxCaseManagerPatientsAssignmentResponseVO.setPatientId(patientId);
				watchrxCaseManagerPatientsAssignmentResponseVO.setAlreadyAssigned(false);
				watchrxCaseManagerPatientsAssignmentResponseVOList.add(watchrxCaseManagerPatientsAssignmentResponseVO);
			} else {
				log.info(" >>>>>> already assigned for patient  : " + patientId);
				WatchrxCaseManagerPatientsAssignmentResponseVO watchrxCaseManagerPatientsAssignmentResponseVO = new WatchrxCaseManagerPatientsAssignmentResponseVO();
				watchrxCaseManagerPatientsAssignmentResponseVO.setPatientId(patientId);
				watchrxCaseManagerPatientsAssignmentResponseVO.setAlreadyAssigned(true);
				watchrxCaseManagerPatientsAssignmentResponseVOList.add(watchrxCaseManagerPatientsAssignmentResponseVO);
			}
		}
		WatchrxCaseManagerPatientsAssignmentResponseListVO watchrxCaseManagerPatientsAssignmentResponseListVO = new WatchrxCaseManagerPatientsAssignmentResponseListVO();
		watchrxCaseManagerPatientsAssignmentResponseListVO.setWatchrxCaseManagerPatientsAssignmentResponseList(
				watchrxCaseManagerPatientsAssignmentResponseVOList);
		watchrxCaseManagerPatientsAssignmentResponseListVO.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		watchrxCaseManagerPatientsAssignmentResponseListVO.setMessages(messages);

		return watchrxCaseManagerPatientsAssignmentResponseListVO;
	}

	@Override
	public WatchrxPhysicianCaseManagerAssignmentResponseListVO assignCaseManagerToPhysician(
			PhysicianCaseManagerAssignVO physicianCaseManagerAssignVO) {
		WatchRxPhysician physician = physicianDAO.getById(physicianCaseManagerAssignVO.getPhysicianId());
		List<WatchrxPhysicianCaseManagerAssignment> watchrxPhysicianCaseManagerAssignmentList = physicianCaseManagerAssignmentDAO
				.findByProperty("watchrxPhysician.physicianId", physicianCaseManagerAssignVO.getPhysicianId());
		List<Long> assignedCaseManagers = new ArrayList<Long>();
		List<WatchrxPhysicianCaseManagerAssignmentResponseVO> watchrxPhysicianCaseManagerAssignmentResponseVOList = new ArrayList<WatchrxPhysicianCaseManagerAssignmentResponseVO>();
		for (WatchrxPhysicianCaseManagerAssignment watchrxPhysicianCaseManagerAssignment : watchrxPhysicianCaseManagerAssignmentList) {
			assignedCaseManagers.add(watchrxPhysicianCaseManagerAssignment.getWatchrxClinician().getClinicianId());
		}
		if (physician != null) {
			for (Long caseManagerId : physicianCaseManagerAssignVO.getCaseManagerIds()) {
				boolean alreadyAssigned = false;

				if (assignedCaseManagers.contains(caseManagerId)) {
					alreadyAssigned = true;
				}

				if (alreadyAssigned == false) {
					WatchrxPhysicianCaseManagerAssignment physicianCaseManagerPair = new WatchrxPhysicianCaseManagerAssignment();
					physicianCaseManagerPair.setWatchrxPhysician(physician);
					WatchrxClinician watchrxClinician = new WatchrxClinician();
					watchrxClinician.setClinicianId(caseManagerId);
					physicianCaseManagerPair.setWatchrxClinician(watchrxClinician);
					physicianCaseManagerAssignmentDAO.save(physicianCaseManagerPair);
					WatchrxPhysicianCaseManagerAssignmentResponseVO watchrxPhysicianCaseManagerAssignmentResponseVO = new WatchrxPhysicianCaseManagerAssignmentResponseVO();
					watchrxPhysicianCaseManagerAssignmentResponseVO.setCasemangerID(caseManagerId);
					watchrxPhysicianCaseManagerAssignmentResponseVO.setAlreadyAssigned(false);
					watchrxPhysicianCaseManagerAssignmentResponseVOList
							.add(watchrxPhysicianCaseManagerAssignmentResponseVO);

				} else {
					WatchrxPhysicianCaseManagerAssignmentResponseVO watchrxPhysicianCaseManagerAssignmentResponseVO = new WatchrxPhysicianCaseManagerAssignmentResponseVO();
					watchrxPhysicianCaseManagerAssignmentResponseVO.setCasemangerID(caseManagerId);
					watchrxPhysicianCaseManagerAssignmentResponseVO.setAlreadyAssigned(true);
					watchrxPhysicianCaseManagerAssignmentResponseVOList
							.add(watchrxPhysicianCaseManagerAssignmentResponseVO);
				}

			}

		} else {
			WatchrxPhysicianCaseManagerAssignmentResponseListVO watchrxPhysicianCaseManagerAssignmentResponseListVO = new WatchrxPhysicianCaseManagerAssignmentResponseListVO();
			watchrxPhysicianCaseManagerAssignmentResponseListVO.setWatchrxPhysicianCaseManagerAssignmentResponseList(
					watchrxPhysicianCaseManagerAssignmentResponseVOList);
			watchrxPhysicianCaseManagerAssignmentResponseListVO.setSuccess(false);
			String messages[] = new String[1];
			messages[0] = "Operation Failed";
			watchrxPhysicianCaseManagerAssignmentResponseListVO.setMessages(messages);
			return watchrxPhysicianCaseManagerAssignmentResponseListVO;
		}
		WatchrxPhysicianCaseManagerAssignmentResponseListVO watchrxPhysicianCaseManagerAssignmentResponseListVO = new WatchrxPhysicianCaseManagerAssignmentResponseListVO();
		watchrxPhysicianCaseManagerAssignmentResponseListVO.setWatchrxPhysicianCaseManagerAssignmentResponseList(
				watchrxPhysicianCaseManagerAssignmentResponseVOList);
		watchrxPhysicianCaseManagerAssignmentResponseListVO.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		watchrxPhysicianCaseManagerAssignmentResponseListVO.setMessages(messages);
		return watchrxPhysicianCaseManagerAssignmentResponseListVO;
	}

	@Override
	public Long getClinicianIdForUserId(Long userId, Long roleType) throws WatchRxServiceException {
		try {
			Long clinicianId = 0l;
			if (roleType == 3) {
				List<WatchRxPhysician> physicians = physicianDAO.findByProperty("watchrxUser.userId", userId);
				if (physicians.size() == 1) {
					clinicianId = physicians.get(0).getPhysicianId();
				}
			} else {
				List<WatchrxClinician> clinicians = clinicianDAO.findByProperty("watchrxUser.userId", userId);

				if (clinicians.size() == 1) {
					clinicianId = clinicians.get(0).getClinicianId();
				}
			}
			return clinicianId;
		} catch (Exception e) {
			throw new WatchRxServiceException("Error while fetching patients.");
		}
	}

	@Override
	public Long getAllRPMCaseManagersCount() {
		return clinicianDAO.getAllUsersByTypeCount(Constants.UserType.CASEMANAGER);
	}

	@Override
	public Long getAllRPMAdminCount() {
		return clinicianDAO.getAllAdminUsersByTypeCount(Constants.UserType.COORDINATOR);
	}

	private PhysicianVO physicianModelToVO(WatchRxPhysician physician) {
		PhysicianVO physicianVO = new PhysicianVO();
		WatchrxAddress address = physician.getWatchrxAddress();
		AddressVO addressVO = new AddressVO();
		if (address != null) {
			addressVO.setAddress1(address.getAddress1());
			addressVO.setAddress2(address.getAddress2());
			addressVO.setCity(address.getCity());
			addressVO.setCountry(address.getCountry());
			physicianVO.setAddress(addressVO);
		} else {
			physicianVO.setAddress(null);
		}
		physicianVO.setFirstName(physician.getFirstName());
		physicianVO.setLastName(physician.getLastName());
		physicianVO.setAltPhoneNumber(physician.getAltPhoneNumber());
		physicianVO.setPhysicianId(physician.getPhysicianId());
		physicianVO.setEmail(physician.getWatchrxUser().getUserName());
		return physicianVO;

	}

	private ClinicianVO clinicianModelToVO(WatchrxClinician clinician) {
		ClinicianVO clinicianVO = new ClinicianVO();
		WatchrxAddress address = clinician.getWatchrxAddress();
		if (address != null) {
			clinicianVO.setAddress1(address.getAddress1());
			clinicianVO.setAddress2(address.getAddress2());
			clinicianVO.setCity(address.getCity());
			clinicianVO.setCountry(address.getCountry());
		}
		clinicianVO.setFirstName(clinician.getFirstName());
		clinicianVO.setLastName(clinician.getLastName());
		clinicianVO.setAltPhoneNumber(clinician.getAltPhoneNumber());
		clinicianVO.setClinicianId(clinician.getClinicianId());
		clinicianVO.setEmail(clinician.getWatchrxUser().getUserName());
		clinicianVO.setName(clinician.getFirstName() + " " + clinician.getLastName());
		return clinicianVO;

	}

	@Override
	public PhysicianListVO getAllPhysiciansByCaseMangerByUserIndex(Long userId, Integer index, Integer pageSize) {
		WatchrxClinician clinician = clinicianDAO.findByProperty("watchrxUser.userId", userId).get(0);
		List<WatchrxPhysicianCaseManagerAssignment> watchrxPhysicianCaseManagerAssignments = physicianCaseManagerAssignmentDAO
				.findByPropertyPaginated("watchrxClinician.clinicianId", clinician.getClinicianId(), index, pageSize);

		List<PhysicianVO> physicianVOList = new ArrayList<PhysicianVO>();

		for (WatchrxPhysicianCaseManagerAssignment watchrxPhysicianCaseManagerAssignment : watchrxPhysicianCaseManagerAssignments) {
			WatchRxPhysician physician = watchrxPhysicianCaseManagerAssignment.getWatchrxPhysician();
			Long patientCount = patientDAO.patientCountPhysicianCaseManager(physician.getPhysicianId(),
					clinician.getClinicianId());
			PhysicianVO physicianVO = physicianModelToVO(physician);
			physicianVO.setPatientCount(patientCount);
			physicianVOList.add(physicianVO);
		}

		PhysicianListVO physicianListVO = new PhysicianListVO();
		physicianListVO.setPhysicianVOList(physicianVOList);
		return physicianListVO;

	}

	@Override
	public CaregiverListVO getAllCaseMangersByPhysicianByUserIndex(Long userId, Integer index, Integer pageSize) {
		WatchRxPhysician physician = physicianDAO.findByProperty("watchrxUser.userId", userId).get(0);
		List<WatchrxPhysicianCaseManagerAssignment> watchrxPhysicianCaseManagerAssignments = physicianCaseManagerAssignmentDAO
				.findByPropertyPaginated("watchrxPhysician.physicianId", physician.getPhysicianId(), index, pageSize);

		List<ClinicianVO> clinicianVOList = new ArrayList<ClinicianVO>();

		for (WatchrxPhysicianCaseManagerAssignment watchrxPhysicianCaseManagerAssignment : watchrxPhysicianCaseManagerAssignments) {
			WatchrxClinician clinician = watchrxPhysicianCaseManagerAssignment.getWatchrxClinician();
			Long patientCount = patientDAO.patientCountPhysicianCaseManager(physician.getPhysicianId(),
					clinician.getClinicianId());
			ClinicianVO clinicianVO = clinicianModelToVO(clinician);
			clinicianVO.setPatientCount(patientCount);
			clinicianVOList.add(clinicianVO);
		}
		CaregiverListVO caregiverListVO = new CaregiverListVO();
		caregiverListVO.setCaregivers(clinicianVOList);
		return caregiverListVO;
	}

	@Override
	public Long getAllPhysiciansByCaseMangerByUserIndexCount(Long userId) {
		WatchrxClinician clinician = clinicianDAO.findByProperty("watchrxUser.userId", userId).get(0);
		return physicianCaseManagerAssignmentDAO.findByPropertyCount("watchrxClinician.clinicianId",
				clinician.getClinicianId());

	}

	@Override
	public Long getAllCaseMangersByPhysicianByUserIndexCount(Long userId) {
		WatchRxPhysician physician = physicianDAO.findByProperty("watchrxUser.userId", userId).get(0);
		return physicianCaseManagerAssignmentDAO.findByPropertyCount("watchrxPhysician.physicianId",
				physician.getPhysicianId());
	}

	@Override
	public PatientMinimalListVO getPatientsByCaseManger(Long userId, Long orgId) {
		log.info("Get Clinician: for User ID" + userId);
		WatchrxClinician clinician = clinicianDAO.findByProperty("watchrxUser.userId", userId).get(0);
		List<WatchrxPatientClinicianAssignmnt> watchrxPatientClinicianAssignmnts = patientClinicianAssignmentDAO
				.findByProperty("watchrxClinician.clinicianId", clinician.getClinicianId());
		PatientMinimalListVO patientMinimalListVO = new PatientMinimalListVO();
		List<PatientMinimalVO> patientMinimalVOList = new ArrayList<PatientMinimalVO>();
		for (WatchrxPatientClinicianAssignmnt watchrxPatientClinicianAssignmnt : watchrxPatientClinicianAssignmnts) {
			PatientMinimalVO patientMinimalVO = new PatientMinimalVO();
			patientMinimalVO.setPatientId(watchrxPatientClinicianAssignmnt.getWatchrxPatient().getPatientId());
			patientMinimalVO.setPatientName(watchrxPatientClinicianAssignmnt.getWatchrxPatient().getFirstName() + " "
					+ watchrxPatientClinicianAssignmnt.getWatchrxPatient().getLastName());
			if (watchrxPatientClinicianAssignmnt.getWatchrxPatient().getWatchrxGroup().getGroupId() == orgId) {
				patientMinimalVOList.add(patientMinimalVO);
			}
		}
		patientMinimalListVO.setPatientMinimalVOList(patientMinimalVOList);
		return patientMinimalListVO;
	}

	@Override
	public boolean saveTask(TasksVO tasksVO) {
		WatchrxPatient patient = patientDAO.getById(tasksVO.getPatientId());
		if (patient != null) {
			WatchRxTasks watchRxTasks = null;
			log.info("Task Id id  = " + tasksVO.getTaskId());
			if (tasksVO.getTaskId() == null) {
				watchRxTasks = new WatchRxTasks();
			} else {
				watchRxTasks = tasksDAO.getById(tasksVO.getTaskId());
			}
			try {
				watchRxTasks.setTaskStartDate(formater.parse(tasksVO.getTaskStartDate()));
				watchRxTasks.setTaskEndDate(
						tasksVO.getTaskEndDate() != null ? formater.parse(tasksVO.getTaskEndDate()) : new Date());
			} catch (ParseException e) {
				e.printStackTrace();
				watchRxTasks.setTaskStartDate(new Date());
				watchRxTasks.setTaskEndDate(new Date());
			}
			if (tasksVO.getTaskStatus() == null) {
				watchRxTasks.setTaskStatus("Open");
			} else {
				watchRxTasks.setTaskStatus(tasksVO.getTaskStatus());
			}
			watchRxTasks.setTaskDesc(tasksVO.getTaskDesc());
			watchRxTasks.setTaskPriority(tasksVO.getTaskPriority());
			watchRxTasks.setTaskTitle(tasksVO.getTaskTitle());

			WatchrxUser watchrxUser = new WatchrxUser();
			watchrxUser.setUserId(tasksVO.getUserId());

			watchRxTasks.setWatchrxUser(watchrxUser);
			watchRxTasks.setWatchrxPatient(patient);

			WatchRxTasks result = tasksDAO.save(watchRxTasks);
			if (result.getTaskId() > 0) {
				return true;
			}
		}
		return false;
	}

	@Override
	public boolean deleteTask(TasksVO tasksVO) {
		WatchRxTasks watchRxTasks = tasksDAO.getById(tasksVO.getTaskId());
		if (watchRxTasks != null) {
			tasksDAO.delete(tasksVO.getTaskId());
			return true;
		}
		return false;
	}

	@Override
	public TaskListVO getDueTasks(TasksVO tasksVO) {
		Date taskStartDate = new Date();
		List<TasksVO> tasksVOList = new ArrayList<TasksVO>();
		Integer roleType = tasksVO.getRoleType();
		Integer index = tasksVO.getIndex();
		Integer pagesize = tasksVO.getPageSize();
		Long orgId = tasksVO.getOrgId();

		List<WatchRxTasks> watchRxTasksList = tasksDAO.getDueTasks(taskStartDate, orgId, roleType, index, pagesize,
				tasksVO.getUserId());
		for (WatchRxTasks watchRxTask : watchRxTasksList) {
			TasksVO task = new TasksVO();
			task.setPatientId(watchRxTask.getWatchrxPatient().getPatientId());
			task.setPatientName(watchRxTask.getWatchrxPatient().getFirstName() + " "
					+ watchRxTask.getWatchrxPatient().getLastName());
			task.setTaskDesc(watchRxTask.getTaskDesc());
			task.setTaskEndDate(formater1.format(watchRxTask.getTaskEndDate()));
			task.setTaskId(watchRxTask.getTaskId());
			task.setTaskPriority(watchRxTask.getTaskPriority());
			task.setTaskStatus(watchRxTask.getTaskStatus());
			task.setTaskStartDate(formater1.format(watchRxTask.getTaskStartDate()));
			task.setTaskTitle(watchRxTask.getTaskTitle());
			tasksVOList.add(task);
		}
		TaskListVO taskListVO = new TaskListVO();
		taskListVO.setTasksVOList(tasksVOList);
		return taskListVO;
	}

	@Override
	public TaskListVO getFutureTasks(TasksVO tasksVO) {
		Date taskStartDate = new Date();
		List<TasksVO> tasksVOList = new ArrayList<TasksVO>();
		Integer roleType = tasksVO.getRoleType();
		Integer index = tasksVO.getIndex();
		Integer pagesize = tasksVO.getPageSize();
		Long orgId = tasksVO.getOrgId();

		List<WatchRxTasks> watchRxTasksList = tasksDAO.getfutureTasks(taskStartDate, orgId, roleType, index, pagesize,
				tasksVO.getUserId());
		for (WatchRxTasks watchRxTask : watchRxTasksList) {
			TasksVO task = new TasksVO();
			task.setPatientId(watchRxTask.getWatchrxPatient().getPatientId());
			task.setPatientName(watchRxTask.getWatchrxPatient().getFirstName() + " "
					+ watchRxTask.getWatchrxPatient().getLastName());
			task.setTaskDesc(watchRxTask.getTaskDesc());
			task.setTaskEndDate(formater1.format(watchRxTask.getTaskEndDate()));
			task.setTaskId(watchRxTask.getTaskId());
			task.setTaskPriority(watchRxTask.getTaskPriority());
			task.setTaskStatus(watchRxTask.getTaskStatus());
			task.setTaskStartDate(formater1.format(watchRxTask.getTaskStartDate()));
			task.setTaskTitle(watchRxTask.getTaskTitle());
			tasksVOList.add(task);
		}
		TaskListVO taskListVO = new TaskListVO();
		taskListVO.setTasksVOList(tasksVOList);
		return taskListVO;
	}

	@Override
	public PatientMinimalListVO getPatientsByPhysician(Long userId, Long orgId) {

		List<WatchrxPatient> patientClinicianPairs = patientDAO.findByProperty("watchrxGroup.groupId", orgId);

		PatientMinimalListVO patientMinimalListVO = new PatientMinimalListVO();
		List<PatientMinimalVO> patientMinimalVOList = new ArrayList<PatientMinimalVO>();
		for (WatchrxPatient watchrxPatient : patientClinicianPairs) {
			PatientMinimalVO patientMinimalVO = new PatientMinimalVO();
			patientMinimalVO.setPatientId(watchrxPatient.getPatientId());
			patientMinimalVO.setPatientName(watchrxPatient.getFirstName() + " " + watchrxPatient.getLastName());
			patientMinimalVOList.add(patientMinimalVO);
		}
		patientMinimalListVO.setPatientMinimalVOList(patientMinimalVOList);
		return patientMinimalListVO;
	}

	@Override
	public Long getDueTasksCount(Long orgId, Integer roleType, Long userId) {
		return tasksDAO.getDueTasksCount(orgId, roleType, userId);
	}

	@Override
	public Long getFutureTasksCount(Long orgId, Integer roleType, Long userId) {
		return tasksDAO.getFutureTasksCount(orgId, roleType, userId);
	}

	@SuppressWarnings("resource")
	@Override
	public PatientCountVO patientCountCaseManager(Long userId, String type) {
		PatientCountVO patientCountVO = new PatientCountVO();
		WatchrxClinician clinician = clinicianDAO.findByProperty("watchrxUser.userId", userId).get(0);
		log.info("patientcountcasemanager");
		Long totalPatientCount = 0L;
		Long totalCallHours = 0L;
		Long totalHouseVisitsCount = 0L;
		if (type.equalsIgnoreCase("year")) {
			log.info("year");
			List<Object[]> keyvalues = patientDAO.patientCountYearCaseManager(clinician.getClinicianId());
			List<Object[]> keyvaluesp = watchRxPatientPhoneCommunicationDAO
					.contactMinutesCountYearCaseManager(clinician.getClinicianId());
			List<Long> counts = new ArrayList<Long>();
			List<Long> callHours = new ArrayList<Long>();
			List<Object[]> keyvaluesh = encountersNewDAO.homeVisitsCountYearCaseManager(clinician.getClinicianId());
			List<Long> homeVisitsCount = new ArrayList<Long>();
			for (long i = 1; i <= Calendar.getInstance().get(Calendar.MONTH) + 1; i++) {
				boolean found = false;
				boolean callhrs = false;
				log.info("inside for outer");
				for (Object[] keyvalue : keyvalues) {
					if (i == (Integer) keyvalue[0]) {
						if (counts.size() > 0) {
							counts.add(counts.get(counts.size() - 1) + (Long) keyvalue[1]);
							totalPatientCount += (Long) keyvalue[1];
							found = true;
							break;
						} else {
							counts.add((Long) keyvalue[1]);
							totalPatientCount += (Long) keyvalue[1];
							found = true;
							break;
						}
					}
				}
				if (!found) {
					if (counts.size() > 0) {
						counts.add(counts.get(counts.size() - 1) + 0L);
					} else {
						counts.add(0L);
					}
				}

				for (Object[] keyvalue : keyvaluesp) {
					if (i == (Integer) keyvalue[0]) {
						if (callHours.size() > 0) {
							callHours.add(callHours.get(callHours.size() - 1) + (Long) keyvalue[1]);
							totalCallHours += (Long) keyvalue[1];
							callhrs = true;
							break;
						} else {
							callHours.add((Long) keyvalue[1]);
							totalCallHours += (Long) keyvalue[1];
							callhrs = true;
							break;
						}
					}
				}
				if (!callhrs) {
					if (callHours.size() > 0) {
						callHours.add(callHours.get(callHours.size() - 1) + 0L);
					} else {
						callHours.add(0L);
					}
				}
				boolean foundhome = false;
				for (Object[] keyvalue : keyvaluesh) {
					if (i == (Integer) keyvalue[0]) {
						if (homeVisitsCount.size() > 0) {
							homeVisitsCount.add(homeVisitsCount.get(homeVisitsCount.size() - 1) + (Long) keyvalue[1]);
							totalHouseVisitsCount += (Long) keyvalue[1];
							foundhome = true;
							break;
						} else {
							homeVisitsCount.add((Long) keyvalue[1]);
							totalHouseVisitsCount += (Long) keyvalue[1];
							foundhome = true;
							break;
						}
					}
				}
				if (!foundhome) {
					if (homeVisitsCount.size() > 0) {
						homeVisitsCount.add(homeVisitsCount.get(homeVisitsCount.size() - 1) + 0L);
					} else {
						homeVisitsCount.add(0L);
					}
				}

			}

			String[] month = { "January", "February", "March", "April", "May", "June", "July", "August", "September",
					"October", "November", "December" };
			List<String> monthList = new ArrayList<String>();
			for (int i = 0; i < Calendar.getInstance().get(Calendar.MONTH) + 1; i++) {
				monthList.add(month[i]);
			}

			patientCountVO.setTotalHomeVisitsCount(totalHouseVisitsCount);
			patientCountVO.setHomeVisits(homeVisitsCount);
			patientCountVO.setMins(callHours);
			patientCountVO.setTotalCountMins(totalCallHours);

			log.info("" + totalPatientCount);
			patientCountVO.setTotalPatientCount(totalPatientCount);
			patientCountVO.setCounts(counts);
			patientCountVO.setMonths(monthList);
		} else if (type.equalsIgnoreCase("month")) {
			log.info("month");
			List<Object[]> keyvalues = patientDAO.patientCountMonthCaseManager(clinician.getClinicianId());
			List<Long> counts = new ArrayList<Long>();
			List<String> dateList = new ArrayList<String>();
			List<Object[]> keyvaluesp = watchRxPatientPhoneCommunicationDAO
					.contactMinutesCountYearCaseManager(clinician.getClinicianId());
			List<Long> callHours = new ArrayList<Long>();
			List<Object[]> keyvaluesh = encountersNewDAO.homeVisitsCountMonthCaseManager(clinician.getClinicianId());
			List<Long> homeVisitsCount = new ArrayList<Long>();
			for (long i = 1; i <= Calendar.getInstance().get(Calendar.DAY_OF_MONTH) + 1; i++) {
				boolean found = false;
				boolean callhrs = false;
				for (Object[] keyvalue : keyvalues) {
					if (i == (Integer) keyvalue[0]) {
						if (counts.size() > 0) {
							counts.add(counts.get(counts.size() - 1) + (Long) keyvalue[1]);
							totalPatientCount += (Long) keyvalue[1];
							found = true;
							break;
						} else {
							counts.add((Long) keyvalue[1]);
							totalPatientCount += (Long) keyvalue[1];
							found = true;
							break;
						}
					}
				}
				if (!found) {
					if (counts.size() > 0) {
						counts.add(counts.get(counts.size() - 1) + 0L);
					} else {
						counts.add(0L);
					}
				}

				for (Object[] keyvalue : keyvaluesp) {
					if (i == (Integer) keyvalue[0]) {
						if (callHours.size() > 0) {
							callHours.add(callHours.get(callHours.size() - 1) + (Long) keyvalue[1]);
							totalCallHours += (Long) keyvalue[1];
							callhrs = true;
							break;
						} else {
							callHours.add((Long) keyvalue[1]);
							totalCallHours += (Long) keyvalue[1];
							callhrs = true;
							break;
						}
					}
				}
				if (!callhrs) {
					if (callHours.size() > 0) {
						callHours.add(callHours.get(callHours.size() - 1) + 0L);
					} else {
						callHours.add(0L);
					}
				}
				boolean foundhome = false;
				for (Object[] keyvalue : keyvaluesh) {
					if (i == (Integer) keyvalue[0]) {
						if (homeVisitsCount.size() > 0) {
							homeVisitsCount.add(homeVisitsCount.get(homeVisitsCount.size() - 1) + (Long) keyvalue[1]);
							totalHouseVisitsCount += (Long) keyvalue[1];
							foundhome = true;
							break;
						} else {
							homeVisitsCount.add((Long) keyvalue[1]);
							totalHouseVisitsCount += (Long) keyvalue[1];
							foundhome = true;
							break;
						}
					}
				}
				if (!foundhome) {
					if (homeVisitsCount.size() > 0) {
						homeVisitsCount.add(homeVisitsCount.get(homeVisitsCount.size() - 1) + 0L);
					} else {
						homeVisitsCount.add(0L);
					}
				}
				Calendar cal = Calendar.getInstance();
				dateList.add("" + new Formatter().format("%tb", cal) + " " + i);
//				dateList.add("" + i + "/" + (Calendar.getInstance().get(Calendar.MONTH) + 1) + "/"
//						+ (Calendar.getInstance().get(Calendar.YEAR) % 100));
			}

			patientCountVO.setTotalHomeVisitsCount(totalHouseVisitsCount);
			patientCountVO.setHomeVisits(homeVisitsCount);
			log.info("" + totalPatientCount);
			patientCountVO.setTotalPatientCount(totalPatientCount);
			patientCountVO.setMins(callHours);
			patientCountVO.setTotalCountMins(totalCallHours);
			patientCountVO.setCounts(counts);
			patientCountVO.setDates(dateList);

		} else if (type.equalsIgnoreCase("week")) {

			log.info("week");
			List<Object[]> keyvalues = patientDAO.patientCountWeekCaseManager(clinician.getClinicianId());
			List<Long> counts = new ArrayList<Long>();
			List<String> dateList = new ArrayList<String>();
			List<Object[]> keyvaluesp = watchRxPatientPhoneCommunicationDAO
					.contactMinutesCountYearCaseManager(clinician.getClinicianId());
			List<Long> callHours = new ArrayList<Long>();
			List<Object[]> keyvaluesh = encountersNewDAO.homeVisitsCountWeekCaseManager(clinician.getClinicianId());
			List<Long> homeVisitsCount = new ArrayList<Long>();
			for (long i = 1; i <= Calendar.getInstance().get(Calendar.DAY_OF_WEEK); i++) {
				boolean found = false;
				for (Object[] keyvalue : keyvalues) {
					if (i == (Integer) keyvalue[0]) {
						if (counts.size() > 0) {
							counts.add(counts.get(counts.size() - 1) + (Long) keyvalue[1]);
							totalPatientCount += (Long) keyvalue[1];
							found = true;
							break;
						} else {
							counts.add((Long) keyvalue[1]);
							totalPatientCount += (Long) keyvalue[1];
							found = true;
							break;
						}
					}
				}
				if (!found) {
					if (counts.size() > 0) {
						counts.add(counts.get(counts.size() - 1) + 0L);
					} else {
						counts.add(0L);
					}
				}

				boolean callhrs = false;
				for (Object[] keyvalue : keyvaluesp) {
					if (i == (Integer) keyvalue[0]) {
						if (callHours.size() > 0) {
							callHours.add(callHours.get(callHours.size() - 1) + (Long) keyvalue[1]);
							totalCallHours += (Long) keyvalue[1];
							callhrs = true;
							break;
						} else {
							callHours.add((Long) keyvalue[1]);
							totalCallHours += (Long) keyvalue[1];
							callhrs = true;
							break;
						}
					}
				}
				if (!callhrs) {
					if (callHours.size() > 0) {
						callHours.add(callHours.get(callHours.size() - 1) + 0L);
					} else {
						callHours.add(0L);
					}
				}

				boolean foundhome = false;
				for (Object[] keyvalue : keyvaluesh) {
					if (i == (Integer) keyvalue[0]) {
						if (homeVisitsCount.size() > 0) {
							homeVisitsCount.add(homeVisitsCount.get(homeVisitsCount.size() - 1) + (Long) keyvalue[1]);
							totalHouseVisitsCount += (Long) keyvalue[1];
							foundhome = true;
							break;
						} else {
							homeVisitsCount.add((Long) keyvalue[1]);
							totalHouseVisitsCount += (Long) keyvalue[1];
							foundhome = true;
							break;
						}
					}
				}
				if (!foundhome) {
					if (homeVisitsCount.size() > 0) {
						homeVisitsCount.add(homeVisitsCount.get(homeVisitsCount.size() - 1) + 0L);
					} else {
						homeVisitsCount.add(0L);
					}
				}

			}
			String[] day = { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };
			List<String> dayList = new ArrayList<String>();
			for (int i = 1; i <= Calendar.getInstance().get(Calendar.DAY_OF_WEEK); i++) {
				dayList.add(day[i]);
			}
			patientCountVO.setTotalHomeVisitsCount(totalHouseVisitsCount);
			patientCountVO.setHomeVisits(homeVisitsCount);
			patientCountVO.setMins(callHours);
			patientCountVO.setTotalPatientCount(totalPatientCount);
			patientCountVO.setTotalCountMins(totalCallHours);
			patientCountVO.setCounts(counts);
			patientCountVO.setDates(dateList);
			patientCountVO.setDays(dayList);
		}

		return patientCountVO;
	}

	@SuppressWarnings("resource")
	@Override
	public PatientCountVO patientCountPhysician(Long userId, String type) {
		PatientCountVO patientCountVO = new PatientCountVO();
		log.info("" + userId);
		WatchRxPhysician physician = physicianDAO.findByProperty("watchrxUser.userId", userId).get(0);
		log.info("patientcountphysician");
		Long totalPatientCount = 0L;
		Long totalCallHours = 0L;
		Long totalHouseVisitsCount = 0L;
		if (type.equalsIgnoreCase("year")) {
			log.info("year");
			List<Object[]> keyvalues = patientDAO.patientCountYearPhysician(physician.getPhysicianId());
			List<Long> counts = new ArrayList<Long>();
			List<Object[]> keyvaluesp = watchRxPatientPhoneCommunicationDAO
					.contactMinutesCountYearPhysician(physician.getPhysicianId());
			List<Long> callHours = new ArrayList<Long>();
			List<Object[]> keyvaluesh = encountersNewDAO.homeVisitsCountYearPhysician(physician.getPhysicianId());
			List<Long> homeVisitsCount = new ArrayList<Long>();
			for (long i = 1; i <= Calendar.getInstance().get(Calendar.MONTH) + 1; i++) {
				boolean found = false;
				for (Object[] keyvalue : keyvalues) {
					if (i == (Integer) keyvalue[0]) {
						if (counts.size() > 0) {
							counts.add(counts.get(counts.size() - 1) + (Long) keyvalue[1]);
							totalPatientCount += (Long) keyvalue[1];
							found = true;
							break;
						} else {
							counts.add((Long) keyvalue[1]);
							totalPatientCount += (Long) keyvalue[1];
							found = true;
							break;
						}
					}
				}
				if (!found) {
					if (counts.size() > 0) {
						counts.add(counts.get(counts.size() - 1) + 0L);
					} else {
						counts.add(0L);
					}
				}

				boolean callhrs = false;
				for (Object[] keyvalue : keyvaluesp) {
					if (i == (Integer) keyvalue[0]) {
						if (callHours.size() > 0) {
							callHours.add(callHours.get(callHours.size() - 1) + (Long) keyvalue[1]);
							totalCallHours += (Long) keyvalue[1];
							callhrs = true;
							break;
						} else {
							callHours.add((Long) keyvalue[1]);
							totalCallHours += (Long) keyvalue[1];
							callhrs = true;
							break;
						}
					}
				}
				if (!callhrs) {
					if (callHours.size() > 0) {
						callHours.add(callHours.get(callHours.size() - 1) + 0L);
					} else {
						callHours.add(0L);
					}
				}
				boolean foundhome = false;
				for (Object[] keyvalue : keyvaluesh) {
					if (i == (Integer) keyvalue[0]) {
						if (homeVisitsCount.size() > 0) {
							homeVisitsCount.add(homeVisitsCount.get(homeVisitsCount.size() - 1) + (Long) keyvalue[1]);
							totalHouseVisitsCount += (Long) keyvalue[1];
							foundhome = true;
							break;
						} else {
							homeVisitsCount.add((Long) keyvalue[1]);
							totalHouseVisitsCount += (Long) keyvalue[1];
							foundhome = true;
							break;
						}
					}
				}
				if (!foundhome) {
					if (homeVisitsCount.size() > 0) {
						homeVisitsCount.add(homeVisitsCount.get(homeVisitsCount.size() - 1) + 0L);
					} else {
						homeVisitsCount.add(0L);
					}
				}
			}
			String[] month = { "January", "February", "March", "April", "May", "June", "July", "August", "September",
					"October", "November", "December" };
			List<String> monthList = new ArrayList<String>();
			for (int i = 0; i < Calendar.getInstance().get(Calendar.MONTH) + 1; i++) {
				monthList.add(month[i]);
			}
			patientCountVO.setTotalHomeVisitsCount(totalHouseVisitsCount);
			patientCountVO.setHomeVisits(homeVisitsCount);
			patientCountVO.setMins(callHours);
			patientCountVO.setTotalCountMins(totalCallHours);
			patientCountVO.setCounts(counts);
			patientCountVO.setMonths(monthList);
			patientCountVO.setTotalPatientCount(totalPatientCount);
			log.info("" + totalPatientCount);
		} else if (type.equalsIgnoreCase("month")) {
			log.info("month");
			List<Object[]> keyvalues = patientDAO.patientCountMonthPhysician(physician.getPhysicianId());
			List<Long> counts = new ArrayList<Long>();
			List<String> dateList = new ArrayList<String>();
			List<Object[]> keyvaluesp = watchRxPatientPhoneCommunicationDAO
					.contactMinutesCountYearPhysician(physician.getPhysicianId());
			List<Long> callHours = new ArrayList<Long>();
			List<Object[]> keyvaluesh = encountersNewDAO.homeVisitsCountMonthPhysician(physician.getPhysicianId());
			List<Long> homeVisitsCount = new ArrayList<Long>();
			for (long i = 1; i <= Calendar.getInstance().get(Calendar.DAY_OF_MONTH); i++) {
				boolean found = false;
				for (Object[] keyvalue : keyvalues) {
					if (i == (Integer) keyvalue[0]) {
						if (counts.size() > 0) {
							counts.add(counts.get(counts.size() - 1) + (Long) keyvalue[1]);
							totalPatientCount += (Long) keyvalue[1];
							found = true;
							break;
						} else {
							counts.add((Long) keyvalue[1]);
							totalPatientCount += (Long) keyvalue[1];
							found = true;
							break;
						}
					}
				}
				if (!found) {
					if (counts.size() > 0) {
						counts.add(counts.get(counts.size() - 1) + 0L);
					} else {
						counts.add(0L);
					}
				}

				boolean callhrs = false;
				for (Object[] keyvalue : keyvaluesp) {
					if (i == (Integer) keyvalue[0]) {
						if (callHours.size() > 0) {
							callHours.add(callHours.get(callHours.size() - 1) + (Long) keyvalue[1]);
							totalCallHours += (Long) keyvalue[1];
							callhrs = true;
							break;
						} else {
							callHours.add((Long) keyvalue[1]);
							totalCallHours += (Long) keyvalue[1];
							callhrs = true;
							break;
						}
					}
				}
				if (!callhrs) {
					if (callHours.size() > 0) {
						callHours.add(callHours.get(callHours.size() - 1) + 0L);
					} else {
						callHours.add(0L);
					}
				}

				boolean foundhome = false;
				for (Object[] keyvalue : keyvaluesh) {
					if (i == (Integer) keyvalue[0]) {
						if (homeVisitsCount.size() > 0) {
							homeVisitsCount.add(homeVisitsCount.get(homeVisitsCount.size() - 1) + (Long) keyvalue[1]);
							totalHouseVisitsCount += (Long) keyvalue[1];
							foundhome = true;
							break;
						} else {
							homeVisitsCount.add((Long) keyvalue[1]);
							totalHouseVisitsCount += (Long) keyvalue[1];
							foundhome = true;
							break;
						}
					}
				}
				if (!foundhome) {
					if (homeVisitsCount.size() > 0) {
						homeVisitsCount.add(homeVisitsCount.get(homeVisitsCount.size() - 1) + 0L);
					} else {
						homeVisitsCount.add(0L);
					}
				}
				Calendar cal = Calendar.getInstance();
				dateList.add("" + new Formatter().format("%tb", cal) + " " + i);
			}
			patientCountVO.setTotalHomeVisitsCount(totalHouseVisitsCount);
			patientCountVO.setHomeVisits(homeVisitsCount);
			patientCountVO.setMins(callHours);
			patientCountVO.setTotalCountMins(totalCallHours);
			log.info("" + totalPatientCount);
			patientCountVO.setTotalPatientCount(totalPatientCount);

			patientCountVO.setCounts(counts);
			patientCountVO.setDates(dateList);
		} else if (type.equalsIgnoreCase("week")) {

			log.info("week");
			List<Object[]> keyvalues = patientDAO.patientCountWeekPhysician(physician.getPhysicianId());
			List<Long> counts = new ArrayList<Long>();
			List<String> dateList = new ArrayList<String>();
			List<Object[]> keyvaluesp = watchRxPatientPhoneCommunicationDAO
					.contactMinutesCountWeekPhysician(physician.getPhysicianId());
			List<Long> callHours = new ArrayList<Long>();
			List<Object[]> keyvaluesh = encountersNewDAO.homeVisitsCountWeekPhysician(physician.getPhysicianId());
			List<Long> homeVisitsCount = new ArrayList<Long>();
			for (long i = 1; i <= Calendar.getInstance().get(Calendar.DAY_OF_WEEK); i++) {
				boolean found = false;
				for (Object[] keyvalue : keyvalues) {
					if (i == (Integer) keyvalue[0]) {
						if (counts.size() > 0) {
							counts.add(counts.get(counts.size() - 1) + (Long) keyvalue[1]);
							totalPatientCount += (Long) keyvalue[1];
							found = true;
							break;
						} else {
							counts.add((Long) keyvalue[1]);
							totalPatientCount += (Long) keyvalue[1];
							found = true;
							break;
						}
					}
				}
				if (!found) {
					if (counts.size() > 0) {
						counts.add(counts.get(counts.size() - 1) + 0L);
					} else {
						counts.add(0L);
					}
				}
				boolean callhrs = false;
				for (Object[] keyvalue : keyvaluesp) {
					if (i == (Integer) keyvalue[0]) {
						if (callHours.size() > 0) {
							callHours.add(callHours.get(callHours.size() - 1) + (Long) keyvalue[1]);
							totalCallHours += (Long) keyvalue[1];
							callhrs = true;
							break;
						} else {
							callHours.add((Long) keyvalue[1]);
							totalCallHours += (Long) keyvalue[1];
							callhrs = true;
							break;
						}
					}
				}
				if (!callhrs) {
					if (callHours.size() > 0) {
						callHours.add(callHours.get(callHours.size() - 1) + 0L);
					} else {
						callHours.add(0L);
					}
				}
				boolean foundhome = false;
				for (Object[] keyvalue : keyvaluesh) {
					if (i == (Integer) keyvalue[0]) {
						if (homeVisitsCount.size() > 0) {
							homeVisitsCount.add(homeVisitsCount.get(homeVisitsCount.size() - 1) + (Long) keyvalue[1]);
							totalHouseVisitsCount += (Long) keyvalue[1];
							foundhome = true;
							break;
						} else {
							homeVisitsCount.add((Long) keyvalue[1]);
							totalHouseVisitsCount += (Long) keyvalue[1];
							foundhome = true;
							break;
						}
					}
				}
				if (!foundhome) {
					if (homeVisitsCount.size() > 0) {
						homeVisitsCount.add(homeVisitsCount.get(homeVisitsCount.size() - 1) + 0L);
					} else {
						homeVisitsCount.add(0L);
					}
				}
			}

			String[] day = { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };
			List<String> dayList = new ArrayList<String>();
			for (int i = 1; i <= Calendar.getInstance().get(Calendar.DAY_OF_WEEK); i++) {
				dayList.add(day[i]);
			}

			patientCountVO.setTotalHomeVisitsCount(totalHouseVisitsCount);
			patientCountVO.setHomeVisits(homeVisitsCount);
			patientCountVO.setTotalCountMins(totalCallHours);
			patientCountVO.setMins(callHours);
			patientCountVO.setTotalCountMins(totalCallHours);
			log.info("" + totalPatientCount);
			patientCountVO.setTotalPatientCount(totalPatientCount);
			patientCountVO.setDays(dayList);
			patientCountVO.setCounts(counts);
			patientCountVO.setDates(dateList);

		}
		return patientCountVO;
	}

	@SuppressWarnings("resource")
	@Override
	public CriticalAlertsCountVO criticalAlertsCountCaseManager(Long userId, String type) {
		CriticalAlertsCountVO criticalAlertsCountVO = new CriticalAlertsCountVO();
		WatchrxClinician clinician = clinicianDAO.findByProperty("watchrxUser.userId", userId).get(0);
		log.info("patientcountcasemanager");
		Long totalCriticalAlertsCount = 0L;
		if (type.equalsIgnoreCase("year")) {
			log.info("year");
			List<Object[]> keyvalues = patientAlertDAO.criticalAlertsCountYearCaseManager(clinician.getClinicianId());
			List<Long> counts = new ArrayList<Long>();
			for (long i = 1; i <= Calendar.getInstance().get(Calendar.MONTH) + 1; i++) {
				boolean found = false;
				log.info("inside for outer");
				for (Object[] keyvalue : keyvalues) {
					if (i == (Integer) keyvalue[0]) {
						if (counts.size() > 0) {
							counts.add(counts.get(counts.size() - 1) + (Long) keyvalue[1]);
							totalCriticalAlertsCount += (Long) keyvalue[1];
							found = true;
							break;
						} else {
							counts.add((Long) keyvalue[1]);
							totalCriticalAlertsCount += (Long) keyvalue[1];
							found = true;
							break;
						}
					}
				}
				if (!found) {
					if (counts.size() > 0) {
						counts.add(counts.get(counts.size() - 1) + 0L);
					} else {
						counts.add(0L);
					}
				}

			}

			String[] month = { "January", "February", "March", "April", "May", "June", "July", "August", "September",
					"October", "November", "December" };
			List<String> monthList = new ArrayList<String>();
			for (int i = 0; i < Calendar.getInstance().get(Calendar.MONTH) + 1; i++) {
				monthList.add(month[i]);
			}

			criticalAlertsCountVO.setTotalCriticalAlertsCount(totalCriticalAlertsCount);
			criticalAlertsCountVO.setCounts(counts);
			criticalAlertsCountVO.setMonths(monthList);
		} else if (type.equalsIgnoreCase("month")) {
			log.info("month");
			List<Object[]> keyvalues = patientAlertDAO.criticalAlertsCountMonthCaseManager(clinician.getClinicianId());
			List<Long> counts = new ArrayList<Long>();
			List<String> dateList = new ArrayList<String>();
			for (long i = 1; i <= Calendar.getInstance().get(Calendar.DAY_OF_MONTH) + 1; i++) {
				boolean found = false;
				for (Object[] keyvalue : keyvalues) {
					if (i == (Integer) keyvalue[0]) {
						if (counts.size() > 0) {
							counts.add(counts.get(counts.size() - 1) + (Long) keyvalue[1]);
							totalCriticalAlertsCount += (Long) keyvalue[1];
							found = true;
							break;
						} else {
							counts.add((Long) keyvalue[1]);
							totalCriticalAlertsCount += (Long) keyvalue[1];
							found = true;
							break;
						}
					}
				}
				if (!found) {
					if (counts.size() > 0) {
						counts.add(counts.get(counts.size() - 1) + 0L);
					} else {
						counts.add(0L);
					}
				}

				Calendar cal = Calendar.getInstance();
				dateList.add("" + new Formatter().format("%tb", cal) + " " + i);
//				dateList.add("" + i + "/" + (Calendar.getInstance().get(Calendar.MONTH) + 1) + "/"
//						+ (Calendar.getInstance().get(Calendar.YEAR) % 100));
			}

			criticalAlertsCountVO.setTotalCriticalAlertsCount(totalCriticalAlertsCount);
			criticalAlertsCountVO.setCounts(counts);
			criticalAlertsCountVO.setDates(dateList);

		} else if (type.equalsIgnoreCase("week")) {

			log.info("week");
			List<Object[]> keyvalues = patientAlertDAO.criticalAlertsCountWeekCaseManager(clinician.getClinicianId());
			List<Long> counts = new ArrayList<Long>();
			for (long i = 1; i <= Calendar.getInstance().get(Calendar.DAY_OF_WEEK); i++) {
				boolean found = false;
				for (Object[] keyvalue : keyvalues) {
					if (i == (Integer) keyvalue[0]) {
						if (counts.size() > 0) {
							counts.add(counts.get(counts.size() - 1) + (Long) keyvalue[1]);
							totalCriticalAlertsCount += (Long) keyvalue[1];
							found = true;
							break;
						} else {
							counts.add((Long) keyvalue[1]);
							totalCriticalAlertsCount += (Long) keyvalue[1];
							found = true;
							break;
						}
					}
				}
				if (!found) {
					if (counts.size() > 0) {
						counts.add(counts.get(counts.size() - 1) + 0L);
					} else {
						counts.add(0L);
					}
				}

			}
			String[] day = { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };
			List<String> dayList = new ArrayList<String>();
			for (int i = 1; i <= Calendar.getInstance().get(Calendar.DAY_OF_WEEK); i++) {
				dayList.add(day[i]);
			}
			criticalAlertsCountVO.setTotalCriticalAlertsCount(totalCriticalAlertsCount);
			criticalAlertsCountVO.setCounts(counts);
			criticalAlertsCountVO.setDays(dayList);
		}

		return criticalAlertsCountVO;
	}

	@SuppressWarnings("resource")
	@Override
	public CriticalAlertsCountVO criticalAlertsCountPhysician(Long userId, String type) {
		CriticalAlertsCountVO criticalAlertsCountVO = new CriticalAlertsCountVO();
		log.info("" + userId);
		WatchRxPhysician physician = physicianDAO.findByProperty("watchrxUser.userId", userId).get(0);
		log.info("patientcountphysician");
		Long totalCriticalAlertCount = 0L;
		if (type.equalsIgnoreCase("year")) {
			log.info("year");
			List<Object[]> keyvalues = patientAlertDAO.criticalAlertsCountYearPhysician(physician.getPhysicianId());
			List<Long> counts = new ArrayList<Long>();
			for (long i = 1; i <= Calendar.getInstance().get(Calendar.MONTH) + 1; i++) {
				boolean found = false;
				for (Object[] keyvalue : keyvalues) {
					if (i == (Integer) keyvalue[0]) {
						if (counts.size() > 0) {
							counts.add(counts.get(counts.size() - 1) + (Long) keyvalue[1]);
							totalCriticalAlertCount += (Long) keyvalue[1];
							found = true;
							break;
						} else {
							counts.add((Long) keyvalue[1]);
							totalCriticalAlertCount += (Long) keyvalue[1];
							found = true;
							break;
						}
					}
				}
				if (!found) {
					if (counts.size() > 0) {
						counts.add(counts.get(counts.size() - 1) + 0L);
					} else {
						counts.add(0L);
					}
				}

			}
			String[] month = { "January", "February", "March", "April", "May", "June", "July", "August", "September",
					"October", "November", "December" };
			List<String> monthList = new ArrayList<String>();
			for (int i = 0; i < Calendar.getInstance().get(Calendar.MONTH) + 1; i++) {
				monthList.add(month[i]);
			}
			criticalAlertsCountVO.setTotalCriticalAlertsCount(totalCriticalAlertCount);
			criticalAlertsCountVO.setCounts(counts);
			criticalAlertsCountVO.setMonths(monthList);
		} else if (type.equalsIgnoreCase("month")) {
			log.info("month");
			List<Object[]> keyvalues = patientAlertDAO.criticalAlertsCountMonthPhysician(physician.getPhysicianId());
			List<Long> counts = new ArrayList<Long>();
			List<String> dateList = new ArrayList<String>();
			for (long i = 1; i <= Calendar.getInstance().get(Calendar.DAY_OF_MONTH); i++) {
				boolean found = false;
				for (Object[] keyvalue : keyvalues) {
					if (i == (Integer) keyvalue[0]) {
						if (counts.size() > 0) {
							counts.add(counts.get(counts.size() - 1) + (Long) keyvalue[1]);
							totalCriticalAlertCount += (Long) keyvalue[1];
							found = true;
							break;
						} else {
							counts.add((Long) keyvalue[1]);
							totalCriticalAlertCount += (Long) keyvalue[1];
							found = true;
							break;
						}
					}
				}
				if (!found) {
					if (counts.size() > 0) {
						counts.add(counts.get(counts.size() - 1) + 0L);
					} else {
						counts.add(0L);
					}
				}

				Calendar cal = Calendar.getInstance();
				dateList.add("" + new Formatter().format("%tb", cal) + " " + i);
			}
			criticalAlertsCountVO.setTotalCriticalAlertsCount(totalCriticalAlertCount);
			criticalAlertsCountVO.setCounts(counts);
			criticalAlertsCountVO.setDates(dateList);
		} else if (type.equalsIgnoreCase("week")) {

			log.info("week");
			List<Object[]> keyvalues = patientAlertDAO.criticalAlertsCountWeekPhysician(physician.getPhysicianId());
			List<Long> counts = new ArrayList<Long>();
			for (long i = 1; i <= Calendar.getInstance().get(Calendar.DAY_OF_WEEK); i++) {
				boolean found = false;
				for (Object[] keyvalue : keyvalues) {
					if (i == (Integer) keyvalue[0]) {
						if (counts.size() > 0) {
							counts.add(counts.get(counts.size() - 1) + (Long) keyvalue[1]);
							totalCriticalAlertCount += (Long) keyvalue[1];
							found = true;
							break;
						} else {
							counts.add((Long) keyvalue[1]);
							totalCriticalAlertCount += (Long) keyvalue[1];
							found = true;
							break;
						}
					}
				}
				if (!found) {
					if (counts.size() > 0) {
						counts.add(counts.get(counts.size() - 1) + 0L);
					} else {
						counts.add(0L);
					}
				}
			}

			String[] day = { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };
			List<String> dayList = new ArrayList<String>();
			for (int i = 1; i <= Calendar.getInstance().get(Calendar.DAY_OF_WEEK); i++) {
				dayList.add(day[i]);
			}

			log.info("" + totalCriticalAlertCount);
			criticalAlertsCountVO.setTotalCriticalAlertsCount(totalCriticalAlertCount);
			criticalAlertsCountVO.setDays(dayList);
			criticalAlertsCountVO.setCounts(counts);

		}
		return criticalAlertsCountVO;
	}

	@Override
	public void savePhysician(ClinicianVO clinicianVO) {

		WatchRxPhysician watchrxClinician = null;
		WatchrxAddress watchrxAddress = null;
		WatchrxUser watchrxUser = null;

		if (clinicianVO.getClinicianId() != null) {
			watchrxClinician = physicianDAO.getById(clinicianVO.getClinicianId());
			watchrxAddress = watchrxClinician.getWatchrxAddress();
			watchrxUser = watchrxClinician.getWatchrxUser();
		} else {
			watchrxClinician = new WatchRxPhysician();
			watchrxAddress = new WatchrxAddress();
			watchrxUser = new WatchrxUser();
		}
		if (clinicianVO.getAddress() != null) {
			if (watchrxAddress == null) {
				watchrxAddress = new WatchrxAddress();
			}
			watchrxAddress.setAddress1(clinicianVO.getAddress().getAddress1());
			watchrxAddress.setAddress2(clinicianVO.getAddress().getAddress2());
			watchrxAddress.setState(clinicianVO.getAddress().getState());
			watchrxAddress.setCity(clinicianVO.getAddress().getCity());
			watchrxAddress.setZip(clinicianVO.getAddress().getZip());
			watchrxAddress.setCountry(clinicianVO.getAddress().getCountry());
			watchrxAddress = addressDAO.save(watchrxAddress);
		}
		watchrxUser.setUserName(clinicianVO.getUserName());
		watchrxUser.setEmail(clinicianVO.getUserName());
		watchrxUser.setPassword(clinicianVO.getPassword());
		watchrxUser.setUserType(clinicianVO.getRoleType());
		watchrxUser.setCreatedDate(new Date());
		watchrxUser.setUpdatedDate(new Date());

		if (clinicianVO.isFileModified()) {
			watchrxUser.setImgPath(clinicianVO.getPicPath());

		}
		watchrxUser.setFirstName(clinicianVO.getFirstName());
		watchrxUser.setLastName(clinicianVO.getLastName());
		watchrxUser = userDAO.save(watchrxUser);
		watchrxClinician.setWatchrxUser(watchrxUser);
		if (watchrxAddress != null) {
			watchrxClinician.setWatchrxAddress(watchrxAddress);
		}
		watchrxClinician.setFirstName(clinicianVO.getFirstName());
		watchrxClinician.setAltPhoneNumber(clinicianVO.getAltPhoneNumber());
		watchrxClinician.setHospitalName(clinicianVO.getHospital());
		watchrxClinician.setLastName(clinicianVO.getLastName());
		watchrxClinician.setPhoneNumber(clinicianVO.getPhoneNumber());

		watchrxClinician.setShiftId(clinicianVO.getShift());
		watchrxClinician.setStatus(Constants.Status.ACTIVE);
		watchrxClinician.setCreatedDate(new Date());
		watchrxClinician.setUpdatedDate(new Date());
		watchrxClinician = physicianDAO.save(watchrxClinician);
	}

	@Override
	public LoggedUserNotificationVO getUserNotificationCount(Long userId, Integer roleType) {

		Long count = 0L;
		LoggedUserNotificationVO vo = new LoggedUserNotificationVO();
		if (roleType == 3) {
			WatchRxPhysician physician = physicianDAO.findByProperty("watchrxUser.userId", userId).get(0);
			count = physicianCaseManagerAssignmentDAO.findByPropertyCount("watchrxPhysician.physicianId",
					physician.getPhysicianId());
			vo.setNoOfcaseManagers(count);
		} else if (roleType == 5) {
			WatchrxClinician clinician = clinicianDAO.findByProperty("watchrxUser.userId", userId).get(0);
			count = physicianCaseManagerAssignmentDAO.findByPropertyCount("watchrxClinician.clinicianId",
					clinician.getClinicianId());
			vo.setNoOfPhysicians(count);
		} else {
			vo.setNoOfcaseManagers(count);
			vo.setNoOfPhysicians(count);
		}
		vo.setUnReadEmails(emailReceiversDAO.getAllUnReadEmailsCount(userId));
		return vo;
	}

	@Override
	public List<CareGiverMinimalVO> getAllCaregiversMinimal() {
		List<WatchrxClinician> cliniciansList = clinicianDAO.getAllUsersByType(4);
		;
		List<CareGiverMinimalVO> careGiverMinimalVOList = new ArrayList<CareGiverMinimalVO>();
		for (WatchrxClinician watchrxClinician : cliniciansList) {
			CareGiverMinimalVO careGiverMinimalVO = new CareGiverMinimalVO();
			careGiverMinimalVO.setCareGiverFirstName(watchrxClinician.getFirstName());
			careGiverMinimalVO.setCareGiverLastName(watchrxClinician.getLastName());
			careGiverMinimalVO.setCareGiverId(watchrxClinician.getClinicianId());
			careGiverMinimalVO.setPhoneNumber(watchrxClinician.getPhoneNumber());
			careGiverMinimalVO.setCareGiverEmail(watchrxClinician.getWatchrxUser().getUserName());
			careGiverMinimalVOList.add(careGiverMinimalVO);
		}
		return careGiverMinimalVOList;
	}

	@Override
	public boolean assignPatientToCareGiver(AssignPatientsToCareGiverVO assignPatientsToCareGiverVO) {
		WatchrxClinician clinician = new WatchrxClinician();
		clinician.setClinicianId(assignPatientsToCareGiverVO.getCareGiverId());
		List<WatchrxPatientCareGiverAssignmnt> assignmntList = new ArrayList<WatchrxPatientCareGiverAssignmnt>();
		for (Long patientId : assignPatientsToCareGiverVO.getPatientIdList()) {

			Long count = patientCareGiverAssignmentDAO.getCountAssignedCareGiverPatient(patientId,
					assignPatientsToCareGiverVO.getCareGiverId());
			if (count > 0) {
				continue;
			}
			WatchrxPatient patient = new WatchrxPatient();
			patient.setPatientId(patientId);
			WatchrxPatientCareGiverAssignmnt assignmnt = new WatchrxPatientCareGiverAssignmnt();
			assignmnt.setWatchrxClinician(clinician);
			assignmnt.setWatchrxPatient(patient);

			assignmntList.add(assignmnt);
		}
		patientCareGiverAssignmentDAO.save(assignmntList);
		return true;
	}

	@Override
	public Long physicianCount() {
		return (long) physicianDAO.getAll().size();
	}

	@Override
	public TaskListVO getDueTasksNotClosed(TasksVO tasksVO) {
		List<TasksVO> tasksVOList = new ArrayList<TasksVO>();
		Integer index = tasksVO.getIndex();
		Integer pagesize = tasksVO.getPageSize();
		List<WatchRxTasks> watchRxTasksList = tasksDAO.getDueTasksNotClosedV1(tasksVO.getOrgId(), index, pagesize,
				tasksVO.getUserId());
		for (WatchRxTasks watchRxTask : watchRxTasksList) {
			TasksVO task = new TasksVO();
			task.setPatientId(watchRxTask.getWatchrxPatient().getPatientId());
			task.setPatientName(watchRxTask.getWatchrxPatient().getFirstName() + " "
					+ watchRxTask.getWatchrxPatient().getLastName());
			task.setTaskDesc(watchRxTask.getTaskDesc());
			task.setTaskEndDate(formater1.format(watchRxTask.getTaskEndDate()));
			task.setTaskId(watchRxTask.getTaskId());
			task.setTaskPriority(watchRxTask.getTaskPriority());
			task.setTaskStatus(watchRxTask.getTaskStatus());
			task.setTaskStartDate(formater1.format(watchRxTask.getTaskStartDate()));
			task.setTaskTitle(watchRxTask.getTaskTitle());
			tasksVOList.add(task);
		}
		TaskListVO taskListVO = new TaskListVO();
		taskListVO.setTasksVOList(tasksVOList);
		return taskListVO;
	}

	@Override
	public Long getDueTasksNotClosedCount(Long orgId, Long userId) {
		return tasksDAO.getDueTasksNotClosedCount(orgId, userId);

	}

	// @Scheduled(cron="0 0 9 25 * ?")
	@Override
	public void intimatePhysicianForBillingReview() {
		try {
			List<WatchrxClinician> clinicians = clinicianDAO.getAll();

			for (WatchrxClinician clinicianVo : clinicians) {
				String body = "Hello  " + clinicianVo.getFirstName() + "  " + clinicianVo.getLastName() + "\n"
						+ "This is a reminder mail to notify on sign off pending. Kindly provide sign off before the month end.";
				List<Object[]> data = patientDAO.getPhysicianPatientsAddressBilling(clinicianVo.getClinicianId(), 0, 1,
						null);
				if (!data.isEmpty()) {
					if (clinicianVo.getWatchrxUser().getEmail() != null) {
						email.sendEmail(Constants.Email.ADMINEMAIL, clinicianVo.getWatchrxUser().getEmail(),
								clinicianVo.getFirstName() + " " + clinicianVo.getLastName(), "Pending Sign off", body);
					}
				}
			}
		} catch (Exception e) {
			log.info("Exception occured" + e);
		}
	}

	@Override
	public ClinicianVO changePhysicianStatus(Long clinicianId) {

		WatchRxPhysician watchrxClinician = physicianDAO.getById(clinicianId);
		WatchrxUser usr = watchrxClinician.getWatchrxUser();
		if (usr != null) {
			if (usr.getStatus().equals("N")) {
				usr.setStatus("Y");
			} else {
				usr.setStatus("N");
			}
			usr.setLoggedInDateTime(new Date());
			usr.setLoggedOutDateTime(new Date());
			usr.setInvalidLoginAttempt(0);
			userDAO.save(usr);
		}
		if (watchrxClinician.getStatus().toUpperCase().equals("Y")) {
			watchrxClinician.setStatus("Y");
			watchrxClinician.getWatchrxUser().setStatus("N");
		} else if (watchrxClinician.getStatus().toUpperCase().equals("N")) {
			watchrxClinician.setStatus("Y");
			watchrxClinician.getWatchrxUser().setStatus("Y");
		} else {
			watchrxClinician.setStatus("N");
		}
		ClinicianVO clinicianVO = getPhysicianVO(watchrxClinician);
		watchrxClinician = physicianDAO.save(watchrxClinician);
		clinicianVO.setStatus(watchrxClinician.getStatus());
		return clinicianVO;

	}

	@Override
	public boolean resentOTPByEmailId(String username) {
		boolean sentOTP = false;
		try {
			List<WatchrxUser> watchrxClinicianList = userDAO.findByProperty("userName", username);
			if (watchrxClinicianList != null && watchrxClinicianList.size() > 0) {

				WatchrxUser watchrxClinician = watchrxClinicianList.get(0);
				OTPGenerator gotpGen = new OTPGenerator();
				watchrxClinician.setOtp(gotpGen.generateOTP());
				watchrxClinician.setOtpStatus("OTP_NOT_VALIDATED");
				WatchrxUser watrxUser = userDAO.save(watchrxClinician);

				String body = "Hi " + watrxUser.getFirstName() + " " + watrxUser.getLastName()
						+ ", Your OTP for the password reset of WatchRx Application is " + watrxUser.getOtp()
						+ ". OTP is secret and can be used only once. Therefore, do not disclose this to anyone.";

				email.sendEmail(Constants.Email.ADMINEMAIL, watrxUser.getUserName(),
						watrxUser.getLastName() + " " + watrxUser.getLastName(), "Password Reset OTP", body);
				sentOTP = true;
			} else {
				sentOTP = false;
			}
		} catch (Exception e) {
			log.info("Exception occure while persisting OTP {}" + e);
		}
		return sentOTP;
	}

	@Override
	public Long getDueTasksByDate(Long userId, Integer roleType, String date) {
		return tasksDAO.getDueTasksByDate(userId, roleType, date);

	}

	@Override
	public void sendDialogsCommToCG(DiyvaDialog dailog) {
		try {
			PatientTest details = new PatientTest();
			List<String> gcmIds = WatchRxFactory.getClinicianService()
					.getNurseGCMRegIdByPatientId(dailog.getWatchUserId());
			;
			String platformType = WatchRxFactory.getClinicianService()
					.getClinicianPlatformTypeByPatientId(dailog.getWatchUserId());
			if (!gcmIds.isEmpty()) {
				JsonObject jo = new JsonObject();
				jo.addProperty("messageType", "dialogComm");
				jo.addProperty("patientId", dailog.getWatchUserId());
				jo.addProperty("patientComm", dailog.getCommunication());
				Gson gson = new Gson();
				String jsonStr = gson.toJson(jo);

				log.info("FCM message to be sent is " + jsonStr);
				log.info("Platform is " + platformType);
				for (String gcmId : gcmIds) {
					try {
						details.sendMessageToNurse("message", jsonStr, gcmId, platformType);
					} catch (IOException e) {
//						log.info("Failed to send to GCM ID: " + gcmId, e);
					}
				}
			}
		} catch (Exception e) {
			log.info("Something went wrong in careGiverStatus method " + e.getMessage());
			e.printStackTrace();
		}
	}

	@Override
	public void sendNewDialogsToCG(DiyvaDialog dailog) {
		try {
			PatientTest details = new PatientTest();
			List<WatchrxClinician> watchrxClinicians = clinicianDAO.findByProperty("watchrxUser.userName",
					dailog.getUserId());
			String platformType = WatchRxFactory.getClinicianService()
					.getClinicianPlatformTypeByPatientId(dailog.getWatchUserId());
			List<String> gcmIds = new ArrayList<>();
			for (WatchrxClinician clinician : watchrxClinicians) {
				List<WatchRxClinicianDevice> devices = watchRxClinicianDeviceDAO
						.findByUserId(clinician.getWatchrxUser().getUserId());
				for (WatchRxClinicianDevice device : devices) {
					if (device.getGcmRegistrationId() != null) {
						gcmIds.add(device.getGcmRegistrationId());
					}
				}
				if (devices.isEmpty() && clinician.getGcmRegistrationId() != null) {
					gcmIds.add(clinician.getGcmRegistrationId());
				}
			}
			if (!gcmIds.isEmpty()) {
				JsonObject jo = new JsonObject();
				jo.addProperty("messageType", "newDialog");
				jo.addProperty("name", dailog.getDialogName());
				jo.addProperty("dialogConfig", dailog.getDialogConfig());
				Gson gson = new Gson();
				String jsonStr = gson.toJson(jo);

				log.info("FCM message to be sent is " + jsonStr);
				log.info("Platform is " + watchrxClinicians.get(0).getPlatformType());
				for (String gcmId : gcmIds) {
					details.sendMessageToNurse("message", jsonStr, gcmId, platformType);
				}
			}
		} catch (IOException e) {
			log.info("Something went wrong in careGiverStatus method " + e.getMessage());
			e.printStackTrace();
		}
	}

	@Override
	public PatientMinimalListVO getPatientsByCaseMangerId(Long caseManagerId) {
		log.info("Get patients for CasemanagerId" + caseManagerId);
		List<WatchrxPatientClinicianAssignmnt> watchrxPatientClinicianAssignmnts = patientClinicianAssignmentDAO
				.findByProperty("watchrxClinician.clinicianId", caseManagerId);
		PatientMinimalListVO patientMinimalListVO = new PatientMinimalListVO();
		List<PatientMinimalVO> patientMinimalVOList = new ArrayList<PatientMinimalVO>();
		for (WatchrxPatientClinicianAssignmnt watchrxPatientClinicianAssignmnt : watchrxPatientClinicianAssignmnts) {
			PatientMinimalVO patientMinimalVO = new PatientMinimalVO();
			patientMinimalVO.setPatientId(watchrxPatientClinicianAssignmnt.getWatchrxPatient().getPatientId());
			patientMinimalVO.setPatientName(watchrxPatientClinicianAssignmnt.getWatchrxPatient().getFirstName() + " "
					+ watchrxPatientClinicianAssignmnt.getWatchrxPatient().getLastName());
			patientMinimalVOList.add(patientMinimalVO);
		}
		patientMinimalListVO.setPatientMinimalVOList(patientMinimalVOList);
		return patientMinimalListVO;
	}

	@Override
	public Long getUserIdByClinicianId(Long clinicianId) throws WatchRxServiceException {
		long clinicianId1 = 0;
		try {
			log.info("Getting user by clinician ID:" + clinicianId);
			WatchrxClinician clinician = clinicianDAO.getById(clinicianId);
			if (clinician != null) {
				clinicianId1 = clinician.getWatchrxUser().getUserId();
				log.info("Found user " + clinicianId1 + " by clinician ID:" + clinicianId);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return clinicianId1;
	}

	@Override
	public PatientMinimalListVO getAllPatientByOrg(Long orgId, Long roleType, Long userId) {
		List<WatchrxPatient> patientClinicianPairs = new ArrayList<>();
		if (roleType == 1 || roleType == 2) {
			patientClinicianPairs = patientDAO.getAll();
		} else if (roleType == 3) {
			patientClinicianPairs = patientDAO.getPatientsByOrgIdAndPhysician(orgId, userId, 0, 1000);
		} else {
			patientClinicianPairs = patientDAO.getAllPatientsForOrgV1(orgId, userId);
		}
		PatientMinimalListVO patientMinimalListVO = new PatientMinimalListVO();
		List<PatientMinimalVO> patientMinimalVOList = new ArrayList<PatientMinimalVO>();
		for (WatchrxPatient watchrxPatient : patientClinicianPairs) {
			PatientMinimalVO patientMinimalVO = new PatientMinimalVO();
			patientMinimalVO.setPatientId(watchrxPatient.getPatientId());
			patientMinimalVO.setPatientName(watchrxPatient.getFirstName() + " " + watchrxPatient.getLastName());
			patientMinimalVOList.add(patientMinimalVO);
		}
		patientMinimalListVO.setPatientMinimalVOList(patientMinimalVOList);
		return patientMinimalListVO;
	}

	@Override
	public ClinicianVO getClinicianByUserId(Long userId, Long roleType) {
		if (userId > 0) {
			if (roleType == 3) {
				WatchRxPhysician physician = physicianDAO.findByProperty("watchrxUser.userId", userId).get(0);
				ClinicianVO clinicianVO = getPhysicianVO(physician);
				return clinicianVO;
			} else {
				List<WatchrxClinician> watchrxClinicianList = clinicianDAO.findByProperty("watchrxUser.userId", userId);
				if (watchrxClinicianList != null && !watchrxClinicianList.isEmpty()) {
					ClinicianVO clinicianVO = getClinicianVO(watchrxClinicianList.get(0));
					return clinicianVO;
				}
			}
		}
		return null;
	}

	@Override
	public List<Group> getGroupsAndRolesForUser(Long userId) {
		log.info("Inside getGroupsAndRolesForUser " + userId);
		List<Group> grps = new ArrayList<>();
		try {
			List<WatchrxGroupUserAssignment> wGUA = grpUsrDao.findByProperty("watchrxUser.userId", userId);
			if (wGUA.size() > 0) {
				wGUA.forEach(wg -> {
					Group gp = new Group();
					if (wg.getWatchrxGroup() != null) {
						WatchrxGroup grp = wg.getWatchrxGroup();
						gp.setGroupDescription(grp.getGroupDescription());
						gp.setGroupId(grp.getGroupId());
						gp.setGroupName(grp.getGroupName());
						grps.add(gp);
					}
				});
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		return grps;
	}

	@Override
	public boolean expirePasswordReset(JwtRequest loginDetails) {
		List<WatchrxUser> watchrxClinicianList = userDAO.findByProperty("userName", loginDetails.getUsername());
		if (watchrxClinicianList != null && watchrxClinicianList.size() > 0) {
			WatchrxUser user = watchrxClinicianList.get(0);
			user.setPassword(loginDetails.getPassword());
			user.setPasswordChangedTime(new Date());
			WatchrxUser updatedUser = userDAO.save(user);
			if (updatedUser.getPassword().equals(loginDetails.getPassword())) {
				return true;
			}
		}
		return false;
	}

	@Override
	public PatientMinimalListVO getPatientsByCaseMangerV2(Long userId, Long orgId) {

		PatientMinimalListVO patientMinimalListVO = new PatientMinimalListVO();
		List<PatientMinimalVO> patientMinimalVOList = new ArrayList<PatientMinimalVO>();

		List<WatchrxPatient> patientsList = patientDAO.getAllPatientsByOrgIdAndClinicianId(orgId, userId);
		for (WatchrxPatient patient : patientsList) {
			PatientMinimalVO patientMinimalVO = new PatientMinimalVO();
			patientMinimalVO.setPatientId(patient.getPatientId());
			patientMinimalVO.setPatientName(patient.getFirstName() + " " + patient.getLastName());
			patientMinimalVOList.add(patientMinimalVO);
		}
		patientMinimalListVO.setPatientMinimalVOList(patientMinimalVOList);
		return patientMinimalListVO;
	}

	@Override
	public ClinicianOrgDetails getClinicinOrgDetails() {
		List<WatchrxGroup> watchrxGroupList = grpDao.getAll();
		List<WatchrxGroupUserAssignment> grps = grpUsrDao.findByProperty("watchrxUser.userType",
				Constants.UserType.CASEMANAGER);
		ClinicianOrgDetails cliOrgdetails = new ClinicianOrgDetails();
		List<ClinicianOrgDropDown> cdtls = new ArrayList<>();
		List<ClinicianOrgDropDown> gdtls = new ArrayList<>();
		grps.forEach(c -> {
			ClinicianOrgDropDown dp = new ClinicianOrgDropDown();
			// if(c.getWatchrxUser().getStatus()=="Y") {
			dp.setId(c.getWatchrxUser().getUserId());
			dp.setLabel(c.getWatchrxUser().getFirstName() + " " + c.getWatchrxUser().getLastName() + " ("
					+ c.getWatchrxUser().getUserName() + ")");
			dp.setOrgId(c.getWatchrxGroup().getGroupId());
			dp.setStatus(c.getWatchrxUser().getStatus());
			cdtls.add(dp);
		});
		watchrxGroupList.stream().forEach(g -> {
			ClinicianOrgDropDown dp = new ClinicianOrgDropDown();
			dp.setId(g.getGroupId());
			dp.setLabel(g.getGroupName());
			gdtls.add(dp);
		});
		cliOrgdetails.setClinicianList(cdtls);
		cliOrgdetails.setOrgList(gdtls);
		return cliOrgdetails;
	}

	@Override
	public void assignPatientsToNewCaseManager(AsssignCMToPatientVO caregiver) {
		// TODO Auto-generated method stub
		List<WatchrxClinician> clisFrom = clinicianDAO.findByProperty("watchrxUser.userId", caregiver.getFromUserId());
		caregiver.setFromUserId(clisFrom.get(0).getClinicianId());
		List<WatchrxClinician> clisTo = clinicianDAO.findByProperty("watchrxUser.userId", caregiver.getToUserId());
		caregiver.setToUserId(clisTo.get(0).getClinicianId());
		log.info("*********" + caregiver.getFromUserId());
		log.info("*********" + caregiver.getToUserId());
		clinicianDAO.saveNewCMToPatient(caregiver);
	}

	@Override
	public List<Map<String, Object>> getAllCMByUsersByOrg(Long orgId, String startDate, String edDate) {
		List<WatchrxGroupUserAssignment> users = grpUsrDao.findByProperty("watchrxGroup.groupId", orgId);
		List<Map<String, Object>> details = new ArrayList<>();
		long sno = 0;
		getStartDateByDate(startDate);
		getEndDateByDate(edDate);

		users.forEach(u1 -> {
			if (!u1.getWatchrxUser().getWatchrxClinicians().isEmpty()) {
				for (WatchrxClinician m : u1.getWatchrxUser().getWatchrxClinicians()) {
					Map<String, Object> dtl = new HashMap<String, Object>();
					dtl.put("email", u1.getWatchrxUser().getEmail());
					dtl.put("name", m.getFirstName() + " " + m.getLastName());
					dtl.put("phone", m.getPhoneNumber());
					dtl.put("sno", sno + 1);
					dtl.put("userId", u1.getWatchrxUser().getUserId());

//					Long patientsCount = watchRxPatientRpmCcmDataDetailsDAO
//							.getAllPatientsCountByOrgIdAndClinicianIdAndFilterType(orgId, m.getClinicianId(), 5, "all",
//									st, ed);
					List<BigInteger> patientsList = patientDAO.activePatientsForOrgAndClinician(orgId,
							m.getClinicianId(), 5);

					dtl.put("patientsCount", patientsList.size());
					details.add(dtl);
				}
			}
		});
		return details;
	}

	private Date getStartDateByDate(String date) {
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Date sDate;
		try {
			sDate = sdf.parse(date);
			Calendar startDateCal = Calendar.getInstance();
			startDateCal.setTime(sDate);
			startDateCal.set(Calendar.HOUR_OF_DAY, 00);
			startDateCal.set(Calendar.MINUTE, 00);
			startDateCal.set(Calendar.SECOND, 00);
			return startDateCal.getTime();
		} catch (ParseException e) {
			e.printStackTrace();
			return new Date();
		}
	}

	private Date getEndDateByDate(String date) {
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Date eDate;
		try {
			eDate = sdf.parse(date);
			Calendar endDateCal = Calendar.getInstance();
			endDateCal.setTime(eDate);
			endDateCal.set(Calendar.HOUR_OF_DAY, 23);
			endDateCal.set(Calendar.MINUTE, 59);
			endDateCal.set(Calendar.SECOND, 59);
			return endDateCal.getTime();
		} catch (ParseException e) {
			e.printStackTrace();
			return new Date();
		}
	}

	@Override
	public TaskListVO getAllTasksByPatientId(Integer index, Integer pageSize, Long patientId) {
		List<TasksVO> result = new ArrayList<>();
		List<WatchRxTasks> watchRxTasksList = tasksDAO.getAllTasksByPatientId(index, pageSize, patientId);
		for (WatchRxTasks watchRxTask : watchRxTasksList) {
			TasksVO task = new TasksVO();
			task.setPatientId(watchRxTask.getWatchrxPatient().getPatientId());
			task.setPatientName(watchRxTask.getWatchrxPatient().getFirstName() + " "
					+ watchRxTask.getWatchrxPatient().getLastName());
			task.setTaskDesc(watchRxTask.getTaskDesc());
			task.setTaskEndDate(formater1.format(watchRxTask.getTaskEndDate()));
			task.setTaskId(watchRxTask.getTaskId());
			task.setTaskPriority(watchRxTask.getTaskPriority());
			task.setTaskStatus(watchRxTask.getTaskStatus());
			task.setTaskStartDate(formater1.format(watchRxTask.getTaskStartDate()));
			task.setTaskTitle(watchRxTask.getTaskTitle());
			result.add(task);
		}
		TaskListVO taskListVO = new TaskListVO();
		taskListVO.setTasksVOList(result);
		return taskListVO;
	}

	@Override
	public Long getAllTasksCountByPatientId(Long patientId) {
		return tasksDAO.getAllTasksCount(patientId);
	}

	@Override
	public PatientMinimalListVO getPatientsByOrgV2(Long orgId) {
		PatientMinimalListVO patientMinimalListVO = new PatientMinimalListVO();
		List<PatientMinimalVO> patientMinimalVOList = new ArrayList<PatientMinimalVO>();
		List<WatchrxPatient> patientsList = patientDAO.getAllPatientsByOrgId(orgId);
		for (WatchrxPatient patient : patientsList) {
			PatientMinimalVO patientMinimalVO = new PatientMinimalVO();
			patientMinimalVO.setPatientId(patient.getPatientId());
			patientMinimalVO.setPatientName(patient.getFirstName() + " " + patient.getLastName());
			patientMinimalVOList.add(patientMinimalVO);
		}
		patientMinimalListVO.setPatientMinimalVOList(patientMinimalVOList);
		return patientMinimalListVO;
	}

	@Override
	public StatusVO createCalendarTask(WatchRxCalendarTaskVo calendarTaskVo) {
		StatusVO statusVO = new StatusVO();
		try {
//			Date startDate = inputFormat.parse(calendarTaskVo.getStart());
//			Date endDate = inputFormat.parse(calendarTaskVo.getEnd());

			Date startDate = getUtcDateTimeByLocal(calendarTaskVo.getStart(), calendarTaskVo.getTimezone());
			Date endDate = getUtcDateTimeByLocal(calendarTaskVo.getEnd(), calendarTaskVo.getTimezone());

			List<WatchRxCalendarTask> alreadyExitingTask = calendarTaskDAO
					.getTaskByUserIdAndDates(calendarTaskVo.getUserId(), startDate, endDate);
			if (alreadyExitingTask != null && alreadyExitingTask.size() > 0) {
				statusVO.setResponseCode(500);
				statusVO.setSuccess(false);
				statusVO.setMessages(new String[] { "Already task found for given date range" });
				return statusVO;
			}
			WatchRxCalendarTask calendarTask = new WatchRxCalendarTask();
			calendarTask.setColorCode(calendarTaskVo.getClassName());
			calendarTask.setCreatedDate(new Date());
			calendarTask.setIsDeleted(false);
			calendarTask.setTaskPriority(calendarTaskVo.getPriority());
			calendarTask.setStatus("CREATED");
			calendarTask.setTaskDescription(calendarTaskVo.getDescription());
			calendarTask.setTaskEndDateTime(endDate);
			calendarTask.setTaskStartDateTime(startDate);
			calendarTask.setTaskTitle(calendarTaskVo.getTitle());
			calendarTask.setUpdatedDate(new Date());
			calendarTask.setWatchrxPatient(new WatchrxPatient(calendarTaskVo.getPatientId()));
			calendarTask.setWatchrxUser(new WatchrxUser(calendarTaskVo.getUserId()));
			calendarTask.setTimezone(calendarTaskVo.getTimezone());
			calendarTask.setIsEmailSent(false);
			calendarTask.setColorCode("");
			WatchRxCalendarTask savedTask = calendarTaskDAO.save(calendarTask);
			if (savedTask != null && savedTask.getTaskId() > 0) {
				statusVO.setResponseCode(200);
				statusVO.setSuccess(true);
				statusVO.setMessages(new String[] { "Task successfully created" });
			} else {
				statusVO.setResponseCode(500);
				statusVO.setSuccess(false);
				statusVO.setMessages(new String[] { "Failed to add task, please try again" });
			}
		} catch (Exception e) {
			e.printStackTrace();
			statusVO.setSuccess(false);
		}
		return statusVO;
	}

	@Override
	public List<WatchRxCalendarTaskVo> getAllCalendarTaskByUserId(Long orgId, Long userId) {
		List<WatchRxCalendarTaskVo> response = new ArrayList<>();
		try {
			List<WatchRxCalendarTask> taskList = calendarTaskDAO.getTaskByUserId(orgId, userId);
			if (taskList != null && taskList.size() > 0) {
				taskList.forEach(task -> {
					WatchRxCalendarTaskVo calendarTaskVo = new WatchRxCalendarTaskVo();
					calendarTaskVo.setId(task.getTaskId());
					calendarTaskVo.setClassName(task.getColorCode() == null ? "bg-info" : task.getColorCode());
					calendarTaskVo.setDescription(task.getTaskDescription());
					calendarTaskVo.setEnd(getLocalDateByUtc(task.getTaskEndDateTime(), task.getTimezone()));
					WatchrxPatient patient = task.getWatchrxPatient();
					calendarTaskVo.setPatinetName(patient.getFirstName() + " " + patient.getLastName());
					calendarTaskVo.setPatientId(patient.getPatientId());
					calendarTaskVo.setStart(getLocalDateByUtc(task.getTaskStartDateTime(), task.getTimezone()));
					calendarTaskVo.setTitle(task.getTaskTitle());
					calendarTaskVo.setUserId(userId);
					calendarTaskVo.setPriority(task.getTaskPriority());
					response.add(calendarTaskVo);
				});
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	@Override
	public StatusVO editCalendarTask(WatchRxCalendarTaskVo calendarTaskVo) {
		StatusVO statusVO = new StatusVO();
		try {
			WatchRxCalendarTask calendarTask = calendarTaskDAO.getById(calendarTaskVo.getId());
			if (calendarTask == null) {
				statusVO.setResponseCode(500);
				statusVO.setSuccess(false);
				statusVO.setMessages(new String[] { "Unable update task, Please check with admin" });
				return statusVO;
			}
			Date startDate = getUtcDateTimeByLocal(calendarTaskVo.getStart(), calendarTaskVo.getTimezone());
			Date endDate = getUtcDateTimeByLocal(calendarTaskVo.getEnd(), calendarTaskVo.getTimezone());
			List<WatchRxCalendarTask> alreadyExitingTask = calendarTaskDAO
					.getTaskByUserIdAndDates(calendarTaskVo.getUserId(), startDate, endDate);
			if (alreadyExitingTask != null && alreadyExitingTask.size() > 0) {
				calendarTask.setColorCode(calendarTaskVo.getClassName());
				calendarTask.setIsDeleted(false);
				calendarTask.setTaskPriority(calendarTaskVo.getPriority());
				calendarTask.setStatus("RE_SCHEDULED");
				calendarTask.setUpdatedDate(new Date());
				WatchRxCalendarTask savedTask = calendarTaskDAO.save(calendarTask);
				if (savedTask != null && savedTask.getTaskId() > 0) {
					statusVO.setResponseCode(200);
					statusVO.setSuccess(true);
					statusVO.setMessages(new String[] { "Task updated successfully." });
				} else {
					statusVO.setResponseCode(500);
					statusVO.setSuccess(false);
					statusVO.setMessages(new String[] { "Failed to update task, please try again" });
				}
				return statusVO;
			}
			calendarTask.setColorCode(calendarTaskVo.getClassName());
			calendarTask.setIsDeleted(false);
			calendarTask.setTaskPriority(calendarTaskVo.getPriority());
			calendarTask.setStatus("RE_SCHEDULED");
			calendarTask.setTaskEndDateTime(endDate);
			calendarTask.setTaskStartDateTime(startDate);
			calendarTask.setUpdatedDate(new Date());
			WatchRxCalendarTask savedTask = calendarTaskDAO.save(calendarTask);
			if (savedTask != null && savedTask.getTaskId() > 0) {
				statusVO.setResponseCode(200);
				statusVO.setSuccess(true);
				statusVO.setMessages(new String[] { "Task updated successfully." });
			} else {
				statusVO.setResponseCode(500);
				statusVO.setSuccess(false);
				statusVO.setMessages(new String[] { "Failed to update task, please try again" });
			}
		} catch (Exception e) {
			e.printStackTrace();
			statusVO.setSuccess(false);
		}
		return statusVO;
	}

	@Override
	public StatusVO deleteCalendarTask(Long taskId) {
		StatusVO statusVO = new StatusVO();
		try {
			WatchRxCalendarTask calendarTask = calendarTaskDAO.getById(taskId);
			if (calendarTask != null) {
				if (calendarTask.getIsDeleted()) {
					statusVO.setResponseCode(500);
					statusVO.setSuccess(false);
					statusVO.setMessages(new String[] { "Task already deleted...!!" });
				}
				calendarTask.setIsDeleted(true);
				WatchRxCalendarTask savedTask = calendarTaskDAO.save(calendarTask);
				if (savedTask != null && savedTask.getTaskId() > 0) {
					statusVO.setResponseCode(200);
					statusVO.setSuccess(true);
					statusVO.setMessages(new String[] { "Task deleted successfully." });
				} else {
					statusVO.setResponseCode(500);
					statusVO.setSuccess(false);
					statusVO.setMessages(new String[] { "Failed to delete task, please try again" });
				}
			}
		} catch (Exception e) {
			statusVO.setSuccess(false);
		}
		return statusVO;
	}

	private Date getUtcDateTimeByLocal(String date, String tz) {
		// Parse the input string to LocalDateTime
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd-yyyy hh:mm a");
		LocalDateTime localDateTime = LocalDateTime.parse(date, formatter);
		// Convert LocalDateTime to ZonedDateTime using the given time zone
		ZoneId zoneId = ZoneId.of(tz);
		ZonedDateTime zonedDateTime = ZonedDateTime.of(localDateTime, zoneId);
		// Convert ZonedDateTime to UTC Instant
		ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
		Date utcDate = Date.from(utcDateTime.toInstant());
		// Print the UTC date-time
		System.out.println("UTC Date-Time: " + utcDate);
		return utcDate;
	}

	private String getLocalDateByUtc(Date utcDate, String targetTimeZone) {

		Instant instant = utcDate.toInstant();

		// Convert Instant to ZonedDateTime with UTC time zone
		ZonedDateTime utcDateTime = instant.atZone(ZoneId.of("UTC"));

		// Convert ZonedDateTime to Asia/Kolkata time zone
		ZonedDateTime kolkataDateTime = utcDateTime.withZoneSameInstant(ZoneId.of(targetTimeZone));

		// Format the result as string
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
		return kolkataDateTime.format(formatter);
	}

	private String getLocalDateByUtcForEmail(Date utcDate, String targetTimeZone, String format) {
		Instant instant = utcDate.toInstant();
		ZonedDateTime utcDateTime = instant.atZone(ZoneId.of("UTC"));
		ZonedDateTime kolkataDateTime = utcDateTime.withZoneSameInstant(ZoneId.of(targetTimeZone));
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
		return kolkataDateTime.format(formatter);
	}

	@Override
	public StatusVO notificationStatus(Long userId, Boolean status) {
		StatusVO statusVO = new StatusVO();
		try {
			WatchrxUser user = userDAO.getById(userId);
			if (user != null) {
				user.setIsNotificationEnabled(status);
				WatchrxUser updatedUser = userDAO.save(user);
				if (updatedUser != null && updatedUser.getIsNotificationEnabled().equals(status)) {
					statusVO.setResponseCode(200);
					statusVO.setSuccess(true);
					statusVO.setMessages(new String[] { "Notification settings updated successfully." });
				} else {
					statusVO.setResponseCode(500);
					statusVO.setSuccess(false);
					statusVO.setMessages(new String[] { "Failed to updated notification settings." });
				}
			} else {
				statusVO.setResponseCode(500);
				statusVO.setSuccess(false);
				statusVO.setMessages(new String[] { "Failed to updated notification settings." });
			}
		} catch (Exception e) {
			statusVO.setResponseCode(500);
			statusVO.setSuccess(false);
			statusVO.setMessages(new String[] { "Failed to updated notification settings." });
		}
		return statusVO;
	}

	@Override
	public Map<String, Object> getNotificationStatus(Long userId) {
		Map<String, Object> resp = new HashMap<>();
		resp.put("status", false);
		try {
			WatchrxUser user = userDAO.getById(userId);
			if (user != null) {
				resp.put("status", user.getIsNotificationEnabled());
			}
		} catch (Exception e) {
			resp.put("status", false);
		}
		return resp;
	}

	@Override
	public void sendEmailNotifications() {
		try {
			Date startDate = new Date();
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(startDate);
			calendar.add(Calendar.MINUTE, 15);
			calendar.add(Calendar.SECOND, 59);
			Date endDate = calendar.getTime();

			log.info("While sending Email notifications: start Date:" + startDate + " End Date: " + endDate);
			List<WatchRxCalendarTask> tasksList = calendarTaskDAO.getTaskByDates(startDate, endDate);
			if (tasksList != null && tasksList.size() > 0) {
				for (WatchRxCalendarTask calendarTask : tasksList) {
					try {
						WatchrxUser user = calendarTask.getWatchrxUser();
						if (user != null) {
							Boolean notiEnabled = calendarTask.getWatchrxUser().getIsNotificationEnabled();
							if (notiEnabled != null && notiEnabled) {
								if (!calendarTask.getIsEmailSent()) {
									String startTime = getLocalDateByUtcForEmail(calendarTask.getTaskStartDateTime(),
											calendarTask.getTimezone(), "EEE MMM d, yyyy h:mm a");

									String endTime = getLocalDateByUtcForEmail(calendarTask.getTaskEndDateTime(),
											calendarTask.getTimezone(), "h:mm a");

									String subject = "Reminder: appointment @" + startTime + " - " + endTime;

									WatchrxPatient patient = calendarTask.getWatchrxPatient();
									String body = "You have an upcoming appointment with " + patient.getFirstName()
											+ " " + patient.getLastName();

									email.sendEmail(Constants.Email.ADMINEMAIL, user.getEmail(),
											user.getFirstName() + " " + user.getLastName(), subject, body);
									calendarTask.setIsEmailSent(true);
									calendarTaskDAO.save(calendarTask);
								} else {
									log.info("Task Id: " + calendarTask.getTaskId() + " and Email Sent: "
											+ calendarTask.getIsEmailSent());
								}
							} else {
								log.info("User Notification not enabled for task Id: " + calendarTask.getTaskId()
										+ " and User Id: " + user.getUserId());
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
						continue;
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public List<WatchRxCalendarTaskVo> getTodayCalendarTasks(Long orgId, Long userId, String date) {
		List<WatchRxCalendarTaskVo> response = new ArrayList<>();
		try {
			Date st = getStartDateByDate(date);
			Date ed = getEndDateByDate(date);
			List<WatchRxCalendarTask> taskList = calendarTaskDAO.getTaskByDatesAndUserId(orgId, userId, st, ed);
			if (taskList != null && taskList.size() > 0) {
				taskList.forEach(task -> {
					WatchRxCalendarTaskVo calendarTaskVo = new WatchRxCalendarTaskVo();
					calendarTaskVo.setId(task.getTaskId());
					calendarTaskVo.setClassName(task.getColorCode() == null ? "bg-info" : task.getColorCode());
					calendarTaskVo.setDescription(task.getTaskDescription());
					calendarTaskVo.setEnd(getLocalDateByUtc(task.getTaskEndDateTime(), task.getTimezone()));
					WatchrxPatient patient = task.getWatchrxPatient();
					calendarTaskVo.setPatinetName(patient.getFirstName() + " " + patient.getLastName());
					calendarTaskVo.setPatientId(patient.getPatientId());
					calendarTaskVo.setStart(getLocalDateByUtc(task.getTaskStartDateTime(), task.getTimezone()));
					calendarTaskVo.setTitle(task.getTaskTitle());
					calendarTaskVo.setUserId(userId);
					calendarTaskVo.setPriority(task.getTaskPriority());
					response.add(calendarTaskVo);
				});
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	@Override
	public List<WatchRxCalendarTaskVo> getTodayCalendarTasksV2(Long orgId, Long userId, String date,
			String userTimezone) {
		List<WatchRxCalendarTaskVo> response = new ArrayList<>();
		try {

			SimpleDateFormat debugFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss z");
			debugFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
			ZoneId zone = ZoneId.of(userTimezone);
			ZonedDateTime localStart = LocalDate.parse(date).atStartOfDay(zone);
			ZonedDateTime localEnd = localStart.plusDays(1);
			Instant utcStart00 = localStart.toInstant();
			Instant utcEnd00 = localEnd.toInstant();
			Date utcStart = Date.from(utcStart00);
			Date utcEnd = Date.from(utcEnd00);
			System.out.println("localStart : " + localStart + " localEnd : " + localEnd + " utcStartInstant : "
					+ utcStart00 + " utcEndInstant : " + utcEnd00);
			System.out.println("UTC start debugged to UTC: " + debugFormat.format(utcStart) + "UTC end debugged to UTC "
					+ debugFormat.format(utcEnd));
			System.out.println("utcStart : " + utcStart + " utcEnd : " + utcEnd);
			System.out.println("OrgID:" + orgId + "UserID:" + userId);

			List<WatchRxCalendarTask> taskList = calendarTaskDAO.getTaskByDatesAndUserIdV2(orgId, userId, utcStart,
					utcEnd);
			if (taskList != null && taskList.size() > 0) {
				taskList.forEach(task -> {
					WatchRxCalendarTaskVo calendarTaskVo = new WatchRxCalendarTaskVo();
					calendarTaskVo.setId(task.getTaskId());
					calendarTaskVo.setClassName(task.getColorCode() == null ? "bg-info" : task.getColorCode());
					calendarTaskVo.setDescription(task.getTaskDescription());
					calendarTaskVo.setStart(getLocalDateByUtc(task.getTaskStartDateTime(), task.getTimezone()));
					calendarTaskVo.setEnd(getLocalDateByUtc(task.getTaskEndDateTime(), task.getTimezone()));
					WatchrxPatient patient = task.getWatchrxPatient();
					calendarTaskVo.setPatinetName(patient.getFirstName() + " " + patient.getLastName());
					calendarTaskVo.setPatientId(patient.getPatientId());
					calendarTaskVo.setStart(getLocalDateByUtc(task.getTaskStartDateTime(), task.getTimezone()));
					calendarTaskVo.setTitle(task.getTaskTitle());
					calendarTaskVo.setUserId(userId);
					calendarTaskVo.setPriority(task.getTaskPriority());
					response.add(calendarTaskVo);
				});
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	@Override
	public List<Map<String, Object>> getCarePlanTabs() {
		List<Map<String, Object>> response = new ArrayList<>();
		try {
			List<WatchRxCarePlanTabsConfig> list = carePlanTabsConfigDAO.findByProperty("isEnabled", true);
			if (list != null && list.size() > 0) {
				list.forEach(data -> {
					Map<String, Object> map = new HashMap<>();
					map.put("id", data.getTabId());
					map.put("tabName", data.getTabName());
					map.put("isEnabled", data.getIsEnabled());
					response.add(map);
				});
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	@Override
	public Map<Long, Object> getCarePlanQuestions() {
		Map<Long, Object> response = new HashMap<>();
		try {
			List<WatchRxCarePlanQuestions> list = watchRxCarePlanQuestionsDAO.findByProperty("isEnabled", true);
			if (list != null && list.size() > 0) {
				list.forEach(data -> {
					response.put(data.getId(), data.getQuestions());
				});
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	@Override
	public List<WatchRxCalendarTaskVo> getTaskByPatientId(Long patientId) {
		List<WatchRxCalendarTaskVo> response = new ArrayList<>();
		try {
			List<WatchRxCalendarTask> taskList = calendarTaskDAO.getTasksByPatientId(patientId);
			if (taskList != null && taskList.size() > 0) {
				taskList.forEach(task -> {
					WatchRxCalendarTaskVo calendarTaskVo = new WatchRxCalendarTaskVo();

					calendarTaskVo.setPatientId(patientId);
					calendarTaskVo.setId(task.getTaskId());
					calendarTaskVo.setTitle(task.getTaskTitle());
					calendarTaskVo.setClassName(task.getColorCode() == null ? "bg-info" : task.getColorCode());
					calendarTaskVo.setDescription(task.getTaskDescription());
					calendarTaskVo.setPriority(task.getTaskPriority());
					calendarTaskVo.setStart(getLocalDateByUtc(task.getTaskStartDateTime(), task.getTimezone()));
					calendarTaskVo.setEnd(getLocalDateByUtc(task.getTaskEndDateTime(), task.getTimezone()));
					calendarTaskVo.setPatinetName(
							task.getWatchrxPatient().getFirstName() + " " + task.getWatchrxPatient().getLastName());
					calendarTaskVo.setTimezone(task.getTimezone());
					response.add(calendarTaskVo);
				});
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	@Override
	public void saveCMPatientTimer(Long userId, Long patientId, String timer) {
		// TODO Auto-generated method stub
		try {
			WatchRxClinicianPatientTimerReport rep = new WatchRxClinicianPatientTimerReport();
			rep.setTimeSpent(timer);
			rep.setWatchrxPatient(new WatchrxPatient(patientId));
			rep.setWatchrxUser(new WatchrxUser(userId));
			rep.setCreatedDate(new Date());
			if (rep.getTimeSpent() != null) {
				var b = rep.getTimeSpent().substring(0, rep.getTimeSpent().lastIndexOf(":"));
				String a1[] = b.split(":");
				Integer time1 = (Integer.valueOf(a1[0]) * 60) + Integer.valueOf(a1[1]);
				PatientDataDto patientDataDto = new PatientDataDto(new WatchrxPatient(patientId), time1.doubleValue(),
						"cm_rev_mins", PatientDataDto.READING_TYPE.ENCOUNTER_READING);
				patientDataProcessor.pushDataForProcessing(patientDataDto);
			}
			caseManagerPatientTimerDAO.save(rep);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public List<ClinicianPatientTimerVO> fetchCMPatientTimer() {
		// TODO Auto-generated method stub
		try {
			List<WatchRxClinicianPatientTimerReport> reps = caseManagerPatientTimerDAO.getAll();
			List<ClinicianPatientTimerVO> vos = new ArrayList<>();
			reps.forEach(r -> {
				ClinicianPatientTimerVO rep = new ClinicianPatientTimerVO();
				rep.setTimeSpent(r.getTimeSpent());
				rep.setPatientName(r.getWatchrxPatient().getFirstName() + " " + r.getWatchrxPatient().getLastName());
				rep.setClicnicianName(r.getWatchrxUser().getFirstName() + " " + r.getWatchrxUser().getLastName());
				rep.setCreatedDate(r.getCreatedDate().toString());
				vos.add(rep);
			});
			return vos;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return Collections.emptyList();
	}

	@Override
	public List<Map<String, Object>> getAllCMByUsersByOrgandName(Long orgId, String name) {
		List<WatchrxGroupUserAssignment> users = grpUsrDao.getClinicianByName(name, orgId);
		List<Map<String, Object>> details = new ArrayList<>();
		long sno = 0;

		users.forEach(u1 -> {
			if (!u1.getWatchrxUser().getWatchrxClinicians().isEmpty()) {
				for (WatchrxClinician m : u1.getWatchrxUser().getWatchrxClinicians()) {
					Map<String, Object> dtl = new HashMap<String, Object>();
					dtl.put("email", u1.getWatchrxUser().getEmail());
					dtl.put("name", m.getFirstName() + " " + m.getLastName());
					dtl.put("phone", m.getPhoneNumber());
					dtl.put("sno", sno + 1);
					dtl.put("userId", u1.getWatchrxUser().getUserId());

					List<BigInteger> patientsList = patientDAO.activePatientsForOrgAndClinician(orgId,
							m.getClinicianId(), 5);

					dtl.put("patientsCount", patientsList.size());
					details.add(dtl);
				}
			}
		});
		return details;
	}

	@Override
	public PhysicianListVO getPhysiciansByOrgAndName(Long orgId, String name, Long userId) {
		List<WatchrxGroupUserAssignment> users = grpUsrDao.getPhysicianByName(name, orgId);

		List<PhysicianVO> physicianVOList = new ArrayList<PhysicianVO>();

		for (WatchrxGroupUserAssignment watchrxPhysicianCaseManagerAssignment : users) {
			WatchrxUser user = watchrxPhysicianCaseManagerAssignment.getWatchrxUser();
			WatchRxPhysician physician = physicianDAO.findByProperty("watchrxUser.userId", user.getUserId()).get(0);
			Long patientCount = patientDAO.patientCountPhysician(physician.getPhysicianId(), orgId);
			PhysicianVO physicianVO = physicianModelToVO(physician);
			physicianVO.setPatientCount(patientCount);
			physicianVOList.add(physicianVO);
		}

		PhysicianListVO physicianListVO = new PhysicianListVO();
		physicianListVO.setPhysicianVOList(physicianVOList);
		return physicianListVO;

	}

	public static void main(String[] args) {
		String pattern = "0:1";
		String a1[] = pattern.split(":");
		var a = (Integer.valueOf(a1[0]) * 60) + Integer.valueOf(a1[1]);
		System.out.println(a);
	}

	@Override
	public ClinicianResponse searchUserByRole(Integer role, String name) {
		ClinicianResponse clinicianResponse = new ClinicianResponse();
		List<ClinicianResponseVO> clinicianList = null;
		if (role == 5 || role == 4) {
			clinicianList = new ArrayList<>();
			List<WatchrxClinician> watchrxClinicians = clinicianDAO.getAllWithInActive(name, role);
			for (WatchrxClinician watchrxClinician : watchrxClinicians) {
				ClinicianResponseVO clinicianVO = new ClinicianResponseVO();
				clinicianVO.setFirstName(watchrxClinician.getFirstName());
				clinicianVO.setLastName(watchrxClinician.getLastName());
				clinicianVO.setEmail(watchrxClinician.getWatchrxUser().getUserName());
				clinicianVO.setUserName(watchrxClinician.getWatchrxUser().getUserName());
				clinicianVO.setPhoneNumber(watchrxClinician.getPhoneNumber());
				clinicianVO.setStatus(watchrxClinician.getWatchrxUser().getStatus());
				clinicianVO.setClinicianId(watchrxClinician.getClinicianId());
				clinicianList.add(clinicianVO);
			}
			clinicianResponse.setClinicianList(clinicianList);
			clinicianResponse.setCount(Long.valueOf(clinicianList.size()));
		} else if (role == 3) {
			clinicianList = new ArrayList<>();
			List<WatchRxPhysician> physician = physicianDAO.getAllWithInActive(name);
			for (WatchRxPhysician watchrxClinician : physician) {
				ClinicianResponseVO clinicianVO = new ClinicianResponseVO();
				clinicianVO.setFirstName(watchrxClinician.getFirstName());
				clinicianVO.setLastName(watchrxClinician.getLastName());
				clinicianVO.setEmail(watchrxClinician.getWatchrxUser().getUserName());
				clinicianVO.setUserName(watchrxClinician.getWatchrxUser().getUserName());
				clinicianVO.setPhoneNumber(watchrxClinician.getPhoneNumber());
				clinicianVO.setStatus(watchrxClinician.getWatchrxUser().getStatus());
				clinicianVO.setClinicianId(watchrxClinician.getPhysicianId());
				clinicianList.add(clinicianVO);
			}
			clinicianResponse.setClinicianList(clinicianList);
			clinicianResponse.setCount(Long.valueOf(clinicianList.size()));
		}
		return clinicianResponse;
	}
}