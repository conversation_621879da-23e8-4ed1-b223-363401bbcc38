package com.medsure.dao;

import com.medsure.model.WatchRxEncounterDrafts;

import java.util.Date;
import java.util.List;

public interface EncounterDraftsDAO extends BaseDAO<WatchRxEncounterDrafts>{

    List<WatchRxEncounterDrafts> getAllDraftsByPatientIdAndDatePagination(Long patientId, Date startDate, Date endDate, int index, int pageSize);

    Long countAllDraftsByPatientIdAndDate(Long patientId, Date startDate, Date endDate);

}
