package com.medsure.dao.impl;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.validation.constraints.NotBlank;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.medsure.common.Constants;
import com.medsure.dao.PatientDAO;
import com.medsure.model.WatchrxClinician;
import com.medsure.model.WatchrxPatient;
import com.medsure.ui.entity.server.PatientVO;
import com.medsure.ui.entity.server.adminreport.EncounterReport;
import com.medsure.ui.entity.server.adminreport.VitalReport;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestChronicConditionsVO;
import com.medsure.ui.util.WatchRxUtils;

/**
 * <AUTHOR>
 *
 */
@Component
public class PatientDAOImpl extends BaseDAOImpl<WatchrxPatient> implements PatientDAO {

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxPatient> getAll() {
		System.out.println("In PatientDAOImpl:getAll");
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxPatient> entities = em
				.createQuery("FROM WatchrxPatient WHERE status='" + Constants.Status.ACTIVE + "'").getResultList();
		return entities;
	}

	/**
	 * Gets a entity by property id
	 */
	@Override
	@SuppressWarnings("unchecked")
	public List<WatchrxPatient> findByProperty(String propertyName, Object value) {
		System.out.println("In PatientDAOImpl:findByProperty");
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery("SELECT e FROM WatchrxPatient e WHERE status='" + Constants.Status.ACTIVE
				+ "' AND e." + propertyName + "= :propertyValue");
		query.setParameter("propertyValue", value);
		List<WatchrxPatient> list = query.getResultList();
		return list;
	}

	@Transactional
	@Override
	public int updatePatientVisitVerifyCode(Long patientId, Long verifycode) {

		EntityManager em = entityManagerFactory.createEntityManager();
		em.getTransaction().begin();
		Query query = em
				.createQuery("UPDATE WatchrxPatient e set e.verifycode =:verifycode where e.patientId =:patientId ");

		query.setParameter("patientId", patientId);
		query.setParameter("verifycode", verifycode);
		int result = query.executeUpdate();
		em.getTransaction().commit();
		return result;

	}

	@Override
	public Long getVisitVerificationCodeByPatientId(Long pId) {
		Object num = 0;
		EntityManager em = entityManagerFactory.createEntityManager();

		try {
			num = em.createQuery("SELECT e.verifycode FROM WatchrxPatient e WHERE e.patientId =:patientId")
					.setParameter("patientId", pId).getSingleResult();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return (Long) num;

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> patientCountYearCaseManager(Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery("SELECT MONTH(a.createdDate),COUNT(a) FROM WatchrxPatient a WHERE a.patientId IN "
				+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxPatient.status='Y' AND b.watchrxClinician.clinicianId = :clinicianId)"
				+ "AND YEAR(a.createdDate) = YEAR(CURDATE()) " + "GROUP BY MONTH(a.createdDate)");
		q.setParameter("clinicianId", clinicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> patientCountMonthCaseManager(Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em
				.createQuery("SELECT DAYOFMONTH(a.createdDate),COUNT(a) FROM WatchrxPatient a  WHERE a.patientId IN "
						+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxPatient.status='Y' AND b.watchrxClinician.clinicianId = :clinicianId)"
						+ "AND YEAR(a.createdDate) = YEAR(CURDATE()) AND MONTH(a.createdDate) = MONTH(CURDATE()) "
						+ "GROUP BY DAYOFMONTH(a.createdDate)");
		q.setParameter("clinicianId", clinicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> patientCountWeekCaseManager(Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em
				.createQuery("SELECT DAYOFMONTH(a.createdDate),COUNT(a) FROM WatchrxPatient a  WHERE a.patientId IN "
						+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxPatient.status='Y' AND b.watchrxClinician.clinicianId = :clinicianId)"
						+ "AND YEAR(a.createdDate) = YEAR(CURDATE()) AND WEEK(a.createdDate) = WEEK(CURDATE()) "
						+ "GROUP BY DAYOFMONTH(a.createdDate)");
		q.setParameter("clinicianId", clinicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> patientCountYearPhysician(Long physicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT MONTH(a.createdDate),COUNT(a) FROM WatchrxPatient a WHERE a.status='Y' AND a.watchrxPhysician.physicianId= :physicianId "
						+ "AND YEAR(a.createdDate) = YEAR(CURDATE()) " + "GROUP BY MONTH(a.createdDate)");
		q.setParameter("physicianId", physicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> patientCountMonthPhysician(Long physicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT DAYOFMONTH(a.createdDate),COUNT(a) FROM WatchrxPatient a  WHERE a.status='Y' AND a.watchrxPhysician.physicianId= :physicianId "
						+ "AND YEAR(a.createdDate) = YEAR(CURDATE()) AND MONTH(a.createdDate) = MONTH(CURDATE())"
						+ "GROUP BY DAYOFMONTH(a.createdDate)");
		q.setParameter("physicianId", physicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> patientCountWeekPhysician(Long physicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT DAYOFMONTH(a.createdDate),COUNT(a) FROM WatchrxPatient a  WHERE a.status='Y' AND a.watchrxPhysician.physicianId= :physicianId "
						+ "AND YEAR(a.createdDate) = YEAR(CURDATE()) AND WEEK(a.createdDate) = WEEK(CURDATE())"
						+ "GROUP BY DAYOFMONTH(a.createdDate)");
		q.setParameter("physicianId", physicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@Override
	public Long patientCountPhysicianCaseManager(Long physicianId, Long caseManagerId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em
				.createQuery("SELECT COUNT(a) FROM WatchrxPatient a WHERE a.watchrxPhysician.physicianId= :physicianId "
						+ "AND a.patientId IN ( SELECT e.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt e "
						+ "where e.watchrxClinician.clinicianId= :clinicianId ) AND a.status='Y'");
		q.setParameter("physicianId", physicianId).setParameter("clinicianId", caseManagerId);
		Long result = (Long) q.getSingleResult();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxPatient> patientsPhysicianCaseManagerPaginated(Long physicianId, Long caseManagerId,
			Integer index, Integer pageSize) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery("SELECT a FROM WatchrxPatient a  WHERE a.watchrxPhysician.physicianId= :physicianId "
				+ "AND a.patientId IN ( SELECT e.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt e "
				+ "where e.watchrxClinician.clinicianId= :clinicianId AND e.watchrxPatient.status='Y' )");
		q.setParameter("physicianId", physicianId).setParameter("clinicianId", caseManagerId)
				.setFirstResult(index * pageSize).setMaxResults(pageSize);
		List<WatchrxPatient> result = q.getResultList();

		return result;
	}

	@Override
	public Long patientTotalCountMonthPhysician(Long physicianId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Query q = em.createQuery(
				"SELECT COUNT(a) FROM WatchrxPatient a  WHERE a.status='Y' AND a.watchrxPhysician.physicianId= :physicianId "
						+ "AND YEAR(a.createdDate) = YEAR(CURDATE()) AND MONTH(a.createdDate) = MONTH(CURDATE())");
		q.setParameter("physicianId", physicianId);
		Long result = (Long) q.getSingleResult();
		return result;
	}

	@Override
	public Long patientTotalCountMonthCaseManager(Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery("SELECT COUNT(a) FROM WatchrxPatient a  WHERE a.status='Y' AND a.patientId IN "
				+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = :clinicianId)"
				+ "AND YEAR(a.createdDate) = YEAR(CURDATE()) AND MONTH(a.createdDate) = MONTH(CURDATE()) ");
		q.setParameter("clinicianId", clinicianId);
		Long result = (Long) q.getSingleResult();

		return result;
	}

	@Override
	public Long patientTotalCountCaseManager(Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery("SELECT COUNT(a) FROM WatchrxPatient a  WHERE a.patientId IN "
				+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = :clinicianId) AND a.status='Y' ");
		q.setParameter("clinicianId", clinicianId);
		Long result = (Long) q.getSingleResult();

		return result;
	}

	@Override
	public Long patientTotalCountCaseManager(List<Long> clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery("SELECT COUNT(a) FROM WatchrxPatient a  WHERE a.status='Y' AND a.patientId IN "
				+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId in (:clinicianId))");
		q.setParameter("clinicianId", clinicianId);
		Long result = (Long) q.getSingleResult();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxPatient> getPatientsByPhysicianByName(String name, Long physicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		List<WatchrxPatient> allPatient = em.createQuery(
				"SELECT p FROM WatchrxPatient p where p.status='A' AND p.watchrxPhysician.physicianId= :physicianId AND p.firstName LIKE CONCAT(:name,'%') OR p.lastName LIKE CONCAT(:name,'%') OR p.mrn LIKE CONCAT(:name,'%')")
				.setParameter("physicianId", physicianId).setParameter("name", name).getResultList();
		return allPatient;
	}

	// Added by KV for Pagination
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getAllPatientAddressBilling(Integer index, Integer pageSize, String status) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		try {
			if (status.equals("All")) {
				patientresList = em.createNativeQuery(
						"select pa.PATIENT_ID , concat(pa.FIRST_NAME,\" \",pa.LAST_NAME) PatientName, pa.DOB, ps.STATUS, pa.PRIMARY_CASE_MANAGER_ID "
								+ "	FROM medsure.WATCHRX_PATIENT pa"
								+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_STATE_MACHINE ps"
								+ "	ON (pa.PATIENT_ID = ps.FK_PATIENT_ID)  WHERE pa.status='Y' ")
						.setFirstResult(index * pageSize).setMaxResults(pageSize).getResultList();
			} else {
				patientresList = em.createNativeQuery(
						"select pa.PATIENT_ID , concat(pa.FIRST_NAME,\" \",pa.LAST_NAME) PatientName, pa.DOB, ps.STATUS, pa.PRIMARY_CASE_MANAGER_ID "
								+ "	FROM medsure.WATCHRX_PATIENT pa"
								+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_STATE_MACHINE ps"
								+ "	ON (pa.PATIENT_ID = ps.FK_PATIENT_ID)" + " AND ps.STATUS = :status  "
								+ "   WHERE pa.status='Y'  ")
						.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("status", status)
						.getResultList();

			}
		} finally {
			em.close();
		}
		return patientresList;
	}

	// Added by KV for Pagination
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getPhysicianPatientsAddressBilling(Long userId, Integer index, Integer pageSize,
			String status) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		try {
			if (status.equals("All")) {
				patientresList = em.createNativeQuery(
						"select pa.PATIENT_ID, concat(pa.FIRST_NAME,\" \",pa.LAST_NAME) PatientName, pa.DOB, ps.STATUS, pa.PRIMARY_CASE_MANAGER_ID"
								+ "	FROM medsure.WATCHRX_PATIENT pa" + " JOIN medsure.WATCHRX_PHYSCIAN py"
								+ " ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID AND py.FK_USER_ID = :userId)"
								+ "	JOIN medsure.WATCHRX_PATIENT_STATE_MACHINE ps"
								+ "	ON (pa.PATIENT_ID = ps.FK_PATIENT_ID)" + " AND ps.STATUS = 'IN_PROGRESS'"
								+ "   WHERE pa.status='Y'  ")
						.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("userId", userId)
						.getResultList();
			} else {
				patientresList = em.createNativeQuery(
						"select pa.PATIENT_ID, concat(pa.FIRST_NAME,\" \",pa.LAST_NAME) PatientName, pa.DOB, ps.STATUS, pa.PRIMARY_CASE_MANAGER_ID"
								+ "	FROM medsure.WATCHRX_PATIENT pa" + " JOIN medsure.WATCHRX_PHYSCIAN py"
								+ " ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID AND py.FK_USER_ID = :userId)"
								+ "	JOIN medsure.WATCHRX_PATIENT_STATE_MACHINE ps"
								+ "	ON (pa.PATIENT_ID = ps.FK_PATIENT_ID)" + " AND ps.STATUS = :status"
								+ "    WHERE pa.status='Y'  ")
						.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("userId", userId)
						.setParameter("status", status).getResultList();
			}
		} finally {
			em.close();
		}
		return patientresList;
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<String> getCptCodeForPatient(Long patientId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<String> cptCodeList = null;
		try {
			System.out.println("GET cpt code for PatientId: " + patientId);
			cptCodeList = em.createNativeQuery(
					"select CPT_CODE " + " from medsure.WATCHRX_PATIENT_CPT " + " where FK_PATIENT_ID = :patientId")
					.setParameter("patientId", patientId).getResultList();
		} finally {
			em.close();
		}
		return cptCodeList;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxClinician> getCaseManagerForPatientId(Long patientId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxClinician> clinicianList = null;
		try {
			clinicianList = em.createQuery("select e " + " from WatchrxClinician e "
					+ "	where clinicianId in (select b.watchrxClinician.clinicianId "
					+ "  FROM WatchrxPatientClinicianAssignmnt b  " + " WHERE b.watchrxPatient.patientId = :patientId)")
					.setParameter("patientId", patientId).getResultList();
		} finally {
			em.close();
		}
		return clinicianList;
	}

	// Added by KV for Pagination
	@Override
	@SuppressWarnings("unchecked")
	public List<Object[]> getClinicianPatientAddressBilling(List<Long> userId, Integer index, Integer pageSize,
			String status) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		try {
			if (status.equals("All")) {
				patientresList = em.createNativeQuery(
						"select distinct pa.PATIENT_ID, concat(pa.FIRST_NAME,\" \",pa.LAST_NAME) PatientName, pa.DOB, ps.STATUS, pa.PRIMARY_CASE_MANAGER_ID,pa.PHONE_NUMBER "
								+ "	FROM medsure.WATCHRX_PATIENT pa" + " JOIN  medsure.WATCHRX_CLINICIAN c"
								+ " ON (c.FK_USER_ID IN (:userId))"
								+ " JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT ca"
								+ " ON (c.CLINICIAN_ID = ca.FK_CLINICIAN_ID AND pa.PATIENT_ID = ca.FK_PATIENT_ID)"
								+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_STATE_MACHINE ps"
								+ "	ON (pa.PATIENT_ID = ps.FK_PATIENT_ID" + "  WHERE pa.status='Y' ")
						.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("userId", userId)
						.getResultList();
			} else if (status.equals("CaregiverApp")) {
				patientresList = em.createNativeQuery(
						"select distinct pa.PATIENT_ID, concat(pa.FIRST_NAME,\" \",pa.LAST_NAME) PatientName, pa.DOB, ps.STATUS, pa.PRIMARY_CASE_MANAGER_ID,pa.PHONE_NUMBER "
								+ "	FROM medsure.WATCHRX_PATIENT pa" + " JOIN  medsure.WATCHRX_CLINICIAN c"
								+ " ON (c.FK_USER_ID IN (:userId))"
								+ " JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT ca"
								+ " ON (c.CLINICIAN_ID = ca.FK_CLINICIAN_ID AND pa.PATIENT_ID = ca.FK_PATIENT_ID)"
								+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_STATE_MACHINE ps"
								+ "	ON (pa.PATIENT_ID = ps.FK_PATIENT_ID)" + "  WHERE pa.status='Y' ")
						.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("userId", userId)
						.getResultList();
			} else {
				patientresList = em.createNativeQuery(
						"select distinct pa.PATIENT_ID, concat(pa.FIRST_NAME,\" \",pa.LAST_NAME) PatientName, pa.DOB, ps.STATUS, pa.PRIMARY_CASE_MANAGER_ID,pa.PHONE_NUMBER "
								+ "	FROM medsure.WATCHRX_PATIENT pa" + " JOIN medsure.WATCHRX_CLINICIAN c"
								+ " ON (c.FK_USER_ID IN (:userId))"
								+ " JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT ca"
								+ " ON (c.CLINICIAN_ID = ca.FK_CLINICIAN_ID AND pa.PATIENT_ID = ca.FK_PATIENT_ID)"
								+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_STATE_MACHINE ps"
								+ "	ON (pa.PATIENT_ID = ps.FK_PATIENT_ID)" + " AND ps.STATUS = :status"
								+ "  WHERE pa.status='Y' ")
						.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("userId", userId)
						.setParameter("status", status).getResultList();
			}
		} finally {
			em.close();
		}
		return patientresList;
	}

	// Added by KV for Pagination
	@Override
	@SuppressWarnings("unchecked")
	public List<PatientVO> getAllPatientSummary(Integer index, Integer pageSize) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		try {
			patientresList = em.createNativeQuery(
					"SELECT pa.PATIENT_ID, pa.FIRST_NAME , pa.LAST_NAME, pa.IMG_PATH, pa.PHONE_NUMBER,pa.MRN,"
							+ " concat(py.FIRST_NAME,\" \",py.LAST_NAME) Physician_Name," + " cc.CHRONIC_CONDITION_NAME"
							+ "	FROM medsure.WATCHRX_PATIENT pa" + "	LEFT OUTER JOIN medsure.WATCHRX_PHYSCIAN py"
							+ "	ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID)"
							+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CHRONIC_CONDITIONS cc"
							+ "	ON (pa.PATIENT_ID = cc.FK_PATIENT_ID)" + " WHERE pa.status='Y' ")
					.getResultList();
		} finally {
			em.close();
		}
		return joinchronicObjects(patientresList);
	}

	// Added by KV for Pagination
	@SuppressWarnings("unchecked")
	@Override
	public List<PatientVO> getPhysicianPatientsByUserId(Long userId, Integer index, Integer pageSize) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		try {
			patientresList = em
					.createNativeQuery(
							"SELECT pa.PATIENT_ID, pa.FIRST_NAME , pa.LAST_NAME, pa.IMG_PATH, pa.PHONE_NUMBER,pa.MRN,"
									+ " concat(py.FIRST_NAME,\" \",py.LAST_NAME) Physician_Name,"
									+ " cc.CHRONIC_CONDITION_NAME" + "	FROM medsure.WATCHRX_PATIENT pa"
									+ "	JOIN medsure.WATCHRX_PHYSCIAN py"
									+ "	ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID AND py.FK_USER_ID = :userId)"
									+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CHRONIC_CONDITIONS cc"
									+ "	ON (pa.PATIENT_ID = cc.FK_PATIENT_ID)" + "  WHERE pa.status='Y' ")
					.setParameter("userId", userId).setFirstResult(index * pageSize).setMaxResults(pageSize)
					.getResultList();
		} finally {
			em.close();
		}
		// em.close();

		return joinchronicObjects(patientresList);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PatientVO> getPhysicianPatientsByUserIds(List<Long> userId, Integer index, Integer pageSize) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		try {
			patientresList = em
					.createNativeQuery(
							"SELECT pa.PATIENT_ID, pa.FIRST_NAME , pa.LAST_NAME, pa.IMG_PATH, pa.PHONE_NUMBER,pa.MRN,"
									+ " concat(py.FIRST_NAME,\" \",py.LAST_NAME) Physician_Name,"
									+ " cc.CHRONIC_CONDITION_NAME" + "	FROM medsure.WATCHRX_PATIENT pa"
									+ "	JOIN medsure.WATCHRX_PHYSCIAN py"
									+ "	ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID AND py.FK_USER_ID in (:userId))"
									+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CHRONIC_CONDITIONS cc"
									+ "	ON (pa.PATIENT_ID = cc.FK_PATIENT_ID)" + "  WHERE pa.status='Y' ")
					.setParameter("userId", userId).setFirstResult(index * pageSize).setMaxResults(pageSize)
					.getResultList();
		} finally {
			em.close();
		}
		// em.close();

		return joinchronicObjects(patientresList);
	}

	// Added by KV for Pagination
	@SuppressWarnings("unchecked")
	@Override
	public List<PatientVO> getClinicianPatientsByUserId(Long userId, Integer index, Integer pageSize) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		System.out.println(">>>>>>>>>>>>>>>>>>>>>>>> 3 : DB query patients for user,clinician >>>>>>>>>>>");
		try {
			patientresList = em
					.createNativeQuery(
							"SELECT pa.PATIENT_ID, pa.FIRST_NAME , pa.LAST_NAME, pa.IMG_PATH, pa.PHONE_NUMBER,pa.MRN,"
									+ " concat(py.FIRST_NAME,\" \",py.LAST_NAME) Physician_Name,"
									+ " cc.CHRONIC_CONDITION_NAME" + "	FROM medsure.WATCHRX_PATIENT pa"
									+ "	JOIN  medsure.WATCHRX_CLINICIAN c" + "	ON (c.FK_USER_ID = :userId)"
									+ "	JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT ca"
									+ "	ON (c.CLINICIAN_ID = ca.FK_CLINICIAN_ID AND pa.PATIENT_ID = ca.FK_PATIENT_ID)"
									+ "	LEFT OUTER JOIN medsure.WATCHRX_PHYSCIAN py"
									+ "	ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID)"
									+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CHRONIC_CONDITIONS cc"
									+ "	ON (pa.PATIENT_ID = cc.FK_PATIENT_ID)" + "  WHERE pa.status='Y' ")
					.setParameter("userId", userId).setFirstResult(index * pageSize).setMaxResults(pageSize)
					.getResultList();

			System.out.println(">>>>>>>>>>>>>>>>>>>>>>>> Query list size >>>>>>>>>>>  " + patientresList.size());
		} finally {
			em.close();
		}
		// joinchronicObjects(patientsList);
		return joinchronicObjects(patientresList);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PatientVO> getClinicianPatientsByUserIds(List<Long> userId, Integer index, Integer pageSize) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		System.out.println(">>>>>>>>>>>>>>>>>>>>>>>> 3 : DB query patients for user,clinician >>>>>>>>>>>");
		try {
			patientresList = em
					.createNativeQuery(
							"SELECT pa.PATIENT_ID, pa.FIRST_NAME , pa.LAST_NAME, pa.IMG_PATH, pa.PHONE_NUMBER,pa.MRN,"
									+ " concat(py.FIRST_NAME,\" \",py.LAST_NAME) Physician_Name,"
									+ " cc.CHRONIC_CONDITION_NAME" + "	FROM medsure.WATCHRX_PATIENT pa"
									+ "	JOIN  medsure.WATCHRX_CLINICIAN c" + "	ON (c.FK_USER_ID in (:userId))"
									+ "	JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT ca"
									+ "	ON (c.CLINICIAN_ID = ca.FK_CLINICIAN_ID AND pa.PATIENT_ID = ca.FK_PATIENT_ID)"
									+ "	LEFT OUTER JOIN medsure.WATCHRX_PHYSCIAN py"
									+ "	ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID)"
									+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CHRONIC_CONDITIONS cc"
									+ "	ON (pa.PATIENT_ID = cc.FK_PATIENT_ID)" + "  WHERE pa.status='Y' ")
					.setParameter("userId", userId).setFirstResult(index * pageSize).setMaxResults(pageSize)
					.getResultList();

			System.out.println(">>>>>>>>>>>>>>>>>>>>>>>> Query list size >>>>>>>>>>>  " + patientresList.size());
		} finally {
			em.close();
		}
		// joinchronicObjects(patientsList);
		return joinchronicObjects(patientresList);
	}

	public List<PatientVO> joinchronicObjects(List<Object[]> patientssummList) {

		HashMap<Long, PatientVO> patiMap = new HashMap<Long, PatientVO>();
		List<PatientVO> patientsList = new ArrayList<PatientVO>();

		for (Object[] summaryObjt : patientssummList) {

			Long pid = Long.valueOf(summaryObjt[0].toString());

			/*
			 * if (patiMap.containsKey(pid)) { PatientVO temp = patiMap.get(pid);
			 * List<CreatePatientRequestChronicConditionsVO> chronicConditions =
			 * temp.getChronicConditions(); CreatePatientRequestChronicConditionsVO ch = new
			 * CreatePatientRequestChronicConditionsVO(); if (summaryObjt[6] != null) {
			 * ch.setChronicConditionName(summaryObjt[6].toString()); }
			 * chronicConditions.add(ch);
			 * chronicConditions.stream().distinct().collect(Collectors.toList()); } else {
			 */
			PatientVO pvo = new PatientVO();
			pvo.setPatientId(pid);

			if (null != summaryObjt[1]) {
				pvo.setFirstName(summaryObjt[1].toString());
			}

			if (null != summaryObjt[2]) {
				pvo.setLastName(summaryObjt[2].toString());
			}

			if (null != summaryObjt[3]) {
				pvo.setPicPath(WatchRxUtils.readTextFileOnly(summaryObjt[3].toString()));
			}

			if (null != summaryObjt[4]) {
				pvo.setPhoneNumber(summaryObjt[4].toString());
			}

			if (null != summaryObjt[5]) {
				pvo.setMrn(String.valueOf(summaryObjt[5]));
			}

			if (null != summaryObjt[6]) {
				pvo.setPhysicianName(summaryObjt[6].toString());
			}

			List<CreatePatientRequestChronicConditionsVO> chronicConditions = new ArrayList<CreatePatientRequestChronicConditionsVO>();
			if (null != summaryObjt[7]) {
				String[] arr = String.valueOf(summaryObjt[7]).split(",");
				for (int i = 0; i < arr.length; i++) {
					CreatePatientRequestChronicConditionsVO ch = new CreatePatientRequestChronicConditionsVO();

					ch.setChronicConditionName(arr[i].toString());
					chronicConditions.add(ch);
				}
			}

			try {
				if (null != summaryObjt[8]) {
					pvo.setGroupName(summaryObjt[8].toString());
				}
			} catch (Exception e) {

			}

			if (null != summaryObjt[9]) {
				pvo.setConsentStatus(summaryObjt[9].toString());
			}

			pvo.setChronicConditions(chronicConditions);
			patientsList.add(pvo);
			patiMap.put(pid, pvo);
			// }

		}
		return patientsList;
	}

	@Override
	public String getTimeZoneByPatientId(Long pId) {
		Object zone = null;
		EntityManager em = entityManagerFactory.createEntityManager();

		try {
			zone = em.createQuery("SELECT e.timeZoneId FROM WatchrxPatient e WHERE e.patientId =:patientId")
					.setParameter("patientId", pId).getSingleResult();
		} finally {
			em.close();
		}
		return (String) zone;

	}

	@Override
	public List<WatchrxPatient> authenticatePatient(String email, String otp) {
		System.out.println("In PatientDAOImpl:findByProperty");
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery("SELECT e FROM WatchrxPatient e WHERE status='" + Constants.Status.ACTIVE
				+ "' AND ( e.email = :propertyValue1 OR e.phoneNumber = :propertyValue1 ) "
				+ "  AND e.otp = :propertyValue2");
		query.setParameter("propertyValue1", email);
		query.setParameter("propertyValue2", Integer.valueOf(otp));
		@SuppressWarnings("unchecked")
		List<WatchrxPatient> list = query.getResultList();
		return list;
	}

	@Override
	public List<WatchrxPatient> validatePatientOTPStatus(String email, String otpStatus) {
		System.out.println("In PatientDAOImpl:findByProperty");
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery("SELECT e FROM WatchrxPatient e WHERE status='" + Constants.Status.ACTIVE
				+ "' AND ( e.email = :propertyValue1 OR e.phoneNumber = :propertyValue1 ) "
				+ "  AND e.otpStatus = :propertyValue2");
		query.setParameter("propertyValue1", email);
		query.setParameter("propertyValue2", otpStatus);
		@SuppressWarnings("unchecked")
		List<WatchrxPatient> list = query.getResultList();
		return list;
	}

	@Override
	public List<WatchrxPatient> authorizePatient(String email, String password) {
		System.out.println("In PatientDAOImpl:findByProperty");
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery("SELECT e FROM WatchrxPatient e WHERE status='" + Constants.Status.ACTIVE
				+ "' AND ( e.email = :propertyValue1 OR e.phoneNumber = :propertyValue1 ) "
				+ "  AND e.password = :propertyValue2");
		query.setParameter("propertyValue1", email);
		query.setParameter("propertyValue2", password);
		@SuppressWarnings("unchecked")
		List<WatchrxPatient> list = query.getResultList();
		return list;
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<WatchrxPatient> findByMRN(String value) {
		System.out.println("In PatientDAOImpl:findByProperty");
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery("SELECT e FROM WatchrxPatient e WHERE status='" + Constants.Status.ACTIVE
				+ "' AND e.mrn= :propertyValue");
		query.setParameter("propertyValue", value);
		List<WatchrxPatient> list = query.getResultList();
		return list;
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<WatchrxPatient> findByDeviceTypeAndPlatform(String devType, String platform) {
		System.out.println("In findByDeviceTypeAndPlatform");
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery("SELECT e FROM WatchrxPatient e WHERE status='" + Constants.Status.ACTIVE
				+ "' AND e.connectingDevice= :propertyValue1" + " AND e.platform= :propertyValue2");
		query.setParameter("propertyValue1", devType);
		query.setParameter("propertyValue2", platform);
		List<WatchrxPatient> list = query.getResultList();
		return list;
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<PatientVO> getCareGiverPatientsByUserId(Long userId, Integer index, Integer pageSize) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		try {
			patientresList = em
					.createNativeQuery(
							"SELECT pa.PATIENT_ID, pa.FIRST_NAME , pa.LAST_NAME, pa.IMG_PATH, pa.PHONE_NUMBER,pa.MRN,"
									+ " concat(py.FIRST_NAME,\" \",py.LAST_NAME) Physician_Name,"
									+ " cc.CHRONIC_CONDITION_NAME" + "	FROM medsure.WATCHRX_PATIENT pa"
									+ "	JOIN  medsure.WATCHRX_CLINICIAN c" + "	ON (c.FK_USER_ID = :userId)"
									+ "	JOIN medsure.WATCHRX_PATIENT_CARE_GIVER_ASSIGNMNT ca"
									+ "	ON (c.CLINICIAN_ID = ca.FK_CLINICIAN_ID AND pa.PATIENT_ID = ca.FK_PATIENT_ID)"
									+ "	LEFT OUTER JOIN medsure.WATCHRX_PHYSCIAN py"
									+ "	ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID)"
									+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CHRONIC_CONDITIONS cc"
									+ "	ON (pa.PATIENT_ID = cc.FK_PATIENT_ID)" + "  WHERE pa.status='Y' ")
					.setParameter("userId", userId).setFirstResult(index * pageSize).setMaxResults(pageSize)
					.getResultList();

			System.out.println(">>>>>>>>>>>>>>>>>>>>>>>> Query list size >>>>>>>>>>>  " + patientresList.size());
		} finally {
			em.close();
		}
		// joinchronicObjects(patientsList);
		return joinchronicObjects(patientresList);
	}

	@Override
	public WatchrxPatient doPatientLogin(String email, String password) {
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			Query query = em.createQuery("SELECT e FROM WatchrxPatient e WHERE status='" + Constants.Status.ACTIVE
					+ "' AND e.email = :propertyValue1" + "  AND e.password = :propertyValue2");
			query.setParameter("propertyValue1", email);
			query.setParameter("propertyValue2", password);
			query.setMaxResults(1);
			WatchrxPatient patient = (WatchrxPatient) query.getSingleResult();
			return patient;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getPhysicianPatientsAddressBilling(List<Long> userId, Integer index, Integer pageSize,
			String status) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		try {
			if (status.equals("All")) {
				patientresList = em.createNativeQuery(
						"select pa.PATIENT_ID, concat(pa.FIRST_NAME,\" \",pa.LAST_NAME) PatientName, pa.DOB, ps.STATUS, pa.PRIMARY_CASE_MANAGER_ID , pa.PHONE_NUMBER"
								+ "	FROM medsure.WATCHRX_PATIENT pa" + " JOIN medsure.WATCHRX_PHYSCIAN py"
								+ " ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID AND py.FK_USER_ID IN (:userId))"
								+ "	JOIN medsure.WATCHRX_PATIENT_STATE_MACHINE ps"
								+ "	ON (pa.PATIENT_ID = ps.FK_PATIENT_ID)" + " AND ps.STATUS = 'IN_PROGRESS'"
								+ "  WHERE pa.status='Y' ")
						.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("userId", userId)
						.getResultList();
			} else {
				patientresList = em.createNativeQuery(
						"select pa.PATIENT_ID, concat(pa.FIRST_NAME,\" \",pa.LAST_NAME) PatientName, pa.DOB, ps.STATUS, pa.PRIMARY_CASE_MANAGER_ID , pa.PHONE_NUMBER"
								+ "	FROM medsure.WATCHRX_PATIENT pa" + " JOIN medsure.WATCHRX_PHYSCIAN py"
								+ " ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID AND py.FK_USER_ID IN (:userId))"
								+ "	JOIN medsure.WATCHRX_PATIENT_STATE_MACHINE ps"
								+ "	ON (pa.PATIENT_ID = ps.FK_PATIENT_ID)" + " AND ps.STATUS = :status"
								+ "  WHERE pa.status='Y' ")
						.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("userId", userId)
						.setParameter("status", status).getResultList();
			}
		} finally {
			em.close();
		}
		return patientresList;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxPatient> getAllPatientsForOrg(List<Long> userId, Long roleType) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxPatient> patientresList = null;
		try {
			if (roleType != null && roleType == 3) {
				Query q = em.createQuery(
						"SELECT a FROM WatchrxPatient a WHERE a.status='Y' AND a.watchrxPhysician.physicianId IN "
								+ "(SELECT p.physicianId FROM WatchRxPhysician p WHERE  p.watchrxUser.userId IN (:userId))");
				q.setParameter("userId", userId);
				patientresList = q.getResultList();
			} else {
				Query q = em.createQuery("SELECT p FROM WatchrxPatient p WHERE p.status='Y' AND p.patientId IN "
						+ "	(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b"
						+ " WHERE b.watchrxClinician.clinicianId in "
						+ " (SELECT c.clinicianId FROM WatchrxClinician c WHERE c.watchrxUser.userId IN (:userId)))");
				q.setParameter("userId", userId);
				patientresList = q.getResultList();
			}
		} finally {
			em.close();
		}
		return patientresList;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxPatient> getAllPatientsForOrg(Set<Long> userId, Long roleType) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxPatient> patientresList = null;
		try {
			if (roleType != null && roleType == 3) {
				Query q = em.createQuery(
						"SELECT a FROM WatchrxPatient a WHERE a.status='Y' and a.watchrxPhysician.physicianId IN "
								+ "(SELECT p.physicianId FROM WatchRxPhysician p WHERE  p.watchrxUser.userId IN (:userId))");
				q.setParameter("userId", userId);
				patientresList = q.getResultList();
			} else {
				Query q = em.createQuery("SELECT p FROM WatchrxPatient p WHERE a.status='Y' and p.patientId IN "
						+ "	(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b"
						+ " WHERE b.watchrxClinician.clinicianId in "
						+ " (SELECT c.clinicianId FROM WatchrxClinician c WHERE c.watchrxUser.userId IN (:userId)))");
				q.setParameter("userId", userId);
				patientresList = q.getResultList();
			}
		} finally {
			em.close();
		}
		return patientresList;
	}

	@Override
	public Long getAllPatientsCountByOrgId(Long orgId, Long userId) {
		Long patientCount = 0L;
		try {
			EntityManager em = entityManagerFactory.createEntityManager();

			Query q = em.createQuery(
					"SELECT COUNT(a) FROM WatchrxPatient a  WHERE a.watchrxGroup.groupId =:orgId AND a.status = 'Y' and a.watchrxClinician.clinicianId IN"
							+ " (SELECT c.clinicianId FROM WatchRxClinician c WHERE  c.watchrxUser.userId IN (:userId))");
			q.setParameter("orgId", orgId);
			q.setParameter("userId", userId);
			return (Long) q.getSingleResult();

		} catch (Exception e) {
			patientCount = 0L;
		}
		return patientCount;
	}

	@Override
	public Long getAllPatientsCountByOrgIdNoStatus(Long orgId, Long userId) {
		Long patientCount = 0L;
		EntityManager em = null;
		try {
			em = entityManagerFactory.createEntityManager();
			String baseQuery = "SELECT COUNT(DISTINCT a) FROM WatchrxPatient a " + "JOIN a.watchrxGroup g ";

			if (userId != null && userId != 0) {
				baseQuery += " JOIN a.watchrxPatientClinicianAssignmnts pca " + "JOIN pca.watchrxClinician c "
						+ "JOIN c.watchrxUser u ";
			}
			baseQuery += "WHERE g.groupId = :orgId ";
			if (userId != null && userId != 0) {
				baseQuery += "AND u.userId = :userId ";
			}

			Query q = em.createQuery(baseQuery);
			q.setParameter("orgId", orgId);

			if (userId != null && userId != 0) {
				q.setParameter("userId", userId);
			}

			patientCount = (Long) q.getSingleResult();
			System.out.println(patientCount);

		} catch (Exception e) {
			patientCount = 0L;
		} finally {
			if (em != null) {
				em.close();
			}
		}
		return patientCount;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getAllPatientsBillingAdressInfo(Long orgId, Integer index, Integer pageSize, Long userId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		try {
			patientresList = em.createNativeQuery(
					"select distinct pa.PATIENT_ID, concat(pa.FIRST_NAME,\" \",pa.LAST_NAME) PatientName, pa.DOB, pa.PRIMARY_CASE_MANAGER_ID, pa.PHONE_NUMBER,"
							+ " concat(c.FIRST_NAME,\" \",c.LAST_NAME) CaregiverName "
							+ "	FROM medsure.WATCHRX_PATIENT pa "
							+ " LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT ca"
							+ " ON (pa.PATIENT_ID = ca.FK_PATIENT_ID) LEFT OUTER JOIN medsure.WATCHRX_CLINICIAN c "
							+ " ON (ca.FK_CLINICIAN_ID = c.CLINICIAN_ID)"
							+ " WHERE c.FK_USER_ID = :userId AND pa.GROUP_ID =:orgId AND pa.STATUS = 'Y' ")
					.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
					.setParameter("userId", userId).getResultList();
		} finally {
			em.close();
		}
		return patientresList;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxPatient> getAllPatientsForOrgV1(Long orgId, Long userId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxPatient> patientsList = null;
		try {
			Query q = em.createQuery(
					"SELECT a FROM WatchrxPatient a WHERE a.watchrxGroup.groupId =:orgId AND a.status='Y' AND a.watchrxClinician.watchrxUser.userId=:userId ");
			q.setParameter("userId", userId);
			q.setParameter("orgId", orgId);
			patientsList = q.getResultList();
		} finally {
			em.close();
		}
		return patientsList;
	}

	@Override
	public Long patientTotalCountMonthOrgId(Long orgId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Query q = em.createQuery("SELECT COUNT(a) FROM WatchrxPatient a  WHERE a.watchrxGroup.groupId =:orgId "
					+ "AND YEAR(a.createdDate) = YEAR(CURDATE()) AND MONTH(a.createdDate) = MONTH(CURDATE()) AND a.status='Y' ");
			q.setParameter("orgId", orgId);
			return (Long) q.getSingleResult();
		} catch (Exception e) {
			return 0L;
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PatientVO> getAllPatientsByOrgId(Long orgId, Integer index, Integer pageSize, Long userId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {

			String baseQuery = "SELECT DISTINCT pa.PATIENT_ID, pa.FIRST_NAME , pa.LAST_NAME, pa.IMG_PATH, pa.PHONE_NUMBER,pa.MRN,"
					+ " concat(py.FIRST_NAME,\" \",py.LAST_NAME) Physician_Name,"
					+ " GROUP_CONCAT(cc.CHRONIC_CONDITION_NAME), g.GROUP_NAME, pa.CONSENT_STATUS FROM medsure.WATCHRX_PATIENT pa"
					+ "	LEFT OUTER JOIN medsure.WATCHRX_PHYSCIAN py" + "	ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID)"
					+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CHRONIC_CONDITIONS cc"
					+ "	ON (pa.PATIENT_ID = cc.FK_PATIENT_ID) JOIN medsure.WATCHRX_GROUP g "
					+ " ON (pa.GROUP_ID = g.GROUP_ID)";

			if (userId != null && userId != 0) {
				baseQuery += " JOIN  medsure.WATCHRX_CLINICIAN c" + " ON (c.FK_USER_ID IN (:userId))"
						+ " JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT ca"
						+ " ON (c.CLINICIAN_ID = ca.FK_CLINICIAN_ID AND pa.PATIENT_ID = ca.FK_PATIENT_ID)";
			}

			baseQuery += " WHERE pa.GROUP_ID =:orgId GROUP BY  pa.PATIENT_ID";

			Query query = em.createNativeQuery(baseQuery).setParameter("orgId", orgId).setFirstResult(index * pageSize)
					.setMaxResults(pageSize);
			if (userId != null && userId != 0) {
				query.setParameter("userId", userId);
			}
			patientsList = query.getResultList();

		} finally {
			em.close();
		}
		return joinchronicObjects(patientsList);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxPatient> getAllPatientsByOrgId(Long orgId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxPatient> watchrxPatients = new ArrayList<>();
		try {
			Query q = em.createQuery(
					"SELECT a FROM WatchrxPatient a WHERE a.status='Y' AND a.watchrxGroup.groupId =:orgId");
			q.setParameter("orgId", orgId);
			watchrxPatients = q.getResultList();
		} finally {
			em.close();
		}
		return watchrxPatients;
	}

	@Override
	public Long getPatientsCountByOrgIdAndSearch(Long orgId, String search, Long clinicianId) {
		Long patientCount = 0L;
		try {
			EntityManager em = entityManagerFactory.createEntityManager();

			Query q = em.createQuery(
					"SELECT COUNT(a) FROM WatchrxPatient a  WHERE a.watchrxGroup.groupId =:orgId AND a.status = 'Y' AND "
							+ "(a.firstName LIKE CONCAT('%',:name,'%') OR a.lastName LIKE CONCAT('%',:name,'%') OR a.mrn LIKE CONCAT('%',:name,'%')) AND "
							+ " a.patientId IN "
							+ "	(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = :clinicianId)");
			q.setParameter("orgId", orgId);
			q.setParameter("name", search);
			q.setParameter("clinicianId", clinicianId);
			return (Long) q.getSingleResult();

		} catch (Exception e) {
			patientCount = 0L;
		}
		return patientCount;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getAllPatientsBillingAdressInfoAndSearch(Long orgId, Integer index, Integer pageSize,
			String search) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		try {
			patientresList = em.createNativeQuery(
					"select distinct pa.PATIENT_ID, concat(pa.FIRST_NAME,\" \",pa.LAST_NAME) PatientName, pa.DOB, pa.PRIMARY_CASE_MANAGER_ID, pa.PHONE_NUMBER,"
							+ " concat(c.FIRST_NAME,\" \",c.LAST_NAME) CaregiverName "
							+ "	FROM medsure.WATCHRX_PATIENT pa "
							+ " LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT ca"
							+ " ON (pa.PATIENT_ID = ca.FK_PATIENT_ID) LEFT OUTER JOIN medsure.WATCHRX_CLINICIAN c "
							+ " ON (ca.FK_CLINICIAN_ID = c.CLINICIAN_ID)"
							+ " WHERE pa.GROUP_ID =:orgId AND pa.STATUS = 'Y' AND"
							+ " (pa.FIRST_NAME LIKE CONCAT('%',:name,'%') OR pa.LAST_NAME LIKE CONCAT('%',:name,'%') OR pa.MRN LIKE CONCAT('%',:name,'%'))")
					.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
					.setParameter("name", search).getResultList();
		} finally {
			em.close();
		}
		return patientresList;
	}

	@Override
	public Long getAllCount() {
		EntityManager em = entityManagerFactory.createEntityManager();
		Long count;
		try {
			count = (Long) em.createQuery("SELECT COUNT(a) FROM WatchrxPatient a  WHERE a.status = 'Y'")
					.getSingleResult();
		} finally {
			em.close();
		}
		return count;
	}

	@Override
	public Long findByPropertyCount(String propertyName, Object value) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Long count;
		try {
			Query query = em.createQuery("SELECT COUNT(e) FROM WatchrxPatient e WHERE e.status = 'Y' AND e."
					+ propertyName + "= :propertyValue");
			// SELECT e FROM com.medsure.model.WatchrxPatient e WHERE status='Y' AND
			// e.watchrxPatient.ssn= :propertyValue
			query.setParameter("propertyValue", value);
			count = (Long) query.getSingleResult();
		} finally {
			em.close();
		}
		return count;
	}

	@Override
	public int updateStatus(Long patientId, @NotBlank String status) {
		EntityManager em = entityManagerFactory.createEntityManager();
		em.getTransaction().begin();
		Query query = em.createQuery("UPDATE WatchrxPatient e set e.status =:status where e.patientId =:patientId ");

		query.setParameter("patientId", patientId);
		query.setParameter("status", status);
		int result = query.executeUpdate();
		em.getTransaction().commit();
		return result;
	}

	@Override
	public Long getAllCountByStatus(String status) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Long count;
		try {
			count = (Long) em.createQuery("SELECT COUNT(a) FROM WatchrxPatient a  WHERE a.status ='" + status + "'")
					.getSingleResult();
		} finally {
			em.close();
		}
		return count;
	}

	@Override
	public Long getAllPatientsCountByOrgIdAndStatus(Long orgId, String status, Long userId) {
		Long patientCount = 0L;
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			Query q = em.createQuery(
					"SELECT COUNT(a) FROM WatchrxPatient a WHERE a.watchrxGroup.groupId =:orgId AND a.status ='"
							+ status + "' AND a.patientId IN "
							+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = "
							+ "(SELECT c.clinicianId FROM WatchrxClinician c WHERE c.watchrxUser.userId IN (:userId)))");
			q.setParameter("orgId", orgId);
			q.setParameter("userId", userId);
			return (Long) q.getSingleResult();

		} catch (Exception e) {
			patientCount = 0L;
		}
		return patientCount;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PatientVO> getAllPatientSummaryByStatus(Integer index, Integer pageSize, String status) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		try {
			patientresList = em
					.createNativeQuery(
							"SELECT pa.PATIENT_ID, pa.FIRST_NAME , pa.LAST_NAME, pa.IMG_PATH, pa.PHONE_NUMBER,pa.MRN,"
									+ " concat(py.FIRST_NAME,\" \",py.LAST_NAME) Physician_Name,"
									+ " cc.CHRONIC_CONDITION_NAME, g.GROUP_NAME" + "	FROM medsure.WATCHRX_PATIENT pa"
									+ "	LEFT OUTER JOIN medsure.WATCHRX_PHYSCIAN py"
									+ "	ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID)"
									+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CHRONIC_CONDITIONS cc"
									+ "	ON (pa.PATIENT_ID = cc.FK_PATIENT_ID) JOIN medsure.WATCHRX_GROUP g "
									+ " ON (pa.GROUP_ID = g.GROUP_ID)" + " WHERE pa.status='" + status + "'")
					.setFirstResult(index * pageSize).setMaxResults(pageSize).getResultList();
		} finally {
			em.close();
		}
		return joinchronicObjects(patientresList);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PatientVO> getAllPatientsByOrgIdAndStatus(Long orgId, Integer index, Integer pageSize, String status,
			Long userId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {

//			patientsList = em.createNativeQuery(
//					"SELECT DISTINCT pa.PATIENT_ID, pa.FIRST_NAME , pa.LAST_NAME, pa.IMG_PATH, pa.PHONE_NUMBER,pa.MRN,"
//							+ " concat(py.FIRST_NAME,\" \",py.LAST_NAME) Physician_Name,"
//							+ " GROUP_CONCAT(cc.CHRONIC_CONDITION_NAME), g.GROUP_NAME, pa.CONSENT_STATUS FROM medsure.WATCHRX_PATIENT pa"
//							+ "	LEFT OUTER JOIN medsure.WATCHRX_PHYSCIAN py"
//							+ "	ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID)" + " JOIN  medsure.WATCHRX_CLINICIAN c"
//							+ " ON (c.FK_USER_ID IN (:userId))" + " JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT ca"
//							+ " ON (c.CLINICIAN_ID = ca.FK_CLINICIAN_ID AND pa.PATIENT_ID = ca.FK_PATIENT_ID)"
//							+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CHRONIC_CONDITIONS cc"
//							+ "	ON (pa.PATIENT_ID = cc.FK_PATIENT_ID) JOIN medsure.WATCHRX_GROUP g "
//							+ " ON (pa.GROUP_ID = g.GROUP_ID)"
//							+ " WHERE pa.GROUP_ID =:orgId AND pa.status='Y' GROUP BY  pa.PATIENT_ID")
//					.setParameter("userId", userId).setParameter("orgId", orgId).setFirstResult(index * pageSize)
//					.setMaxResults(pageSize).getResultList();

			patientsList = em.createNativeQuery(
					"SELECT DISTINCT pa.PATIENT_ID, pa.FIRST_NAME , pa.LAST_NAME, pa.IMG_PATH, pa.PHONE_NUMBER,pa.MRN,"
							+ " concat(py.FIRST_NAME,\" \",py.LAST_NAME) Physician_Name,"
							+ " GROUP_CONCAT(cc.CHRONIC_CONDITION_NAME), g.GROUP_NAME, pa.CONSENT_STATUS FROM medsure.WATCHRX_PATIENT pa"
							+ "	LEFT OUTER JOIN medsure.WATCHRX_PHYSCIAN py"
							+ "	ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID)"
							+ " JOIN medsure.WATCHRX_CLINICIAN c ON (c.FK_USER_ID IN (:userId))"
							+ " JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT ca  ON (c.CLINICIAN_ID = ca.FK_CLINICIAN_ID AND pa.PATIENT_ID = ca.FK_PATIENT_ID)"
							+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CHRONIC_CONDITIONS cc"
							+ "	ON (pa.PATIENT_ID = cc.FK_PATIENT_ID) JOIN medsure.WATCHRX_GROUP g "
							+ " ON (pa.GROUP_ID = g.GROUP_ID)" + " WHERE pa.GROUP_ID =:orgId AND pa.status='" + status
							+ "' GROUP BY  pa.PATIENT_ID")
					.setParameter("userId", userId).setParameter("orgId", orgId).setFirstResult(index * pageSize)
					.setMaxResults(pageSize).getResultList();
		} finally {
			em.close();
		}
		return joinchronicObjects(patientsList);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> dashboardPatientCount(Long orgId, Long clinicianId, Integer role) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			if (role == 3) {
//				patientsList = em.createNativeQuery("SELECT COUNT(*) AS total_patients, "
//						+ " SUM(CASE WHEN p.status = 'Y' THEN 1 ELSE 0 END) AS active_patients, "
//						+ " SUM(CASE WHEN p.status = 'N' THEN 1 ELSE 0 END) AS inactive_patients,"
//						+ " SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE())"
//						+ " AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE()) THEN 1 ELSE 0 END) AS active_patients_current_month FROM medsure.WATCHRX_PATIENT p"
//						+ " WHERE p.GROUP_ID =:orgId ").setParameter("orgId", orgId).getResultList();

				patientsList = em.createNativeQuery("SELECT COUNT(*) AS total_patients, "
						+ "       SUM(CASE WHEN p.status = 'Y' THEN 1 ELSE 0 END) AS active_patients, "
						+ "       SUM(CASE WHEN p.status = 'N' THEN 1 ELSE 0 END) AS inactive_patients, "
						+ "       SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE()) "
						+ "                AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE()) THEN 1 ELSE 0 END) AS active_patients_current_month, "
						+ "       SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH) "
						+ "                AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH) THEN 1 ELSE 0 END) AS active_patients_last_month, "
						+ "       CASE "
						+ "           WHEN SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH) "
						+ "                        AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH) THEN 1 ELSE 0 END) = 0 THEN NULL "
						+ "           ELSE "
						+ "               ((SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE()) "
						+ "                           AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE()) THEN 1 ELSE 0 END) - "
						+ "                 SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH) "
						+ "                           AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH) THEN 1 ELSE 0 END)) / "
						+ "                SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH) "
						+ "                           AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH) THEN 1 ELSE 0 END)) * 100 "
						+ "       END AS growth_percentage " + "FROM medsure.WATCHRX_PATIENT p "
						+ "WHERE p.GROUP_ID = :orgId").setParameter("orgId", orgId).getResultList();

			} else {
//				patientsList = em.createNativeQuery("SELECT COUNT(*) AS total_patients, "
//						+ " SUM(CASE WHEN p.status = 'Y' THEN 1 ELSE 0 END) AS active_patients, "
//						+ " SUM(CASE WHEN p.status = 'N' THEN 1 ELSE 0 END) AS inactive_patients,"
//						+ " SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE())"
//						+ " AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE()) THEN 1 ELSE 0 END) AS active_patients_current_month FROM medsure.WATCHRX_PATIENT p"
//						+ " WHERE p.PATIENT_ID IN (SELECT c.FK_PATIENT_ID FROM WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT c WHERE "
//						+ " c.FK_CLINICIAN_ID =:clinicianId) AND p.GROUP_ID =:orgId ").setParameter("orgId", orgId)
//						.setParameter("clinicianId", clinicianId).getResultList();

				patientsList = em.createNativeQuery("SELECT COUNT(*) AS total_patients, "
						+ "       SUM(CASE WHEN p.status = 'Y' THEN 1 ELSE 0 END) AS active_patients, "
						+ "       SUM(CASE WHEN p.status = 'N' THEN 1 ELSE 0 END) AS inactive_patients, "
						+ "       SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE()) "
						+ "                AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE()) THEN 1 ELSE 0 END) AS active_patients_current_month, "
						+ "       SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH) "
						+ "                AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH) THEN 1 ELSE 0 END) AS active_patients_last_month, "
						+ "       CASE "
						+ "           WHEN SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH) "
						+ "                        AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH) THEN 1 ELSE 0 END) = 0 THEN NULL "
						+ "           ELSE "
						+ "               ((SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE()) "
						+ "                           AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE()) THEN 1 ELSE 0 END) - "
						+ "                 SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH) "
						+ "                           AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH) THEN 1 ELSE 0 END)) / "
						+ "                SUM(CASE WHEN p.status = 'Y' AND MONTH(p.CREATED_DATE) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH) "
						+ "                           AND YEAR(p.CREATED_DATE) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH) THEN 1 ELSE 0 END)) * 100 "
						+ "       END AS growth_percentage FROM medsure.WATCHRX_PATIENT p "
						+ "WHERE p.GROUP_ID = :orgId AND ((p.status = 'Y' AND p.PATIENT_ID IN (SELECT c.FK_PATIENT_ID "
						+ "                       FROM WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT c "
						+ "                       WHERE c.FK_CLINICIAN_ID = :clinicianId)) OR p.status = 'N')")
						.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId).getResultList();

			}
		} finally {
			em.close();
		}
		return patientsList;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<BigInteger> activePatientsForOrgAndClinician(Long orgId, Long clinicianId, Integer role) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<BigInteger> patientsList = new ArrayList<>();
		try {
			if (role == 3) {
				patientsList = em.createNativeQuery(
						"SELECT p.PATIENT_ID FROM WATCHRX_PATIENT p WHERE " + " p.GROUP_ID =:orgId AND p.STATUS ='Y'")
						.setParameter("orgId", orgId).getResultList();
			} else {
				patientsList = em.createNativeQuery("SELECT p.PATIENT_ID FROM WATCHRX_PATIENT p WHERE p.PATIENT_ID IN "
						+ "( SELECT c.FK_PATIENT_ID FROM WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT c WHERE c.FK_CLINICIAN_ID =:clinicianId ) AND "
						+ " p.GROUP_ID =:orgId AND p.STATUS ='Y'").setParameter("orgId", orgId)
						.setParameter("clinicianId", clinicianId).getResultList();
			}
		} finally {
			em.close();
		}
		return patientsList;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxPatient> getAllPatientsByOrgIdAndClinicianId(Long orgId, Long clinicianId) {
		List<WatchrxPatient> patients = new ArrayList<>();
		try {
			EntityManager em = entityManagerFactory.createEntityManager();

			Query q = em.createQuery(
					"SELECT a FROM WatchrxPatient a WHERE a.watchrxGroup.groupId =:orgId AND a.status ='Y' AND a.patientId IN "
							+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = "
							+ "(SELECT c.clinicianId FROM WatchrxClinician c WHERE c.watchrxUser.userId IN (:userId)))");
			q.setParameter("orgId", orgId);
			q.setParameter("userId", clinicianId);
			return q.getResultList();

		} catch (Exception e) {
			e.printStackTrace();
		}
		return patients;
	}

	@Override
	public Long getAllCountByStatusOrgIdAndClinicianId(String status, Long orgId, Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();
		Long count;
		StringBuilder sb = new StringBuilder();
		try {
			sb.append("SELECT COUNT(a) FROM WatchrxPatient a  WHERE a.status ='" + status + "'");
			if (orgId != 0) {
				sb.append(" and a.watchrxGroup.groupId =" + orgId + "");
			} else if (clinicianId != 0) {
				sb.append(" and a.watchrxUser.userId =" + clinicianId + "");
			}
			count = (Long) em.createQuery(sb.toString()).getSingleResult();
		} finally {
			em.close();
		}
		return count;

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getAllPatientsBillingAdressInfoV3(Long orgId, Integer index, Integer pageSize) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientresList = null;
		try {
			patientresList = em.createNativeQuery(
					"select distinct pa.PATIENT_ID, concat(pa.FIRST_NAME,\" \",pa.LAST_NAME) PatientName, pa.DOB, pa.PRIMARY_CASE_MANAGER_ID, pa.PHONE_NUMBER,"
							+ " concat(c.FIRST_NAME,\" \",c.LAST_NAME) CaregiverName "
							+ "	FROM medsure.WATCHRX_PATIENT pa "
							+ " LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT ca"
							+ " ON (pa.PATIENT_ID = ca.FK_PATIENT_ID) LEFT OUTER JOIN medsure.WATCHRX_CLINICIAN c "
							+ " ON (ca.FK_CLINICIAN_ID = c.CLINICIAN_ID)"
							+ " WHERE pa.GROUP_ID =:orgId AND pa.STATUS = 'Y' ")
					.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
					.getResultList();
		} finally {
			em.close();
		}
		return patientresList;
	}

	@Override
	public Long getAllPatientsCountByOrgIdV3(Long orgId, Long careManagerId, Long roleType) {
		Long patientCount = 0L;
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			Query q = null;
			if (careManagerId != null && careManagerId > 0 && roleType == 5) {
				q = em.createQuery(
						"SELECT COUNT(a) FROM WatchrxPatient a WHERE a.watchrxGroup.groupId =:orgId AND a.status ='Y' AND a.patientId IN "
								+ " (SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = "
								+ "	(SELECT c.clinicianId FROM WatchrxClinician c WHERE c.watchrxUser.userId IN (:userId)))");
				q.setParameter("orgId", orgId);
				q.setParameter("userId", careManagerId);
			} else {
				q = em.createQuery(
						"SELECT COUNT(a) FROM WatchrxPatient a WHERE a.watchrxGroup.groupId =:orgId AND a.status = 'Y'");
				q.setParameter("orgId", orgId);
			}
			return (Long) q.getSingleResult();
		} catch (Exception e) {
			patientCount = 0L;
		}
		return patientCount;
	}

	@Override
	public Long getAllCountByStatusOrgIdAndForPhysician(String status, Long orgId) {
		Long patientCount = 0L;
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			Query q = em.createQuery(
					"SELECT COUNT(a) FROM WatchrxPatient a WHERE a.watchrxGroup.groupId =:orgId AND a.status = :status");
			q.setParameter("orgId", orgId);
			q.setParameter("status", status);
			return (Long) q.getSingleResult();
		} catch (Exception e) {
			patientCount = 0L;
		}
		return patientCount;
	}

	@Override
	public Long getAllCountByStatusOrgIdAndForPhysicianWithFilters(String status, Long orgId, Long clinicianId,
			Long phyId) {
		Long patientCount = 0L;
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			String sql = "SELECT COUNT(DISTINCT pa.PATIENT_ID) FROM medsure.WATCHRX_PATIENT pa "
					+ "LEFT JOIN medsure.WATCHRX_PHYSCIAN py ON pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID "
					+ "WHERE pa.GROUP_ID = :orgId AND pa.status = :status";

			if (phyId != null && phyId != 0) {
				sql += " AND (py.FK_USER_ID = :phyId )";
			}

			if (clinicianId != null && clinicianId != 0) {
				sql += " AND EXISTS (SELECT 1 FROM medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pca "
						+ "JOIN medsure.WATCHRX_CLINICIAN c ON pca.FK_CLINICIAN_ID = c.CLINICIAN_ID "
						+ "WHERE c.FK_USER_ID = :clinicianId AND pca.FK_PATIENT_ID = pa.PATIENT_ID)";
			}

			Query q = em.createNativeQuery(sql).setParameter("orgId", orgId).setParameter("status", status);

			if (phyId != null && phyId != 0) {
				q.setParameter("phyId", phyId);
			}
			if (clinicianId != null && clinicianId != 0) {
				q.setParameter("clinicianId", clinicianId);
			}
			Object result = q.getSingleResult();
			return ((BigInteger) result).longValue();

		} catch (Exception e) {
			patientCount = 0L;
		}
		return patientCount;
	}

	@Override
	public WatchrxPatient findByPatientId(Long patientId) {
		WatchrxPatient patient = null;
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			Query q = em.createQuery("SELECT a FROM WatchrxPatient a WHERE a.patientId = :patientId ");
			q.setParameter("patientId", patientId);
			patient = (WatchrxPatient) q.getSingleResult();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return patient;
	}

	@Override
	public WatchrxPatient findByPhoneNumber(String phoneNumber) {
		WatchrxPatient patient = null;
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			Query q = em.createQuery("SELECT a FROM WatchrxPatient a WHERE a.phoneNumber = :phoneNumber");
			q.setParameter("phoneNumber", phoneNumber);
			q.setMaxResults(1);
			patient = (WatchrxPatient) q.getSingleResult();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return patient;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxPatient> getAllActivePatientsByOrgId(Long orgId) {
		List<WatchrxPatient> patients = new ArrayList<>();
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			Query q = em
					.createQuery(
							"SELECT a FROM WatchrxPatient a WHERE a.status ='Y' AND  a.watchrxGroup.groupId =:orgId")
					.setParameter("orgId", orgId);
			return q.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return patients;
	}

	@Override
	public Long getPatientsCountByPatientId(Long patientId) {
		try {
			EntityManager em = entityManagerFactory.createEntityManager();

			Object count = em.createNativeQuery("select count(*) from ("
					+ "SELECT distinct(fk_patient_id) as id FROM medsure.WATCHRX_VITAL UNION "
					+ "SELECT distinct(fk_patient_id) as id FROM medsure.WATCHRX_PATIENT_ALERT  UNION "
					+ "SELECT distinct(fk_patient_id) as id FROM medsure.WATCHRX_PATIENT_WATCH_ASSIGNMNT) as patient_data "
					+ "where patient_data.id =:patientId").setParameter("patientId", patientId).getSingleResult();
			return ((BigInteger) count).longValue();
		} catch (Exception e) {
			e.printStackTrace();
			return 0L;
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Long> getActivePatientIdsByOrgId(Long orgId) {
		List<Long> patientIds = new ArrayList<Long>();
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			Query q = em.createQuery(
					"SELECT distinct(patientId) FROM WatchrxPatient a WHERE a.status ='Y' AND  a.watchrxGroup.groupId =:orgId")
					.setParameter("orgId", orgId);
			return q.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return patientIds;
	}

	@Override
	public int updateStatusByOrg(List<Long> patientIds) {
		EntityManager em = entityManagerFactory.createEntityManager();
		em.getTransaction().begin();
		Query query = em.createQuery("UPDATE WatchrxPatient e set e.status ='N' where e.patientId in (:patientId)");
		query.setParameter("patientId", patientIds);
		int result = query.executeUpdate();
		em.getTransaction().commit();
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PatientVO> getAllPatientsByOrgIdForPhysician(Long orgId, Integer index, Integer pageSize, String status,
			Long userId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			patientsList = em.createNativeQuery(
					"SELECT DISTINCT pa.PATIENT_ID, pa.FIRST_NAME , pa.LAST_NAME, pa.IMG_PATH, pa.PHONE_NUMBER,pa.MRN,\n"
							+ "concat(py.FIRST_NAME) Physician_Name,\n"
							+ "GROUP_CONCAT(cc.CHRONIC_CONDITION_NAME), g.GROUP_NAME, pa.CONSENT_STATUS FROM medsure.WATCHRX_PATIENT pa\n"
							+ "LEFT OUTER JOIN medsure.WATCHRX_PHYSCIAN py\n"
							+ "ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID)\n"
							+ "LEFT OUTER  JOIN medsure.WATCHRX_PATIENT_CHRONIC_CONDITIONS cc\n"
							+ "ON (pa.PATIENT_ID = cc.FK_PATIENT_ID) JOIN medsure.WATCHRX_GROUP g\n"
							+ "ON (pa.GROUP_ID = g.GROUP_ID)\n"
							+ "WHERE pa.GROUP_ID =:orgId AND pa.status=:status GROUP BY  pa.PATIENT_ID\n")
					.setParameter("orgId", orgId).setFirstResult(index * pageSize).setMaxResults(pageSize)
					.setParameter("status", status).getResultList();
		} finally {
			em.close();
		}
		return joinchronicObjects(patientsList);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PatientVO> getAllPatientsByOrgIdForPhysicianFilters(Long orgId, Integer index, Integer pageSize,
			String status, Long userId, Long phyId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		System.out.println("cmId and phyId in getAllPatientsByOrgIdForPhysicianFilters are " + userId + " " + phyId);
		try {

			String baseQuery = "SELECT DISTINCT pa.PATIENT_ID, pa.FIRST_NAME , pa.LAST_NAME, pa.IMG_PATH, pa.PHONE_NUMBER,pa.MRN,\n"
					+ "concat(py.FIRST_NAME, ' ' , py.LAST_NAME) Physician_Name,\n"
					+ "GROUP_CONCAT(cc.CHRONIC_CONDITION_NAME), g.GROUP_NAME, pa.CONSENT_STATUS FROM medsure.WATCHRX_PATIENT pa\n"
					+ "LEFT OUTER JOIN medsure.WATCHRX_PHYSCIAN py\n" + "ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID)\n"
					+ "LEFT OUTER  JOIN medsure.WATCHRX_PATIENT_CHRONIC_CONDITIONS cc\n"
					+ "ON (pa.PATIENT_ID = cc.FK_PATIENT_ID) JOIN medsure.WATCHRX_GROUP g\n"
					+ "ON (pa.GROUP_ID = g.GROUP_ID)\n" + "WHERE pa.GROUP_ID =:orgId AND pa.status=:status \n";

			if (phyId != null && phyId != 0) {
				baseQuery += " AND py.FK_USER_ID = :phyId";
			}
			if (userId != null && userId != 0) {
				baseQuery += " AND (EXISTS (SELECT 1 FROM medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pca "
						+ " JOIN medsure.WATCHRX_CLINICIAN c ON pca.FK_CLINICIAN_ID = c.CLINICIAN_ID "
						+ " WHERE c.FK_USER_ID = :cmId AND pca.FK_PATIENT_ID = pa.PATIENT_ID))";
			}

			baseQuery += " GROUP BY pa.PATIENT_ID";

			Query query = em.createNativeQuery(baseQuery).setParameter("orgId", orgId).setParameter("status", status)
					.setFirstResult(index * pageSize).setMaxResults(pageSize);
			if (phyId != null && phyId != 0) {
				query.setParameter("phyId", phyId);
			}
			if (userId != null && userId != 0) {
				query.setParameter("cmId", userId);
			}
			patientsList = query.getResultList();
		} finally {
			em.close();
		}
		return joinchronicObjects(patientsList);
	}

	@Override
	public Long patientCountPhysician(Long physicianId, Long orgId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em
				.createQuery("SELECT COUNT(a) FROM WatchrxPatient a WHERE a.watchrxPhysician.physicianId= :physicianId "
						+ "AND a.watchrxGroup.groupId= :grpId AND a.status='Y'");
		q.setParameter("physicianId", physicianId).setParameter("grpId", orgId);
		Long result = (Long) q.getSingleResult();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<EncounterReport> adminEncounterReports(Long orgId, List<Long> userId, Long duration) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> reportList = null;
		try {
			String dateCondition;
			if (duration == 0) {
				dateCondition = "DATE(wpe.CREATED_DATE) BETWEEN DATE_FORMAT(CURDATE(), '%Y-%m-01') AND CURDATE()";
			} else if (duration == 1) {
				dateCondition = "DATE(wpe.CREATED_DATE) BETWEEN "
						+ "DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01') AND "
						+ "LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))";
			} else {
				long safeDuration = Math.min(duration, 6);
				long startMonthsAgo = safeDuration - 1;
				dateCondition = "DATE(wpe.CREATED_DATE) BETWEEN " + "DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL "
						+ startMonthsAgo + " MONTH), '%Y-%m-01') AND " + "LAST_DAY(CURDATE())";
			}

			String queryStr = "SELECT wu.USER_NAME as CASEMANAGER, wg.GROUP_NAME as CLINIC_NAME, "
					+ "SUM(wpe.DURATION) as ENCOUNTER_MINS, "
					+ "MONTHNAME(wpe.CREATED_DATE) as tmonth, YEAR(wpe.CREATED_DATE) as tYear "
					+ "FROM medsure.WATCHRX_PATIENT wp "
					+ "INNER JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT wpca ON wpca.FK_PATIENT_ID = wp.PATIENT_ID "
					+ "INNER JOIN medsure.WATCHRX_CLINICIAN wc ON wc.CLINICIAN_ID = wpca.FK_CLINICIAN_ID "
					+ "INNER JOIN medsure.WATCHRX_USER wu ON wc.FK_USER_ID = wu.USER_ID "
					+ "INNER JOIN medsure.WATCHRX_PATIENT_ENCOUNTERS wpe ON wpe.FK_PATIENT_ID = wp.PATIENT_ID "
					+ "INNER JOIN medsure.WATCHRX_GROUP wg ON wg.GROUP_ID = wp.GROUP_ID " + "WHERE " + dateCondition
					+ " " + "AND wc.FK_USER_ID IN (:clinicianId) AND wg.GROUP_ID = :grpId "
					+ "GROUP BY wu.USER_NAME, wg.GROUP_NAME, MONTHNAME(wpe.CREATED_DATE), YEAR(wpe.CREATED_DATE)";

			reportList = em.createNativeQuery(queryStr).setParameter("grpId", orgId).setParameter("clinicianId", userId)
					.getResultList();

		} finally {
			em.close();
		}
		return encounterReport(reportList);
	}

	private List<EncounterReport> encounterReport(List<Object[]> reportList) {
		List<EncounterReport> reports = new ArrayList<>();
		for (Object[] summaryObjt : reportList) {
			EncounterReport rep = new EncounterReport();
			rep.setCaseManager(String.valueOf(summaryObjt[0]));
			rep.setClinic(String.valueOf(summaryObjt[1]));
			rep.setEncounterMins(summaryObjt[2]);
			rep.setMonth(String.valueOf(summaryObjt[3]));
			rep.setYear(String.valueOf(summaryObjt[4]));
			rep.setYearMonth(String.valueOf(summaryObjt[3]) + "-" + String.valueOf(summaryObjt[4]));
			reports.add(rep);
		}

		return reports;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<VitalReport> adminVitalReports(Long orgId, List<Long> userId, Long duration) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> reportList = null;
		try {
			String dateCondition = "";
			if (duration == 0) {
				// Current month: 1st of this month to now
				dateCondition = "DATE(wprcdd.CREATED_DATE) BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND NOW()";
			} else if (duration == 1) {
				// Only last month
				dateCondition = "DATE(wprcdd.CREATED_DATE) BETWEEN "
						+ "DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y-%m-01') AND "
						+ "LAST_DAY(DATE_SUB(NOW(), INTERVAL 1 MONTH))";
			} else {
				// Last `duration` months including current (e.g., 6 months = from 1st of 5
				// months ago to today)
				long safeDuration = Math.min(duration, 6);
				dateCondition = "DATE(wprcdd.CREATED_DATE) BETWEEN " + "DATE_FORMAT(DATE_SUB(NOW(), INTERVAL "
						+ (safeDuration - 1) + " MONTH), '%Y-%m-01') AND NOW()";
			}

			String query = "SELECT wu.USER_NAME as CASEMANAGER, wg.GROUP_NAME as CLINIC_NAME, "
					+ "COUNT(CASE WHEN wprcdd.DAYS_MEASURED >= 16 THEN wp.PATIENT_ID END) AS NO_OF_PATIENTS_MEASURED, "
					+ " MONTHNAME(wprcdd.CREATED_DATE) AS tmonth, YEAR(wprcdd.CREATED_DATE) AS tYear ,  COUNT( wp.PATIENT_ID) AS TOTAL_NO_OF_PATIENTS"
					+ " FROM medsure.WATCHRX_PATIENT wp "
					+ "JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT wpca ON wpca.FK_PATIENT_ID = wp.PATIENT_ID "
					+ "JOIN medsure.WATCHRX_CLINICIAN wc ON wc.CLINICIAN_ID = wpca.FK_CLINICIAN_ID "
					+ "JOIN medsure.WATCHRX_USER wu ON wc.FK_USER_ID = wu.USER_ID "
					+ "JOIN medsure.WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS wprcdd ON wprcdd.FK_PATIENT_ID = wp.PATIENT_ID "
					+ "JOIN medsure.WATCHRX_GROUP wg ON wg.GROUP_ID = wp.GROUP_ID "
					+ "JOIN medsure.WATCHRX_PATIENT_PROGRAMS wpp ON wpp.FK_PATIENT_ID = wp.PATIENT_ID WHERE "
					+ dateCondition
					+ " AND wpp.FK_PROGRAM_ID = 1 AND wc.FK_USER_ID IN (:clinicianId) AND wg.GROUP_ID = :grpId "
					+ "GROUP BY wu.USER_NAME, wpca.FK_CLINICIAN_ID, wg.GROUP_NAME , tmonth, tYear";

			reportList = em.createNativeQuery(query).setParameter("grpId", orgId).setParameter("clinicianId", userId)
					.getResultList();

		} finally {
			em.close();
		}
		return vitalReport(reportList);
	}

	private List<VitalReport> vitalReport(List<Object[]> reportList) {
		List<VitalReport> reports = new ArrayList<>();
		for (Object[] summaryObjt : reportList) {
			VitalReport rep = new VitalReport();
			rep.setCaseManager(String.valueOf(summaryObjt[0]));
			rep.setClinic(String.valueOf(summaryObjt[1]));
			rep.setVitalReadings(summaryObjt[2]);
			rep.setMonth(String.valueOf(summaryObjt[3]));
			rep.setYear(String.valueOf(summaryObjt[4]));
			rep.setYearMonth(String.valueOf(summaryObjt[3]) + "-" + String.valueOf(summaryObjt[4]));
			rep.setTotalNoPatient(summaryObjt[5]);
			reports.add(rep);
		}

		return reports;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<VitalReport> admin16DayReports(Long orgId, List<Long> userId, Long duration) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> reportList = null;
		try {
			String dateCondition = "";

			if (duration == 0) {
				// Current month: from 1st of this month to now
				dateCondition = "DATE(wprcdd.CREATED_DATE) BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND NOW()";
			} else if (duration == 1) {
				// Only last month
				dateCondition = "DATE(wprcdd.CREATED_DATE) BETWEEN "
						+ "DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y-%m-01') AND "
						+ "LAST_DAY(DATE_SUB(NOW(), INTERVAL 1 MONTH))";
			} else {
				// Last `duration` months including current month
				long safeDuration = Math.min(duration, 6);
				dateCondition = "DATE(wprcdd.CREATED_DATE) BETWEEN " + "DATE_FORMAT(DATE_SUB(NOW(), INTERVAL "
						+ (safeDuration - 1) + " MONTH), '%Y-%m-01') AND NOW()";
			}

			String query = "SELECT wu.USER_NAME as CASEMANAGER, wg.GROUP_NAME as CLINIC_NAME, "
					+ "COUNT(CASE WHEN wprcdd.DAYS_MEASURED >= 16 THEN wp.PATIENT_ID END) AS NO_OF_PATIENTS_MEASURED, "
					+ "COUNT(wp.PATIENT_ID) AS TOTAL_NO_OF_PATIENTS FROM medsure.WATCHRX_PATIENT wp "
					+ "JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT wpca ON wpca.FK_PATIENT_ID = wp.PATIENT_ID "
					+ "JOIN medsure.WATCHRX_CLINICIAN wc ON wc.CLINICIAN_ID = wpca.FK_CLINICIAN_ID "
					+ "JOIN medsure.WATCHRX_USER wu ON wc.FK_USER_ID = wu.USER_ID "
					+ "JOIN medsure.WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS wprcdd ON wprcdd.FK_PATIENT_ID = wp.PATIENT_ID "
					+ "JOIN medsure.WATCHRX_GROUP wg ON wg.GROUP_ID = wp.GROUP_ID "
					+ "JOIN medsure.WATCHRX_PATIENT_PROGRAMS wpp ON wpp.FK_PATIENT_ID = wp.PATIENT_ID WHERE "
					+ dateCondition
					+ " AND wpp.FK_PROGRAM_ID = 1 AND wc.FK_USER_ID IN (:clinicianId) AND wg.GROUP_ID = :grpId "
					+ "GROUP BY wu.USER_NAME, wpca.FK_CLINICIAN_ID, wg.GROUP_NAME";

			reportList = em.createNativeQuery(query).setParameter("grpId", orgId).setParameter("clinicianId", userId)
					.getResultList();

		} finally {
			em.close();
		}
		return dayReport(reportList);
	}

	private List<VitalReport> dayReport(List<Object[]> reportList) {
		List<VitalReport> reports = new ArrayList<>();
		for (Object[] summaryObjt : reportList) {
			VitalReport rep = new VitalReport();
			rep.setCaseManager(String.valueOf(summaryObjt[0]));
			rep.setClinic(String.valueOf(summaryObjt[1]));
			rep.setNoOfPatient(summaryObjt[2]);
			rep.setTotalNoPatient(summaryObjt[3]);
			reports.add(rep);
		}

		return reports;
	}

	@Override
	public List<WatchrxPatient> findByEmailOrPhoneNo(String eamilOrPhone) {
		List<WatchrxPatient> patient = new ArrayList<>();
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Query q = em.createQuery(
					"SELECT a FROM WatchrxPatient a WHERE a.email = :eamilOrPhone OR a.phoneNumber = :eamilOrPhone ");
			q.setParameter("eamilOrPhone", eamilOrPhone);
			patient = q.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			em.close();
		}
		return patient;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PatientVO> getAllPatientsByOrgIdWithStatusN(Long orgId, Integer index, Integer pageSize,
			String status) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			patientsList = em.createNativeQuery(
					"SELECT DISTINCT pa.PATIENT_ID, pa.FIRST_NAME , pa.LAST_NAME, pa.IMG_PATH, pa.PHONE_NUMBER,pa.MRN,"
							+ " concat(py.FIRST_NAME,\" \",py.LAST_NAME) Physician_Name,"
							+ " GROUP_CONCAT(cc.CHRONIC_CONDITION_NAME ), g.GROUP_NAME, pa.CONSENT_STATUS, concat(c.FIRST_NAME,\" \",c.LAST_NAME) CaregiverName FROM medsure.WATCHRX_PATIENT pa"
							+ "	LEFT OUTER JOIN medsure.WATCHRX_PHYSCIAN py"
							+ "	ON (pa.FK_PHYSICIAN_ID = py.PHYSCIAN_ID)"
							+ " LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT ca"
							+ " ON (pa.PATIENT_ID = ca.FK_PATIENT_ID) LEFT OUTER JOIN medsure.WATCHRX_CLINICIAN c "
							+ " ON (ca.FK_CLINICIAN_ID = c.CLINICIAN_ID)"
							+ "	LEFT OUTER JOIN medsure.WATCHRX_PATIENT_CHRONIC_CONDITIONS cc"
							+ "	ON (pa.PATIENT_ID = cc.FK_PATIENT_ID) JOIN medsure.WATCHRX_GROUP g "
							+ " ON (pa.GROUP_ID = g.GROUP_ID)"
							+ " WHERE pa.GROUP_ID =:orgId AND pa.status=:status GROUP BY pa.PATIENT_ID, CaregiverName")
					.setParameter("status", status).setParameter("orgId", orgId).setFirstResult(index * pageSize)
					.setMaxResults(pageSize).getResultList();
		} finally {
			em.close();
		}
		return joinchronicObjects(patientsList);
	}

	@Override
	public Long getPatientCountByBasicInfo(String firstName, String lastName, String dob, String address1) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Long patientCount = 0L;
		try {
			Query q = em.createQuery(
					"select count(1) FROM WatchrxPatient where firstName= :firstName AND lastName= :lastName "
							+ "AND dob= :dob AND watchrxAddress.address1= :address1");
			q.setParameter("firstName", firstName);
			q.setParameter("lastName", lastName);
			q.setParameter("dob", new SimpleDateFormat("yyyy-MM-dd").parse(dob));
			q.setParameter("address1", address1);
			patientCount = (Long) q.getSingleResult();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			em.close();
		}
		return patientCount;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxPatient> getPatientsByOrgIdAndPhysician(Long orgId, Long physicianId, Integer index,
			Integer size) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxPatient> patientresList = new ArrayList<>();
		try {
			Query q = em.createQuery(
					"SELECT a FROM WatchrxPatient a WHERE a.status='Y' AND a.watchrxGroup.groupId = :orgId AND a.watchrxPhysician.physicianId IN "
							+ "(SELECT p.physicianId FROM WatchRxPhysician p WHERE p.watchrxUser.userId IN (:userId))");
			q.setParameter("userId", physicianId);
			q.setParameter("orgId", orgId);
			q.setFirstResult(index * size);
			q.setMaxResults(size);
			patientresList = q.getResultList();
		} finally {
			em.close();
		}
		return patientresList;
	}

	@Override
	public Long getPatientsByOrgIdAndPhysicianCount(Long orgId, Long physicianId) {
		Long count = 0l;
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Query q = em.createQuery(
					"SELECT COUNT(a) FROM WatchrxPatient a WHERE a.status='Y' AND a.watchrxGroup.groupId = :orgId AND a.watchrxPhysician.physicianId IN "
							+ "(SELECT p.physicianId FROM WatchRxPhysician p WHERE p.watchrxUser.userId IN (:userId))");
			q.setParameter("userId", physicianId);
			q.setParameter("orgId", orgId);
			count = (Long) q.getSingleResult();
		} finally {
			em.close();
		}
		return count;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxPatient> getAllPatientsByOrgIdAndClinicianIdV2(Long orgId, Long clinicianId, Integer index,
			Integer size) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxPatient> patients = new ArrayList<>();
		try {
			Query q = em.createQuery(
					"SELECT a FROM WatchrxPatient a WHERE a.watchrxGroup.groupId =:orgId AND a.status ='Y' AND a.patientId IN "
							+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = "
							+ "(SELECT c.clinicianId FROM WatchrxClinician c WHERE c.watchrxUser.userId IN (:userId)))");
			q.setParameter("orgId", orgId);
			q.setParameter("userId", clinicianId);
			q.setFirstResult(index * size);
			q.setMaxResults(size);
			return q.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			em.close();
		}
		return patients;
	}
}