package com.medsure.service;

import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;

import org.springframework.web.multipart.MultipartFile;

import com.medsure.exception.WatchRxServiceException;
import com.medsure.external.ExternalAPIs.AudioRequest;
import com.medsure.model.WatchRxDiyvaDialogs;
import com.medsure.model.WatchRxEncountersNew;
import com.medsure.model.WatchRxPatientDiares;
import com.medsure.model.WatchRxPatientDocumentation;
import com.medsure.model.WatchRxPatientPhoneCommunication;
import com.medsure.model.WatchRxPatientScheduleMessage;
import com.medsure.model.WatchRxPatientTextMessages;
import com.medsure.model.WatchRxTargets;
import com.medsure.model.WatchRxVitals;
import com.medsure.model.WatchrxPatient;
import com.medsure.model.WatchrxPatientPrescription;
import com.medsure.tenovi.model.TenoviReading;
import com.medsure.twilio.clicktocall.VoiceCallModel;
import com.medsure.ui.entity.caregiver.response.PatientGCInfos;
import com.medsure.ui.entity.caregiver.response.PatientInfo;
import com.medsure.ui.entity.caregiver.response.PatientsInfo;
import com.medsure.ui.entity.external.Request.smartmeter.Reading;
import com.medsure.ui.entity.external.Request.telementary.ForwardStatus;
import com.medsure.ui.entity.external.Request.telementary.TelementaryRequest;
import com.medsure.ui.entity.jwt.JwtRequest;
import com.medsure.ui.entity.patient.common.PedometerReadingInfo;
import com.medsure.ui.entity.patient.common.ScheduledTextMessageInfo;
import com.medsure.ui.entity.patient.request.RegisterWatch;
import com.medsure.ui.entity.patient.response.PatientDetails;
import com.medsure.ui.entity.server.AlertResponseVO;
import com.medsure.ui.entity.server.AlertVO;
import com.medsure.ui.entity.server.AlertsCountRequestVO;
import com.medsure.ui.entity.server.AlertsCountVO;
import com.medsure.ui.entity.server.AllChronicConditionsVO;
import com.medsure.ui.entity.server.AssignToPatientVO;
import com.medsure.ui.entity.server.AssignWatchData;
import com.medsure.ui.entity.server.BaseListVO;
import com.medsure.ui.entity.server.BillingDetails;
import com.medsure.ui.entity.server.CollectVitalsFromMedicalDeviceConfigurationVO;
import com.medsure.ui.entity.server.CollectedHeartRateCountListVO;
import com.medsure.ui.entity.server.CollectedHeartRateXlsVO;
import com.medsure.ui.entity.server.CptCodeVO;
import com.medsure.ui.entity.server.CumulativeMinutesListVO;
import com.medsure.ui.entity.server.CustomAlertVO;
import com.medsure.ui.entity.server.CustomAlertsForPatientVO;
import com.medsure.ui.entity.server.DeviceTypeVO;
import com.medsure.ui.entity.server.EncounterNewVO;
import com.medsure.ui.entity.server.HeartRate;
import com.medsure.ui.entity.server.HeartRateInfo;
import com.medsure.ui.entity.server.HeartRateRequestVO;
import com.medsure.ui.entity.server.HeartRateVO;
import com.medsure.ui.entity.server.PatientBillingStatusVO;
import com.medsure.ui.entity.server.PatientDocumentationListVO;
import com.medsure.ui.entity.server.PatientDocumentationVO;
import com.medsure.ui.entity.server.PatientListVO;
import com.medsure.ui.entity.server.PatientMinimal2VO;
import com.medsure.ui.entity.server.PatientPrescriptionVO;
import com.medsure.ui.entity.server.PatientProgramRequestVO;
import com.medsure.ui.entity.server.PatientTextMessage;
import com.medsure.ui.entity.server.PatientTextMessageVO;
import com.medsure.ui.entity.server.PatientVO;
import com.medsure.ui.entity.server.PatientWatchCaregiverInfoVO;
import com.medsure.ui.entity.server.PatientWatchCaregiverListVO;
import com.medsure.ui.entity.server.PatientsAssignedClinicianListVO;
import com.medsure.ui.entity.server.PedometerConfigurationVO;
import com.medsure.ui.entity.server.PedometerReadingVO;
import com.medsure.ui.entity.server.PhoneCommunicationListVO;
import com.medsure.ui.entity.server.PhoneCommunicationVO;
import com.medsure.ui.entity.server.PhoneCommunicationXlsVO;
import com.medsure.ui.entity.server.ScheduleMessageVO;
import com.medsure.ui.entity.server.ScheduleTextMessageVO;
import com.medsure.ui.entity.server.ScreeningConditionListVO;
import com.medsure.ui.entity.server.ScreeningConditionVO;
import com.medsure.ui.entity.server.ScreeningListVO;
import com.medsure.ui.entity.server.ScreeningValuesListVO;
import com.medsure.ui.entity.server.SecCGInviteInfo;
import com.medsure.ui.entity.server.SleepMonitoringConfigurationVO;
import com.medsure.ui.entity.server.SleepMonitoringVO;
import com.medsure.ui.entity.server.StatusVO;
import com.medsure.ui.entity.server.TargetsVO;
import com.medsure.ui.entity.server.TextMessageResponseListVO;
import com.medsure.ui.entity.server.ThresholdConfigVO;
import com.medsure.ui.entity.server.ThresholdConfigurationVO;
import com.medsure.ui.entity.server.UserVO;
import com.medsure.ui.entity.server.ValidationListVO;
import com.medsure.ui.entity.server.ValidationVO;
import com.medsure.ui.entity.server.ViewTextMessage;
import com.medsure.ui.entity.server.ViewTextMessageResponse;
import com.medsure.ui.entity.server.VitalReadingVO;
import com.medsure.ui.entity.server.VitalRequestListVO;
import com.medsure.ui.entity.server.VitalScheduleRequestVO;
import com.medsure.ui.entity.server.VitalScheduleVO;
import com.medsure.ui.entity.server.VitalVO;
import com.medsure.ui.entity.server.VitalWeeklyGraphListVO;
import com.medsure.ui.entity.server.VitalsAndHeartRate;
import com.medsure.ui.entity.server.VitalsCountGraphListVO;
import com.medsure.ui.entity.server.VitalsCountListVO;
import com.medsure.ui.entity.server.congnita.CognitaData;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestBasicInformationVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestChronicConditionsListVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestChronicConditionsVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestDailyScheduleVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestEmergencyContactListVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestEmergencyContactVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestMedicationListVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestMedicationVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestPrefContactTimeVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestVO;
import com.medsure.ui.entity.server.createpatientflow.MedicationImageExistenceVO;
import com.medsure.ui.entity.server.createpatientflow.NotificationConfigurationVO;
import com.medsure.ui.entity.server.createpatientflow.SaveMedicationImageRequestVO;
import com.medsure.ui.entity.server.createpatientflow.SavePatientImageRequestVO;
import com.medsure.ui.entity.server.editpatientflow.CreatePrescriptionRequestVO;
import com.medsure.ui.entity.server.editpatientflow.PrescriptionVO;
import com.medsure.ui.entity.server.medications.MedicationSummaryVO;
import com.medsure.ui.entity.server.patientsaveedit.CreatePatientBasicInformationResponseVO;
import com.medsure.ui.entity.server.patientsaveedit.SavePatientInfoResponseVO;
import com.medsure.util.AdminBillingVo;
import com.ravkoo.request.vitals.VitalLog;

/**
 * <AUTHOR>
 *
 */
public interface PatientService {

	public PatientsInfo getAllPatients();

	public PatientGCInfos getPatientsByClinician(Long nurseId);

	public PatientInfo getPatientById(Long patientId);

	public PatientsInfo getPatientsByDoctor(Long doctorId);

	public PatientDetails getPatientDetailsByImei(RegisterWatch watch);

	public boolean savePatient(PatientVO patientVO, UserVO user);

	public List<PatientVO> getPatientList(UserVO user);

	public List<PatientVO> getPatientList();

	public PatientVO getPatient(Long patientId);

	public void deletePatient(Long patientId);

	public void savePatientAssign(AssignToPatientVO assignToPatientVO);

	public Long savePrescription(PatientPrescriptionVO patientPrescriptionVO) throws WatchRxServiceException;

	public List<PatientPrescriptionVO> getPatientPrescriptionList(Long patientId);

	public PatientPrescriptionVO getPatientPrescription(Long patientPrescriptonId);

	public AssignToPatientVO getPatientAssinment(Long patientId);

	public void deletePrescription(Long prescriptionId);

	public List<PrescriptionVO> getPrescriptionByPatientId(Long patientId);

	public List<PrescriptionVO> getPrescriptionByPrescriptionIds(List<Long> prescriptionIds);

	public void saveGCMRegID(String gcmId, String imeiNo, String platformType);

	public String getGCMRegID(Long patientId);

	public void updateGCMRegID(String gcmId, long patientId);

	public PatientVO getPrescriptionPatient(Long patientId);

	public Boolean isSSNExists(PatientVO patientVO);

	public Long getClinicianByUserId(Long userId);

	public void saveClinicianWatchAssign(AssignToPatientVO assignToPatientVO);

	void savePatientWatchAssign(AssignToPatientVO assignToPatientVO);

	public StatusVO inviteSecondaryCaregiver(SecCGInviteInfo inviteInfo);

	public PatientListVO getParentsByCaregiver(Long nurseId) throws WatchRxServiceException;

	public PatientListVO getParentsByCaregiver(Long nurseId, Integer index, Integer pageSize);

	public PatientListVO getPatientsByNameAndOrgId(Long orgId, Integer userType, String name, Long userId)
			throws WatchRxServiceException;

	public Long getParentsCountByCaregiverId(Long nurseId);

	public StatusVO assignWatchToParent(AssignWatchData assignVO);

	public StatusVO UnassignWatchFromParent(AssignWatchData assignVO);

	public PatientVO savePatientAndReturnPatient(PatientVO patientVO, UserVO user) throws WatchRxServiceException;

	public StatusVO savePatientImage(SavePatientImageRequestVO patientImage);

	public StatusVO saveMedicationImage(SaveMedicationImageRequestVO medicationImage);

	public void saveEmergencyContacts(PatientVO patientVO, CreatePatientRequestEmergencyContactListVO contacts)
			throws WatchRxServiceException;

	public List<MedicationImageExistenceVO> savePrescriptions(PatientVO patientVO,
			CreatePatientRequestMedicationListVO medications) throws WatchRxServiceException;

	public List<CreatePatientRequestEmergencyContactVO> getPatientEmergencyContacts(long patientId);

	public void deleteById(String itemName, long itemId, long patinetId);

	public WatchrxPatientPrescription getPrescription(Long prescriptionId);

	public Long editPrescription(CreatePatientRequestMedicationVO currentMedication);

	public WatchrxPatientPrescription getPrescriptionById(Long prescriptionid);

	public Long createPrescription(CreatePrescriptionRequestVO currentMedication);

	boolean saveExistingEmergencyContacts(CreatePatientRequestEmergencyContactVO contact);

	public String getSecondaryCaregiverStatus(String email, Long patientId);

	public Boolean patientExists(String firstname, String lastname, Long nurseId);

	public void updateWatchAndroidApkInformation(RegisterWatch watch);

	public CreatePatientBasicInformationResponseVO savePatientBasicInfo(CreatePatientRequestBasicInformationVO patient,
			UserVO user) throws ParseException, WatchRxServiceException;

	public CreatePatientBasicInformationResponseVO getPatientBasicInfo(CreatePatientRequestBasicInformationVO patient);

	public SavePatientInfoResponseVO savePatientBasicAndEmergencyInfo(CreatePatientRequestVO patient, UserVO user)
			throws ParseException, WatchRxServiceException;

	public SavePatientInfoResponseVO savePatientBasicAndEmergencyAndDailyScheduleInfo(CreatePatientRequestVO patient,
			UserVO user) throws ParseException, WatchRxServiceException;

	public SavePatientInfoResponseVO savePatientBasicAndEmergencyAndDailyScheduleInfoInsurance(
			CreatePatientRequestVO patient, UserVO user) throws ParseException, WatchRxServiceException;

	public CreatePatientRequestDailyScheduleVO getDailyScdByPatientId(Long patientId);

	public CreatePatientRequestMedicationVO changePrescriptionFormat(PatientPrescriptionVO prescription);

	public boolean saveEditPatientBasicInfo(PatientVO patientVO, UserVO user);

	public void saveHeartRate(HeartRate heartRate);

	public PatientsInfo getPatientsByCaregiver(Long caregiverId);

	void saveHeartRates(List<HeartRateVO> heartRates);

	public boolean updatePatientVerifyVisitCode(Long patientId, Long visitVerifyCode);

	public boolean visitVerificationCodeFound(Long patientId, Long visitVerifyCode);

	public StatusVO saveCustomAlertInfo(CustomAlertsForPatientVO customAlertsForPatientVO);

	public List<CustomAlertsForPatientVO> getCustomAlertInfo(long patientId);

	boolean saveExistingCustomAlert(CustomAlertsForPatientVO customalert);

	public void saveCustomAlerts(PatientVO patientVO, CustomAlertVO alerts);

	public StatusVO saveScheduleMessage(PatientVO patientVO, ScheduleMessageVO scheduleMessageVO);

	public List<WatchRxPatientScheduleMessage> getScheduleMessages();

	public void deleteScheduleMessage(Long id);

	public StatusVO savePatientHeartInfoFromServer(PatientVO patientVO, HeartRateRequestVO alerts);

	public StatusVO getHeartRateSettingInfoByPatientId(Long patientId);

	public StatusVO savePatientQuestions(PatientTextMessageVO patientTextMessageVO);

	public void updateTexMessage(Long messageId, String messageStatus);

	public List<WatchRxPatientTextMessages> getNotSentMessages();

	public void updateTexMessage(PatientTextMessage message);

	public SavePatientInfoResponseVO savePatientBasicAndEmergencyAndDailyScheduleInfoInsuranceAndChronic(
			CreatePatientRequestVO patient, UserVO user) throws ParseException, WatchRxServiceException;

	public CreatePatientRequestChronicConditionsListVO getPatientChronicConditionsByPatientId(Long patientiId);

	public void saveChronicConditions(PatientVO patientVO,
			CreatePatientRequestChronicConditionsListVO chronicConditions) throws WatchRxServiceException;

	public boolean updateChronicConditions(CreatePatientRequestChronicConditionsVO chronicCondition);

	public CreatePatientRequestChronicConditionsListVO getChronicConditions(PatientVO patientVO);

	public boolean saveChronicCondition(CreatePatientRequestChronicConditionsListVO chronicCondition);

	public WatchRxEncountersNew saveEncountersNew(EncounterNewVO encounterNewVO) throws WatchRxServiceException;;

	public StatusVO deleteEncounterNew(EncounterNewVO encounterNewVO);

	public void saveEncounterDraft(EncounterNewVO encounterNewVO) throws WatchRxServiceException;

	public void deleteEncounterDraft(Long draftId) throws WatchRxServiceException;

	public void submitDraftAsEncounter(EncounterNewVO encounterNewVO) throws WatchRxServiceException;

	public List<TargetsVO> getTargets(PatientVO patientVO);

	public WatchRxTargets saveTargets(TargetsVO targetsVO);

	public StatusVO deleteTarget(TargetsVO targetsVO);

	// Modified below parameter for deleteVitals
	public StatusVO deleteVital(VitalReadingVO vitalReadingVO);

	public List<VitalVO> getVitals(PatientVO patientVO, Integer index, Integer pageSize);

	public WatchRxVitals saveVital(VitalVO vitalVO);

	public WatchRxPatientPhoneCommunication savePhoneCommunication(PhoneCommunicationVO phoneCommunicationVO);

	public PhoneCommunicationListVO getPhoneCommunication(PatientVO patientVO, Integer index, Integer pageSize);

	public Long getPhoneCommunicationCount(PatientVO patientVO);

	public StatusVO deletePhoneCommunication(PhoneCommunicationVO phoneCommunicationVO);

	public PatientListVO getAllPatientsForClinicianId(Long clinicianId, Long roleType, Integer index, Integer pageSize);

	public Long getPatientsCountByPhysicianId(Long clinicianId);

	public List<EncounterNewVO> getEncounterNew(PatientVO patientVO, Integer index, Integer pageSize);

	public List<ViewTextMessage> getPatientTextMessageResponse(Long patientId, Integer index, Integer pageSize,
			ViewTextMessageResponse viewTextMessageResponse);

	public Integer getEncounterNewResultCount(PatientVO patientVO, String startDate, String endDate);

	public Long getPatientTextMessageResponseResultCount(Long patientId);

	public Long getHeartRateResultCount(Long patientId);

	public HeartRateInfo getHeartRate(Long patientId, Integer index, Integer pageSize);

	public AlertsCountVO getAlertsCount(AlertsCountRequestVO requestBody);

	public List<AlertVO> getAlertsExcel(AlertsCountRequestVO requestBody);

	public CumulativeMinutesListVO getCumulativeMinutesCount(AlertsCountRequestVO requestBody);

	public List<PhoneCommunicationXlsVO> getCumulativeMinutesExcel(AlertsCountRequestVO requestBody);

	public CollectedHeartRateCountListVO getCollectedHeartRateCount(AlertsCountRequestVO requestBody);

	public List<CollectedHeartRateXlsVO> getCollectedHeartRateExcel(AlertsCountRequestVO requestBody);

	public VitalsCountListVO getVitalsCount(AlertsCountRequestVO requestBody);

	public Long getVitalResultCount(PatientVO patientVO);

	public PatientsAssignedClinicianListVO getPatientsPhysicianCaseManager(Long userId, Long userType, Long clinicianId,
			Integer index, Integer pageSize);

	public boolean saveInsuranceDetails(PatientVO patientVO) throws WatchRxServiceException;

	public boolean saveCptDetails(PatientVO patientVO) throws WatchRxServiceException;

	public void deleteCptDetails(Long cptId);

	public void deleteInsuranceDetails(Long insuranceId);

	public Map<String, Integer> getValidationPendingCount(Long clinicianId, Integer userType)
			throws WatchRxServiceException;

	public Long getValidationSubmittedCount(Long clinicianId, Integer userType) throws WatchRxServiceException;

	public ValidationVO getValidationForPatient(Long patientId, String date);

	public boolean updateValidationForPatient(ValidationVO validationVO);

	public Long patientCountMonth(Long orgId) throws WatchRxServiceException;

	public Long patientCount(Long userId, Integer userType) throws WatchRxServiceException;

	public TextMessageResponseListVO getLatestTextMessagesAllPatients(Long userId, Integer userType);

	public ThresholdConfigVO getThresholdConfig(Long patientId);

	public void generateThresholdConfig(Long patientId, Long value, String type, String createdDate, String BP);

	public boolean saveThresholdConfig(ThresholdConfigVO thresholdConfigVO);

	public boolean saveScreeningInfo(ScreeningListVO screeningListVO) throws WatchRxServiceException;

	public ScreeningListVO getScreeningInfo(Long patientId) throws WatchRxServiceException;

	public void runScreening();

	public boolean saveScreeningValues(ScreeningValuesListVO screeningValuesListVO);

	public boolean saveScreeningCondition(ScreeningConditionVO screeningConditionVO) throws WatchRxServiceException;

	public ScreeningConditionListVO getAllScreeningConditions() throws WatchRxServiceException;

	public List<PatientMinimal2VO> getAllPatientsMinimal(Integer index, Integer pageSize);

	public Long getAllPatientsCount();

	// public boolean verifyValidation(ValidationVO validationVO);

	public void resetStatusMonthBegining();

	public PatientBillingStatusVO patientBillingStatus(Long patientId);

	public Long getRespondedTextMessageCount(Long userId, Long userType);

	public Long getCriticalAlertCount(Long userId, Long userType);

//	public void addAlertToQueue(MedicationAlert info);

	public boolean saveScheduledTextMessage(ScheduleTextMessageVO scheduleTextMessageVO);

	public void updateScheduledTexMessage(PatientTextMessage message);

	public WatchRxPatientDocumentation savePatientDocumentation(PatientDocumentationVO patientDocumentationVO);

	public PatientDocumentationListVO getPatientDocumentation(PatientVO patientVO, Integer index, Integer pageSize);

	public StatusVO deletePatientDocumentation(PatientDocumentationVO patientDocumentationVO);

	public Long getPatientDocumentationCount(PatientVO patientVO);

	public boolean deleteScheduledTextMessage(ScheduleTextMessageVO scheduleTextMessageVO);

	public boolean updatePhysician(PatientWatchCaregiverInfoVO patientWatchCaregiverInfoVO);

	public Long getPatientsCount();

	public boolean savePedometerConfiguration(PedometerConfigurationVO pedometerConfigurationVO);

	public PedometerConfigurationVO getPedometerConfiguration(Long patientId);

	public boolean addPedometerReading(PedometerReadingInfo pedometerReadingInfo);

	public boolean enableDisablePedometer(PedometerConfigurationVO pedometerConfigurationVO);

	public boolean getPedometerReadingCountNow(Long patientId);

	public List<PedometerReadingVO> getPedometerReadingList(Long patientId, Integer index, Integer pageSize);

	public Long getPedometerReadingCount(Long patientId);

	public boolean resetPedometer(Long patientId);

	public List<PedometerReadingVO> getPedometerReports(AlertsCountRequestVO alertsCountRequestVO);

	public List<PedometerReadingVO> getPedometerReportsNew(AlertsCountRequestVO alertsCountRequestVO);

	public boolean generateAllPatientCummulativeData();

	public StatusVO savePatientQuestionsMultiple(PatientTextMessageVO patientTextMessageVO);

//	public List<PedometerReadingVO> getPedometerReportsNew2(AlertsCountRequestVO alertsCountRequestVO);

	public PatientListVO getPatientsByPhysicianByName(Long physicianId, String name);

	public Long getTextMessageOnlyReceivedByPatientIdCount(Long patientId);

	public List<ScheduledTextMessageInfo> getScheduledTextMessageWatch(Long patientId);

	public List<ScheduleTextMessageVO> getScheduledTextMessage(Long patientId, Integer index, Integer pageSize);

	public Long getScheduledTextMessageCount(Long patientId);

	public List<CptCodeVO> getAllCptCodes(boolean isCCM);

	public boolean setCptCodes(List<CptCodeVO> cptCodeVOList);

	public List<AllChronicConditionsVO> getAllChronicConditions(String type);

	public boolean setChronicConditionsChronicConditions(List<AllChronicConditionsVO> allChronicConditionsVOList);

	public List<ScheduledTextMessageInfo> getScheduledTextMessageWatchByScheduledTextMessageId(Long patientId,
			Long scheduledTextMessageId, String status);

	public void addMessageNotCollectedByWatch();

	public boolean saveVitalsAndHeartRate(VitalsAndHeartRate vitalsAndHeartRate);

	public CollectVitalsFromMedicalDeviceConfigurationVO getVitalsFromMedicalDeviceConfiguration(Long patientId);

//	public StatusVO saveVitalsFromMedicalDeviceConfiguration(CollectVitalsFromMedicalDeviceConfigurationRequestVO configVO);

	public StatusVO saveVitalsFromMedicalDeviceConfiguration(
			CollectVitalsFromMedicalDeviceConfigurationVO vitalConfigurationVO);

	public List<AlertResponseVO> activeCriticalAlertsCurrentMonth(Long clinicianId, Long orgId, Integer index,
			Integer pageSize);

	public Long activeCriticalAlertsCurrentMonthCount(Long clinicianId, Long orgId);

	public boolean updateAlertIsAcknowledged(AlertResponseVO alertResponseVO);

	public Long numberOfPatientsWithActiveCriticalAlertsCount(Long orgId);

	public NotificationConfigurationVO updateNotificationConfiguration(NotificationConfigurationVO configurationVO);

	// Added by KV for Pagination
	// public List<WatchrxPatientSummary> getPatientsForUserId(Long userId, Long
	// roleType);
	public List<PatientVO> getPatientsForUserId(Long userId, Long roleType, Integer index, Integer pageSize)
			throws WatchRxServiceException;

	public PatientVO getPatientAlerts(Long patientId);

	public List<Object[]> getPatientBillingStatus(Long userId, Long roleType, Integer index, Integer pageSize,
			String status);

	public List<VitalScheduleVO> getVitalShedules(VitalScheduleRequestVO vitalScheduleRequestVO);

	public StatusVO saveVitalShedules(VitalScheduleVO vitalScheduleVO);

	public StatusVO saveVitalReadings(VitalReadingVO vitalReadingVO);

	public List<VitalReadingVO> getVitalReadings(VitalScheduleRequestVO vitalScheduleRequestVO);

	public Boolean deleteVitalShedules(VitalScheduleVO vitalScheduleVO);

	public ThresholdConfigurationVO enableVitalCollection(VitalScheduleRequestVO vitalScheduleRequestVO);

	public BaseListVO<ThresholdConfigurationVO> getThresholdConfiguration(
			BaseListVO<VitalScheduleRequestVO> vitalScheduleRequestListVO);

	public StatusVO saveThresholdConfiguration(BaseListVO<ThresholdConfigurationVO> thresholdConfigurationListVO);

	public ThresholdConfigurationVO getEnableStatus(VitalScheduleRequestVO vitalScheduleRequestVO);

	public VitalsCountGraphListVO vitalGraph(VitalRequestListVO vitalRequestListVO);

	public StatusVO saveVitalReadingsFromWatch(VitalReadingVO vitalReadingVO);

	public VitalsCountGraphListVO vitalGraphNew(VitalRequestListVO vitalRequestListVO);

	public List<DeviceTypeVO> getDeviceTypeList();

	public StatusVO saveDeviceType(DeviceTypeVO deviceTypeVO);

	public StatusVO deleteDeviceType(DeviceTypeVO deviceTypeVO);

	public PatientListVO getParentsByClinician(Long orgId, Long userId);

	// public VitalWeeklyGraphListVO getWeeklyVitalGraphDetails(VitalRequestListVO
	// vitalRequestListVO);

	public Long getPhysicianByUserId(Long userId);

	public VitalsCountGraphListVO getDailyPedometerDetails(VitalRequestListVO vitalRequestListVO);

	// public VitalsCountGraphListVO getWeeklyPedometerDetails(VitalRequestListVO
	// vitalRequestListVO);

	public VitalReadingVO saveScheduleForErrorVitals(VitalReadingVO vitalReadingVO);

	public VitalReadingVO deleteScheduleForErrorVitals(VitalReadingVO vitalReadingVO);

	// public VitalWeeklyGraphListVO getMonthlyVitalGraphDetails(VitalRequestListVO
	// vitalRequestListVO);

	public VitalWeeklyGraphListVO getAlertsGraph(AlertsCountRequestVO requestBody);

	// public VitalsCountGraphListVO getMonthlyPedometerDetails(VitalRequestListVO
	// vitalRequestListVO);

	public VitalWeeklyGraphListVO getVitalGraphDetails(VitalRequestListVO vitalRequestListVO);

	public VitalsCountGraphListVO getPedometerDetails(VitalRequestListVO vitalRequestListVO);

	public boolean verifyValidation(ValidationListVO validationVO);

	List<EncounterNewVO> getEncounterByReasonAndPatient(Long patientId, String reason, String startDate,
			String endDate);

	Map<String, Integer> vitalStatus(VitalRequestListVO vitalRequestListVO);

	void generateTask();

	public Map<String, Object> validationInfo(Long patientId, String date);

	public List<Map<String, String>> getLatestVitalDataByPatientId(Long patientId);

	List<Map<String, String>> getVitalDataByPatientIdAndDate(Map<String, String> request);

	List<Map<String, String>> getTextMessageByPatientId(Long patientId);

	public PatientDetails getPatientDetailsByEmail(RegisterWatch watch);

	Integer saveResentOTPByPatnId(Long patientId);

	boolean isPatientMobileAssociated(RegisterWatch watch);

	boolean authenticatePatient(String email, String otp);

	List<WatchrxPatient> getPatient(String email, String otp);

	Integer getOTPByEmailId(String email);

	Integer getOTPByPatientId(Long patientId);

	void saveTokenToPatient(String email, String token);

	String getTokenByPatientId(Long patientId);

	public boolean isPatientEmailExist(String email);

	public void resetPassword(String password, String email);

	boolean authorizePatient(String email, String password);

	List<SleepMonitoringVO> getSleepMonitoringReadings(Long patientId, Integer index, Integer pageSize);

	Long getSleepMonitoringReadingCount(Long patientId);

	void savePatientOTPValidatedStatus(String email, String status);

	boolean resentOTPByEmailId(String emailId);

	boolean patientOTPValidation(String email, String otpStatus);

	boolean saveSleepMonitoringConfiguration(SleepMonitoringConfigurationVO sleepMonitoringConfigurationVO);

	SleepMonitoringConfigurationVO getSleepMonitoringConfigurationConfiguration(Long patientId);

	List<SleepMonitoringVO> getSleepMonitoringReports(AlertsCountRequestVO alertsCountRequestVO);

	void saveSleepMonitoringReadings(SleepMonitoringVO sleepMonitoringVO);

	void saveSleepDataByPatient(SleepMonitoringVO sleepReadingVO);

	void sendIOSSilentNotificationTask();

	VitalsCountGraphListVO getSleepMonitorDetails(VitalRequestListVO vitalRequestListVO);

	VitalsCountGraphListVO getDailySleepMonitorDetails(VitalRequestListVO vitalRequestListVO);

	JwtRequest getWatchPatientDetailsByImei(RegisterWatch watch);

	void getTodaysAllVitalData(Long patientId, String UserId, List<VitalLog> logs);

	Long getPatientIdByEmailId(String email);

	List<BillingDetails> physicianPatientBilling(UserVO user);

	List<WatchRxPatientDiares> patientDiaries(Date startDate, Date endDate, int index, int pageSize, long patientId);

	void insertPatientDiaries(WatchRxPatientDiares diary);

	public List<WatchRxPatientDiares> fullPatientDiaries(int index, int pageSize, long patientId);

	String saveWirelessPatientVitalDetailsByImei(TelementaryRequest wdRequest);

	String saveWirelessPatientTimeZoneByImei(ForwardStatus wdRequest);

	void generateBillingPerMonthTask(String date, UserVO user);

	String saveWirelessPatientVitalDetailForSmartMeter(Reading reading);

	public void savePatientsByBulk(List<PatientVO> patientList, Long orgId);

	List<Long> getPatientIds(String[] mrns);

	Long getPatientIdByMRN(String mrn);

	public StatusVO deleteAlert(Long alertId);

	public void notifiyCaseManagerForPatientIncomplete();

	public WatchRxPatientTextMessages saveTextMessage(WatchRxPatientTextMessages msg);

	List<ViewTextMessage> getPatientTextMessageResponseForDate(Long patientId, String fromDate, String toDate);

	void sendDialogsToPatient(WatchRxDiyvaDialogs watchRxDiyvaDialogs, String patId);

	public List<String> getCPTCodeByPatientId(Long patientId);

	public UserVO doPatientLogin(String userName, String password);

	public VitalWeeklyGraphListVO getAlertsCountGraph(AlertsCountRequestVO requestBody);

	public Map<String, Object> validationInfoByTimeDuration(Long patientId, String timeDuration);

	public Map<String, Integer> getVitalsMeasuredDayByPatientId(Long patientId, String timeDuration);

	public void autoValidationForPatient(Long patientId, int rpmMins, int ccmMins, int daysCount, Long userId,
			String date);

	public Map<String, Integer> getValidationPendingCount(List<Long> patientIds) throws WatchRxServiceException;

	public List<AlertResponseVO> activeCriticalAlertsCurrentMonthForPhysician(Set<Long> physicianIds, Integer index,
			Integer pageSize);

	public Map<String, Integer> validationInfoByStartAndEndDate(Long patientId, Date startDate, Date endDate);

	public List<Object[]> getAllPatientsByOrgId(Long orgId, Integer index, Integer pageSizeF);

	public Long getAllPatientsByOrgIdCount(Long orgId, Long userId);

	public Long getAllPatientsCountByOrgId(Integer userType, Long orgId, Long userId);

	public List<Object[]> getPatientBillingAddressInfo(Long orgId, Integer index, Integer pageSize, Long userId);

	public List<PatientVO> getAllPatientsByOrgId(Long orgId, Integer roleType, Integer index, Integer pageSize,
			Long userId) throws WatchRxServiceException;

	boolean getPatientLoginExpByEmailId(String email);

	public PatientGCInfos getAllPatientsByOrgId(Long orgId, Long userId);

	public Long getPatientsCountByOrgIdBySearch(Long orgId, String search, Long clinicianId);

	public List<Object[]> getPatientBillingAddressInfoBySearch(Long orgId, Integer index, Integer pageSize,
			String search);

	Map<String, Integer> getScreeningPatientDues(List<Long> patientIds) throws WatchRxServiceException;

	Map<String, Map<String, Integer>> getScreeningPatientDuesForGraph(List<Long> patientIds)
			throws WatchRxServiceException;

	Map<String, Object> getScreeningPatientBMCDetails(List<Long> patientIds) throws WatchRxServiceException;

	Map<String, Object> getScreeningPatientBMCDetails(List<Long> ids, Map<String, Integer> bmcSearch);

	Map<String, Object> screeningPatientDuesByConditionV1(List<Long> ids, Map<String, Object> bmcSearch);

	public StatusVO patientSaveStatus(Long patientId, @NotBlank String status);

	String saveTenoviPatientData(List<TenoviReading> readings);

	Long getAllPatientsCountByOrgIdAndStatus(Integer userType, Long orgId, String status, Long clinicianId,
			boolean filterby);

	Long getAllPatientsCountByOrgIdAndStatusAndFilters(Integer userType, Long orgId, String status, Long clinicianId,
			Long phyId, boolean filterby);

	List<PatientVO> getAllPatientsByOrgIdAndStatus(Long orgId, Integer roleType, Integer index, Integer pageSize,
			String status, Long userId, Long phyId, boolean filterby) throws WatchRxServiceException;

	PatientListVO getPatientsByNameAndOrgIdAndStatus(Long orgId, Integer userType, String name, String status,
			Long userId) throws WatchRxServiceException;

	public Map<String, Object> dashboardAlertsCount(Long orgId, Long clinicianId, Integer roleType);

	public Map<String, Object> dashboardPatientCount(Long orgId, Long clinicianId, Integer role, String startDate,
			String endDate);

	public Map<String, Object> dashboardPatientGraph(Long orgId, Long clinicianId, Integer role, String startDate,
			String endDate);

	public void createEmptyMasterDataForRPMCCM();

	public PatientWatchCaregiverListVO getPatientBillingAddressInfoV2(Integer index, Integer pageSize, Long orgId,
			Long userId, Integer roleType, String filterType, String startDate, String endDate, String sortCol,
			String sortDir);

	public PatientWatchCaregiverListVO getPatientBillingAddressInfoV2Search(Long orgId, Long userId, Integer roleType,
			String searchValue, String startDate, String endDate);

	public PatientDocumentationListVO getPatientRpmCcmPcmMins(Long patientId);

	public List<Object[]> getPatientBillingAddressInfoV3(Long orgId, Integer index, Integer pageSize);

	public Long getAllPatientsCountByOrgIdV3(Integer userType, Long orgId, Long userId);

	public List<AlertResponseVO> activeCriticalAlertsCurrentMonthV1(Long orgId, Integer index, Integer pageSize);

	public Long activeCriticalAlertsCurrentMonthCountV1(Long clinicianId);

	public Map<String, Object> patientsStatsCountByClinic(Long orgId, String startDate, String endDate,
			Long careManagerId);

	public Map<String, Object> patientsStatsByClinic(Long orgId, String startDate, String endDate, Integer index,
			Integer pageSize, Long careManagerId, String filter);

	public Map<String, Object> patientsStatsByClinicReport(Long orgId, String startDate, String endDate,
			Long careManagerId, Long userType);

	public AdminBillingVo generateBillingReportByClinic(Long orgId, String date, Long userId);

	public List<BillingDetails> adminBillingReports(Long orgId);

	public List<EncounterNewVO> getEncounterNewV2(PatientVO patientVO, Integer index, Integer pageSize,
			String startDate, String endDate);

	public HttpServletResponse downloadBs(HttpServletResponse httpServletResponse, Long orgId, String startDate,
			String endDate, Long careManagerId, Long roleType);

	PatientListVO getPatientsByName(String name) throws WatchRxServiceException;

	public Map<String, Object> patientDashboardVitalGraph(Long patientId, String currentDate);

	public Map<String, Object> updatePatientPrograms(PatientProgramRequestVO req);

	public Map<String, Object> updatePatientConsent(Long patientId, Boolean consent);

	public List<String> getPatientProgramsList(Long patientId);

	public List<Map<String, Object>> getPatientProgramsListByPId(Long patientId);

	public Map<String, Object> getChronicConditionByPatientId(Long patientId);

	public PatientWatchCaregiverListVO latestCalledPatients(Integer index, Integer pageSize, Long orgId, Long userId,
			Integer roleType, String stDate, String edDate);

	public Map<String, Object> getOverAllMins(Long orgId, String startDate, String endDate);

	public Map<String, Object> getInhalerreadings(Long patientId, Integer index, Integer pageSize);

	public String processCognitaWirelessDevieData(CognitaData readings);

	public void saveInhalerData(CognitaData cognitaData, WatchrxPatient patient);

	public Map<String, Object> getSpirometryReadings(Long patientId, Integer index, Integer pageSize);

	public void saveSpirometryData(CognitaData cognitaData, WatchrxPatient patient);

	public Map<String, Object> saveConsentData(Map<String, String> data);

	public Map<String, Object> getConsentData(Long patientId);

	public HttpServletResponse downloadConsent(HttpServletResponse httpServletResponse, Long patientId);

	public Map<String, Object> consentContact(Long orgId, String email);

	public Map<String, Object> getLastestVitalsByPatientId(Long patientId);

	public PatientGCInfos getAllPatientsByOrgIdAndUserId(Long orgId, Long userId, Long roleType, Integer pageNumber,
			Integer pageSize);

	public Long getPatientsCountByPatientId(Long patientId);

	public void addCummalativeMins(Long patientId, String review, double mins);

	public Map<String, Object> getLatestMinsByPatientId(Long patientId, String startDate, String endDate);

	public StatusVO saveMedicationSummary(MedicationSummaryVO medicationSummaryVO, Long userId);

	public List<MedicationSummaryVO> getMedicationSummaryByPatientId(Long patientId);

	public StatusVO deleteMedicationSummary(Long medId, Long userId);

	public Map<String, String> getOTPStatusByEmailId(String email);

	PatientDetails getPatientLoginDetailsByImei(RegisterWatch watch);

	public List<Map<String, Object>> getPatientPrograms(Long patientId);

	public Map<String, Object> getPatientProgramsAndChatMins(Long patientId);

	public void saveContactTimings(PatientVO patientData, List<CreatePatientRequestPrefContactTimeVO> prefConactDetails)
			throws WatchRxServiceException;

	StatusVO uploadAudio(MultipartFile file, Long patientId, UserVO user, Double durations) throws IOException;

	StatusVO uploadMedicationPrescription(MultipartFile file, Long patientId, UserVO user) throws IOException;

	public List<EncounterNewVO> getEncounterForCaregiverApp(PatientVO patientVO, Integer index, Integer pageSize,
			String startDate, String endDate, String reviewType);

	public StatusVO reviewMedicationPrescsription(Long patientId);

	public StatusVO setPrescriptionReminder(Long prescriptionId, Boolean isReminder);

	List<String> getAudioForProcessing() throws IOException;

	public void setAudioForProcessedData(AudioRequest requestData);

	public Long isPatientExist(String firstName, String lastName, String dob, String address);

	public Map<String, String> getAllEncounterReasonsAndCodes();

	public Map<String, String> getAllDisplayableEncounterReasons();

	boolean isPlatformIOS(Long patientId);

	public VoiceCallModel getVoipTokenByPatientId(Long patientId);

	StatusVO uploadAudio(MultipartFile file, Long patientId, Long orgId) throws IOException;

	StatusVO uploadAudioForSummary(MultipartFile file, Long patientId, UserVO userVo, Double duration)
			throws IOException;
}
