package com.medsure.ui.service.caregiver;

import java.io.IOException;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.inject.Named;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.google.api.server.spi.config.Api;
import com.google.api.server.spi.config.ApiMethod;
import com.google.appengine.api.oauth.OAuthRequestException;
import com.google.appengine.api.users.User;
import com.google.appengine.api.users.UserService;
import com.google.appengine.api.users.UserServiceFactory;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.medsure.common.Constants;
import com.medsure.dao.ClinicianDAO;
import com.medsure.dao.PatientDAO;
import com.medsure.exception.WatchRxException;
import com.medsure.exception.WatchRxExceptionCodes;
import com.medsure.exception.WatchRxServiceException;
import com.medsure.factory.WatchRxFactory;
import com.medsure.model.WatchRxPatientTextMessages;
import com.medsure.model.WatchrxPatient;
import com.medsure.service.PatientService;
import com.medsure.service.WatchService;
import com.medsure.service.impl.UserServiceImpl;
import com.medsure.test.caregiver.CareGiverTest;
import com.medsure.twilio.clicktocall.VoiceCallModel;
import com.medsure.ui.entity.access.Group;
import com.medsure.ui.entity.caregiver.common.CareGiverDetails;
import com.medsure.ui.entity.caregiver.request.AlertById;
import com.medsure.ui.entity.caregiver.request.AlertByPatient;
import com.medsure.ui.entity.caregiver.request.AlertRemainder;
import com.medsure.ui.entity.caregiver.request.AlertStatus;
import com.medsure.ui.entity.caregiver.request.CareGiverStatus;
import com.medsure.ui.entity.caregiver.request.CaregiverRequestForHeartRate;
import com.medsure.ui.entity.caregiver.request.GetAlerts;
import com.medsure.ui.entity.caregiver.request.Login;
import com.medsure.ui.entity.caregiver.request.MedicationIds;
import com.medsure.ui.entity.caregiver.request.ScheduleAlert;
import com.medsure.ui.entity.caregiver.request.VerifyVisit;
import com.medsure.ui.entity.caregiver.request.WatchReachabilityRequest;
import com.medsure.ui.entity.caregiver.response.ClinicianDetails;
import com.medsure.ui.entity.caregiver.response.GeneralInfo;
import com.medsure.ui.entity.caregiver.response.GetAlertStatus;
import com.medsure.ui.entity.caregiver.response.GetOnlyPatientNotes;
import com.medsure.ui.entity.caregiver.response.MedicationInfoList;
import com.medsure.ui.entity.caregiver.response.PatientAlerts;
import com.medsure.ui.entity.caregiver.response.PatientGCInfos;
import com.medsure.ui.entity.caregiver.response.PatientGPSAlerts;
import com.medsure.ui.entity.caregiver.response.PatientInfo;
import com.medsure.ui.entity.caregiver.response.WatchReachabilityResponse;
import com.medsure.ui.entity.patient.common.MedicationInfo;
import com.medsure.ui.entity.patient.common.ScheduledTextMessageInfo;
import com.medsure.ui.entity.patient.request.ChatNotification;
import com.medsure.ui.entity.patient.request.ChatRequest;
import com.medsure.ui.entity.patient.request.ChatResponse;
import com.medsure.ui.entity.patient.request.GeneralRequest;
import com.medsure.ui.entity.patient.request.MedicationAlert;
import com.medsure.ui.entity.patient.request.RegisterWatch;
import com.medsure.ui.entity.patient.request.WatchCGAppStatus;
import com.medsure.ui.entity.patient.response.PatientDetails;
import com.medsure.ui.entity.server.AlertRequestVO;
import com.medsure.ui.entity.server.BaseListVO;
import com.medsure.ui.entity.server.ClinicianVO;
import com.medsure.ui.entity.server.CustomAlertVO;
import com.medsure.ui.entity.server.CustomAlertsForPatientVO;
import com.medsure.ui.entity.server.EncounterNewVO;
import com.medsure.ui.entity.server.PatientCarePlanVO;
import com.medsure.ui.entity.server.PatientMinimalListVO;
import com.medsure.ui.entity.server.PatientPrescriptionVO;
import com.medsure.ui.entity.server.PatientVO;
import com.medsure.ui.entity.server.PatientWatchCaregiverInfoVO;
import com.medsure.ui.entity.server.PatientWatchCaregiverListVO;
import com.medsure.ui.entity.server.StatusVO;
import com.medsure.ui.entity.server.TaskListVO;
import com.medsure.ui.entity.server.TasksVO;
import com.medsure.ui.entity.server.ValidationVO;
import com.medsure.ui.entity.server.ViewTextMessage;
import com.medsure.ui.entity.server.ViewTextMessageResponse;
import com.medsure.ui.entity.server.VitalRequestListVO;
import com.medsure.ui.entity.server.VitalScheduleRequestVO;
import com.medsure.ui.entity.server.VitalWeeklyGraphListVO;
import com.medsure.ui.entity.server.VitalsCountGraphListVO;
import com.medsure.ui.entity.server.WatchVO;
import com.medsure.ui.entity.server.calendar.WatchRxCalendarTaskVo;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestMedicationVO;
import com.medsure.ui.entity.server.createpatientflow.DeleteAnRecordVO;
import com.medsure.ui.entity.server.createpatientflow.NotificationConfigurationVO;
import com.medsure.ui.entity.server.createpatientflow.SaveMedicationImageRequestVO;
import com.medsure.ui.entity.server.editpatientflow.CreatePrescriptionRequestVO;
import com.medsure.ui.entity.server.editpatientflow.CreatePrescriptionResponseVO;
import com.medsure.util.AuthTokenChecker;

@Api(name = "caregiverapi", version = "v1", description = "To serve all care giver request i.e. mobile App")
public class CareGiverService {

	private static final Logger log = Logger.getLogger(CareGiverService.class.getName());
	public static List<String> verifyList = new ArrayList<String>();

	@Autowired
	PatientDAO patientDAO;

	@Autowired
	ClinicianDAO clinicianDAO;

	@Autowired
	UserServiceImpl userServiceImpl;

	String[] clientIDs = { "132597499285-idmv1v8si9eufqunssntqrg9k44h27ro.apps.googleusercontent.com" };
	String audience = "https://watchrx-1007.appspot.com";
	AuthTokenChecker checker = new AuthTokenChecker(clientIDs, audience);

	@ApiMethod(name = "GetWatchReachabilityStatus", path = "GetWatchReachabilityStatus", httpMethod = ApiMethod.HttpMethod.POST)
	public WatchReachabilityResponse GetWatchReachabilityStatus(WatchReachabilityRequest request) {
		log.info("care giver asking for watch reachability status");
		WatchReachabilityResponse response = new WatchReachabilityResponse();

		// if(checker.check(alertDetail.getAuthToken())){
		response.setMonitoredStatus(WatchRxFactory.getWatchService().getWatchReachabilityStatus(request));
		return response;
	}

	@ApiMethod(name = "GetAlertByPatientId", path = "getAlertbypatientid", httpMethod = ApiMethod.HttpMethod.POST)
	public PatientAlerts getAlerts(GetAlerts alertDetail) {
		log.info("Care Giver Service invoked the method getAlerts");
		PatientAlerts alertDetails = new PatientAlerts();
		// if(checker.check(alertDetail.getAuthToken())){
		List<MedicationAlert> alertList = WatchRxFactory.getAlertService().getAlertsByPatientId(alertDetail);

		alertDetails.setData(alertList);
		alertDetails.setResponseCode("001");
		alertDetails.setResponseMessage("Operationsuccessful");
		alertDetails.setResponseType("1");
		// }else{
		// alertDetails.setResponseMessage("invalid auth token");
		// }
		// details.setPatientId("abcd");
		return alertDetails;
	}

	@ApiMethod(name = "GetAlertByNurseId", path = "getAlertbynurseid", httpMethod = ApiMethod.HttpMethod.POST)
	public PatientAlerts getAlertsByNurseId(GetAlerts alertDetail) {

		PatientAlerts alertDetails = new PatientAlerts();
		List<MedicationAlert> data = new ArrayList<MedicationAlert>();
		data = WatchRxFactory.getAlertService().getAlertsByCaregiverForCaregiverApp(alertDetail);
		alertDetails.setData(data);
		alertDetails.setResponseCode("001");
		alertDetails.setResponseMessage("Operationsuccessful");
		alertDetails.setResponseType("1");
		return alertDetails;

	}

	@ApiMethod(name = "GetPatientsForNurse", path = "getpatientsfornurse", httpMethod = ApiMethod.HttpMethod.POST)
	public PatientGCInfos getPatientsForNurse(CareGiverDetails request) {
		log.info("Care Giver Service invoked the method getPatientsForNurse");
		PatientGCInfos patientGCInfos = new PatientGCInfos();
		Long roleTypeId = 5l;
		if (request.getRoleType() != null && request.getRoleType().equalsIgnoreCase("physician")) {
			roleTypeId = 3l;
		}
		patientGCInfos = WatchRxFactory.getPatientService().getAllPatientsByOrgIdAndUserId(request.getOrgId(),
				Long.valueOf(request.getCareGiverId()), roleTypeId, 0, 1000);
		patientGCInfos.setResponseMessage("Operationsuccessful");
		return patientGCInfos;
	}

	@ApiMethod(name = "EnableGPSTracking", path = "enableGPSTracking", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo enableGPSTracking(AlertByPatient gpsAlert) {
		log.info("Caregiver Service invoked enableGPSTracking call");
		GeneralInfo resp = WatchRxFactory.getGPSService().enableGPSTrackingByPatientId(gpsAlert.getPatientId());
		return resp;

	}

	@ApiMethod(name = "DisableGPSTracking", path = "disableGPSTracking", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo disableGPSTracking(AlertByPatient gpsAlert) {
		log.info("Caregiver Service invoked disableGPSTracking call");
		GeneralInfo resp = WatchRxFactory.getGPSService().disableGPSTrackingByPatientId(gpsAlert.getPatientId());
		return resp;
	}

	@ApiMethod(name = "EnableGPS", path = "enableGPS", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo enableGPS(AlertByPatient gpsAlert) {
		log.info("Caregiver Service invoked enableGPS call");
		GeneralInfo resp = WatchRxFactory.getGPSService().enableGPSByPatientId(gpsAlert.getPatientId());
		return resp;

	}

	@ApiMethod(name = "DisableGPS", path = "disableGPS", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo disableGPS(AlertByPatient gpsAlert) {
		log.info("Caregiver Service invoked disableGPS call");
		GeneralInfo resp = WatchRxFactory.getGPSService().disableGPSByPatientId(gpsAlert.getPatientId());
		return resp;
	}

	@ApiMethod(name = "GetGPSTrackingInfo", path = "getGPSTrackingInfo", httpMethod = ApiMethod.HttpMethod.POST)
	public PatientGPSAlerts getGPSTrackingInfo(AlertByPatient patient) {
		log.info("Caregiver Service invoked getGPSTrackingInfo call");
		PatientGPSAlerts resp = WatchRxFactory.getGPSService().getTrackingInfo(patient.getPatientId());

		return resp;

	}

	@ApiMethod(name = "GetAlertById", path = "getalertbyid", httpMethod = ApiMethod.HttpMethod.POST)
	public MedicationAlert getAlerByAlertId(AlertById alertId) {
		log.info("Care Giver Service invoked the method getAlerByAlertId");

		MedicationAlert alert = WatchRxFactory.getAlertService()
				.getAlertByAlertId(Long.parseLong(alertId.getAlertId()));
		return alert;
	}

	@ApiMethod(name = "GetNotesByPatientId", path = "getnotesbypatientid", httpMethod = ApiMethod.HttpMethod.POST)
	public GetOnlyPatientNotes getNotesByPatientId(AlertByPatient alertId) {
		System.out.println(alertId.toString());
		CareGiverTest sdetails = new CareGiverTest();
		// details.setPatientId("abcd");
		return sdetails.patientNotes();
	}

	/*
	 * @ApiMethod(name = "CareGiverEmailLogin", path = "emaillogin", httpMethod =
	 * ApiMethod.HttpMethod.POST) public GeneralInfo doEmailLogin(Login
	 * loginDetails) {System.out.println(loginDetails.toString()); GeneralInfo info
	 * = new GeneralInfo(); ClinicianVO clinicianVO =
	 * WatchRxFactory.getClinicianService().getClinicianByUserName(loginDetails.
	 * getLoginId()); if(checker.check(loginDetails.getAuthToken())){
	 * log.info("Login Successful"); info.setResponseMessage("Login succesful");
	 * info.setNurseId(String.valueOf(clinicianVO.getClinicianId())); }else{
	 * log.info("Login failed"); info.setResponseMessage("Login failed"); } return
	 * info;}
	 */

	@ApiMethod(name = "GetScheduleAlert", path = "schedulealert", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo scheduleAlert(ScheduleAlert details) {
		System.out.println(details.toString());
		CareGiverTest sdetails = new CareGiverTest();
		// details.setPatientId("abcd");
		return sdetails.generalInfo();
	}

	@ApiMethod(name = "SavePatientCallRequest", path = "savecallRequest", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo saveCallRequest(AlertRemainder details) {
		System.out.println(details.toString());
		CareGiverTest sdetails = new CareGiverTest();
		// details.setPatientId("abcd");
		return sdetails.generalInfo();
	}

	@ApiMethod(name = "InsertAlertStatus", path = "insertalertstatus", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo insertAlertStatus(AlertStatus info) {
		System.out.println(info.toString());
		WatchRxFactory.getAlertService().insertAlertStatus(info);
		CareGiverTest details = new CareGiverTest();
		return details.generalInfo();
	}

	@ApiMethod(name = "getAlertStatus", path = "getalertstatusbycaregiverId", httpMethod = ApiMethod.HttpMethod.POST)
	public GetAlertStatus getAlertStatusByPatientId(AlertStatus info) {
		System.out.println(info.toString());
		GetAlertStatus alertStatus = new GetAlertStatus();
		alertStatus.setAlertStatus(
				WatchRxFactory.getAlertService().getAlertStatusInfo(Long.parseLong(info.getCareGiverId())));
		return alertStatus;
	}

	@ApiMethod(name = "getAlertStatusByStatus", path = "getalertstatusByStatusBycaregiverId", httpMethod = ApiMethod.HttpMethod.POST)
	public GetAlertStatus getalertstatusByStatusBycaregiverId(AlertStatus info) {
		System.out.println(info.toString());
		GetAlertStatus alertStatus = new GetAlertStatus();
		alertStatus.setAlertStatus(WatchRxFactory.getAlertService().getAlertStatusInfoByStatusByCG(info));
		return alertStatus;
	}

	@ApiMethod(name = "VerifyPatientVisit", path = "verifyvisit", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo verifyVisit(VerifyVisit details) {
		System.out.println(details.toString());
		CareGiverTest sdetails = new CareGiverTest();
		// details.setPatientId("abcd");

		GeneralInfo generalInfo = new GeneralInfo();
		try {

			PatientInfo patInfo = WatchRxFactory.getPatientService()
					.getPatientById(Long.parseLong(details.getPatientId()));

			if (patInfo.getGcmId() != null && !patInfo.getGcmId().isEmpty()) {

				String tempString = String.valueOf(CareGiverTest.randInt());

				boolean isVerifyCodeValid = WatchRxFactory.getPatientService().updatePatientVerifyVisitCode(
						Long.parseLong(details.getPatientId()), Long.parseLong(tempString));
				if (isVerifyCodeValid) {

					ClinicianVO clinicianVO = WatchRxFactory.getClinicianService()
							.getClinician(Long.parseLong(details.getCareGiverId()), 5L);
					JsonObject jo = new JsonObject();
					jo.addProperty("messageType", "visitVerification");
					jo.addProperty("caregiverName", clinicianVO.getFirstName() + " " + clinicianVO.getLastName());
					jo.addProperty("visitVerificationCode", tempString);
					Gson gson = new Gson();
					String jsonStr = gson.toJson(jo);
					sdetails.sendMessageToPatient("message", jsonStr, patInfo.getGcmId(), patInfo.getPlatform());

					generalInfo.setResponseCode("1");
					generalInfo.setResponseMessage("Operationsuccessful");
					generalInfo.setStatus("success");
				} else {
					generalInfo.setResponseCode("1");
					generalInfo.setResponseMessage("OperationFailed");
					generalInfo.setStatus("failed");
				}
			} else {
				generalInfo.setResponseCode("1");
				generalInfo.setResponseMessage("OperationFailed");
				generalInfo.setStatus("failed");

			}

		} catch (IOException e) {
			generalInfo.setResponseCode("1");
			generalInfo.setResponseMessage("OperationFailed");
			generalInfo.setStatus("failed");
			e.printStackTrace();
		}
		return generalInfo;
	}

	@ApiMethod(name = "VerifyVisted", path = "verifyvisited", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo verifyvisited(VerifyVisit visit) {

		GeneralInfo generalInfo = new GeneralInfo();

		boolean isVerifyCodeValid = WatchRxFactory.getPatientService().visitVerificationCodeFound(
				Long.parseLong(visit.getPatientId()), Long.parseLong(visit.getVerifyCode()));
		if (isVerifyCodeValid) {
			generalInfo.setResponseCode("1");
			generalInfo.setResponseMessage("Operationsuccessful");
			generalInfo.setStatus("success");
		} else {
			generalInfo.setResponseCode("1");
			generalInfo.setResponseMessage("OperationFailed");
			generalInfo.setStatus("failed");

		}

		return generalInfo;
	}

	@ApiMethod(name = "CareGiverStatus", path = "caregiverstatus", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo careGiverStatus(CareGiverStatus details) {
		log.info("In Caregiverservice careGiverStatus method");
		log.info("Caregiver status is: " + details.toString());
		System.out.println(details.toString());
		CareGiverTest sdetails = new CareGiverTest();
		// details.setPatientId("abcd");
		try {
			PatientInfo pat = WatchRxFactory.getPatientService().getPatientById(Long.parseLong(details.getPatinetId()));
			log.info("GCM id is: " + pat.getGcmId());
			ClinicianVO clinicianVO = WatchRxFactory.getClinicianService()
					.getClinician(Long.parseLong(details.getCareGiverId()), 5L);
			JsonObject jo = new JsonObject();
			jo.addProperty("messageType", "nurseOnTheWay");
			// jo.addProperty("caregiverName", clinicianVO.getUserName());
			jo.addProperty("caregiverName", clinicianVO.getFirstName());
			jo.addProperty("status", details.getStatus());
			Gson gson = new Gson();
			String jsonStr = gson.toJson(jo);
			log.info("GCM message to be sent is " + jsonStr);
			sdetails.sendMessageToPatient("message", jsonStr, pat.getGcmId(), pat.getPlatform());
		} catch (IOException e) {
			// TODO Auto-generated catch block
			log.severe("Something went wrong in careGiverStatus method " + e.getMessage());
			e.printStackTrace();
		}
		return sdetails.generalInfo();
	}

	@ApiMethod(name = "RemindMedicationMissed", path = "remindMedicationMissed", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo remindMedicationMissed(MedicationAlert details) {
		log.info("In method remindMedicationMissed");
		log.info("MedicationAlert is: " + details.toString());
		System.out.println(details.toString());
		CareGiverTest sdetails = new CareGiverTest();
		ClinicianVO caregiver = new ClinicianVO();
		// details.setPatientId("abcd");
		try {
			Gson gson = new Gson();
			if (details.getClinicianId() != null) {
				caregiver = WatchRxFactory.getClinicianService().getClinician(Long.parseLong(details.getClinicianId()),
						5L);
			} else {
				String clinicianId = WatchRxFactory.getClinicianService()
						.getClinicianId(Long.parseLong(details.getPatientId()));
				caregiver = WatchRxFactory.getClinicianService().getClinician(Long.parseLong(clinicianId), 5L);
			}
			PatientInfo patIn = WatchRxFactory.getPatientService()
					.getPatientById(Long.parseLong(details.getPatientId()));
			String name = caregiver.getFirstName();
			log.info("Message being sent to gcm id: " + patIn.getGcmId());
			log.info("Caregiver Name:::::::" + name);
			String alert = gson.toJson(details, MedicationAlert.class);
			JsonElement jelem = gson.fromJson(alert, JsonElement.class);
			JsonObject jobj = jelem.getAsJsonObject();
			jobj.addProperty("messageType", "nurseReminder");
			jobj.addProperty("caregiverName", name);
			String gcmObj = gson.toJson(jobj);
			log.info("GCM messge being sent is: " + gcmObj);
			sdetails.sendMessageToPatient("message", gcmObj, patIn.getGcmId(), patIn.getPlatform());
		} catch (IOException e) {
			// TODO Auto-generated catch block
			log.severe("Something went wrong in remindMedicationMissed method: " + e.getMessage());
			e.printStackTrace();
		}
		return sdetails.generalInfo();
	}

	@ApiMethod(name = "MedicationInfoByListOfIds", path = "medicationInfoByIdList", httpMethod = ApiMethod.HttpMethod.POST)
	public List<MedicationInfo> medicationInfoByListOfIds(MedicationIds details) {
		System.out.println(details.toString());
		List<MedicationInfo> result = WatchRxFactory.getAlertService()
				.getMedicationByMedicationIds(details.getMedicationIds());

		return result;
	}

	@ApiMethod(name = "MedicationInfoByPatient", path = "medicationInfoByPatient", httpMethod = ApiMethod.HttpMethod.POST)
	public MedicationInfoList getMedicationInfoByPatient(GeneralRequest patient) {
		// System.out.println(patient.toString());
		MedicationInfoList result = new MedicationInfoList();

		if (patient.getPatientId() == null) {
			result.setResponseMessage("Patient Id is invalid");
			result.setResponseType("Failure");
		} else {
			result = WatchRxFactory.getAlertService().getMedicationListByPatient(patient.getPatientId());
		}
		return result;
	}

	@ApiMethod(name = "CareGiverLogin", path = "login", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo doLogin(Login loginDetails) {

		log.info(loginDetails.toString());
		GeneralInfo info = new GeneralInfo();
		try {
			ClinicianVO clinicianVO = WatchRxFactory.getClinicianService()
					.getClinicianByUserName(loginDetails.getLoginId());
			if (clinicianVO.getUserStatus().equalsIgnoreCase("N")) {
				info.setResponseMessage("Login failed.User account not activated.");
			} else if (clinicianVO.getPassword().equalsIgnoreCase(loginDetails.getPassword())) {
				log.info("Login Successful");
				info.setResponseMessage("Login succesful");
				info.setNurseId(String.valueOf(clinicianVO.getClinicianId()));
			} else {
				log.info("Login failed");
				info.setResponseMessage("Login failed");
			}
		} catch (Exception e) {
			log.info("Caregiver Login exception failed...." + e.getMessage());
			e.getMessage();
		}
		return info;
	}

	@ApiMethod(name = "userInfo", path = "userInfo", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo getUserDetails(User user) throws OAuthRequestException, IOException {
		GeneralInfo info = new GeneralInfo();
		if (user == null) {
			info.setResponseMessage("User Id not exist" + "User Email Id not exist");
		} else {
			info.setResponseMessage("User Id " + user.getUserId() + "User Email Id " + user.getEmail());
		}
		return info;
	}

	@ApiMethod(name = "getUserEmail", path = "getUserEmail", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo getMessage() {
		GeneralInfo info = new GeneralInfo();
		UserService userService = UserServiceFactory.getUserService();
		User user = userService.getCurrentUser();
		String message;
		if (user == null) {
			message = "No one is logged in!\nSent from App Engine at " + new Date();
			info.setResponseMessage("User Id not exist" + "User Email Id not exist");
		} else {
			message = "Hello, " + user.getEmail() + "!\nSent from App Engine at " + new Date();
			info.setResponseMessage("User Id " + user.getUserId() + "User Email Id " + user.getEmail());
		}
		log.info("Returning message \"" + message + "\"");
		return info;
	}

	@ApiMethod(name = "getClinicianDetailsById", path = "getClinicianDetailsById", httpMethod = ApiMethod.HttpMethod.POST)
	public ClinicianDetails getClinicianDetailsById(CareGiverDetails details) {
		ClinicianDetails info = new ClinicianDetails();
		ClinicianVO vo = WatchRxFactory.getClinicianService()
				.getClinicianByUserId(Long.parseLong(details.getCareGiverId()), Long.parseLong(details.getRoleType()));
		vo.setPassword(null);
		info.setClinicianDetails(vo);
		return info;
	}

	@ApiMethod(name = "CareGiverPasswordReset", path = "careGiverPasswordReset", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo doCareGiverPasswordReset(Login loginDetails) {
		System.out.println(loginDetails.toString());
		GeneralInfo info = new GeneralInfo();
		try {
			/*
			 * if(userServiceImpl.isPasswordInHistory(loginDetails.getLoginId(),loginDetails
			 * .getPassword())) { info.setResponseCode("001");
			 * info.setResponseMessage("Last 12 passwords cannot be repeated.");
			 * info.setStatus("Failure"); return info; }else {
			 */
			WatchRxFactory.getClinicianService().resetPasswordByUserName(loginDetails);
			// }
		} catch (Exception e) {
			info.setResponseMessage("Operation failed");
			e.getMessage();
		}
		info.setResponseMessage("Operation successful");
		return info;
	}

	@ApiMethod(name = "insertCGAppUpgradeStatus", path = "insertCGAppUpgradeStatus", httpMethod = ApiMethod.HttpMethod.POST)
	public void insertCGAppUpgradeStatus(WatchCGAppStatus status) {
		log.info("Patient Service invoked the method insertWatchMobileStatus");
		WatchRxFactory.getWatchService().insertWatchMobileStatus(status);
	}

	@ApiMethod(name = "sendRequestToGetHeartRate", path = "sendRequestToGetHeartRate", httpMethod = ApiMethod.HttpMethod.POST)
	public GeneralInfo sendRequestToGetHeartRate(CaregiverRequestForHeartRate details) {
		log.info("In Caregiverservice CaregiverToPatientHeartRate method");
		log.info("Caregiver status is: " + details.toString());

		CareGiverTest sdetails = new CareGiverTest();

		try {// getPatientId getPatinetId
			PatientInfo patient = WatchRxFactory.getPatientService()
					.getPatientById(Long.parseLong(details.getPatientId()));
			log.info("GCM id is: " + patient.getGcmId());
			ClinicianVO clinicianVO = WatchRxFactory.getClinicianService()
					.getClinician(Long.parseLong(details.getCareGiverId()), 5L);
			JsonObject jo = new JsonObject();
			jo.addProperty("messageType", "giveMeHeartRate");
			jo.addProperty("caregiverName", clinicianVO.getFirstName());
			Gson gson = new Gson();
			String jsonStr = gson.toJson(jo);
			log.info("GCM message to be sent is " + jsonStr);
			sdetails.sendMessageToPatient("message", jsonStr, patient.getGcmId(), patient.getPlatform());
		} catch (IOException e) {
			// TODO Auto-generated catch block
			log.severe("Something went wrong in careGiverStatus method " + e.getMessage());
			e.printStackTrace();
		}
		return sdetails.generalInfo();
	}

	@ApiMethod(name = "AddMedication", path = "addMedication", httpMethod = ApiMethod.HttpMethod.POST)
	public CreatePrescriptionResponseVO addMedication(CreatePrescriptionRequestVO requestVO) {
		log.info("Caregiver Service invoked AddMedication call");

		CreatePrescriptionResponseVO response = new CreatePrescriptionResponseVO();

		if (WatchRxFactory.getPatientService().getPatientById(requestVO.getPatientId()) == null) {
			response.setResponseCode(Constants.ResponseCode.PATIENTNOTFOUND);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.PATIENTNOTFOUND;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;

		} else {

			Long createdPrescriptionId = WatchRxFactory.getPatientService().createPrescription(requestVO);
			response.setCreatedPrescription(createdPrescriptionId);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.PRESCRIPTIONCREATED;
			response.setMessages(messages);
			response.setSuccess(true);
			// sendMedicationInfoByPrsecriptionId(createdPrescriptionId);
			return response;

		}

	}

	@ApiMethod(name = "AddMedicationImage", path = "addMedicationImage", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO saveMedicationImage(SaveMedicationImageRequestVO patientImage) {
		StatusVO response = new StatusVO();
		try {

			if (patientImage.getImageFile() != null) {
				return WatchRxFactory.getPatientService().saveMedicationImage(patientImage);
			} else {

				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}
		} catch (Exception e) {
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}
	}

	@ApiMethod(name = "DeleteMedication", path = "deleteMedication", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO removePrescription(DeleteAnRecordVO record) throws Exception {
		log.info("inside deletePrescription:::: " + record.getItemId());

		StatusVO response = new StatusVO();
		try {

			if (WatchRxFactory.getPatientService().getPrescription(record.getItemId()) == null) {
				response.setSuccess(false);
				response.setResponseCode(Constants.ResponseCode.PRESCRIPTIONNOTFOUND);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.PRESCRIPTIONNOTFOUND;
				response.setMessages(messages);
				return response;
			} else {
				/*
				 * Long patientId =
				 * WatchRxFactory.getPatientService().getPrescription(record.getItemId())
				 * .getWatchrxPatient().getPatientId(); log.info("Sending gcm to patientId: " +
				 * patientId);
				 */

				WatchRxFactory.getPatientService().deletePrescription(record.getItemId());
				response.setSuccess(true);
				response.setResponseCode(Constants.ResponseCode.PRESCRIPTIONDELETED);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.PRESCRIPTIONDELETED;
				response.setMessages(messages);
				// sendMedicationInfoByPatientId(patientId);
				return response;
			}

		} catch (Exception e) {
			// log.error("Error in removePrescription: " + e.getMessage());
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}

	@ApiMethod(name = "UpdateNotificationConfiguration", path = "updateNotificationConfiguration", httpMethod = ApiMethod.HttpMethod.POST)
	public NotificationConfigurationVO updateMedication(NotificationConfigurationVO record) throws Exception {
		log.info("inside updateNotificationConfiguration:::: " + record);

		NotificationConfigurationVO response = new NotificationConfigurationVO();
		try {
			response = WatchRxFactory.getPatientService().updateNotificationConfiguration(record);

		} catch (Exception e) {
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

		return response;
	}

	@ApiMethod(name = "getTasks", path = "getTasks", httpMethod = ApiMethod.HttpMethod.POST)
	public TaskListVO getDueTasksByUserId(TasksVO tasksVO) throws Exception {
		TaskListVO response = new TaskListVO();
		if (tasksVO.getTaskType().equalsIgnoreCase("Due")) {
			response = WatchRxFactory.getClinicianService().getDueTasks(tasksVO);
			response.setResultCount(WatchRxFactory.getClinicianService().getDueTasksCount(tasksVO.getOrgId(),
					tasksVO.getRoleType(), tasksVO.getUserId()));
		} else {
			response = WatchRxFactory.getClinicianService().getFutureTasks(tasksVO);
			response.setResultCount(WatchRxFactory.getClinicianService().getFutureTasksCount(tasksVO.getOrgId(),
					tasksVO.getRoleType(), tasksVO.getUserId()));
		}
		response.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		response.setMessages(messages);
		return response;
	}

	@ApiMethod(name = "getTasksCountByDate", path = "getTasksCountByDate", httpMethod = ApiMethod.HttpMethod.POST)
	public TaskListVO getDueTasksByDate(TasksVO tasksVO) throws Exception {

		TaskListVO response = new TaskListVO();
		if (tasksVO.getTaskType().equalsIgnoreCase("Due")) {
			response.setResultCount(WatchRxFactory.getClinicianService().getDueTasksCount(tasksVO.getOrgId(),
					tasksVO.getRoleType(), tasksVO.getUserId()));
		} else {
			response.setResultCount(WatchRxFactory.getClinicianService().getFutureTasksCount(tasksVO.getOrgId(),
					tasksVO.getRoleType(), tasksVO.getUserId()));
		}
		response.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		response.setMessages(messages);
		return response;
	}

	@ApiMethod(name = "getVitalByDate", path = "getVitalByDate", httpMethod = ApiMethod.HttpMethod.POST)
	public Map<String, Object> getVitalDataByPatientIdAndDate(Map<String, String> patientData,
			HttpServletRequest request) {
		Map<String, Object> response = new HashMap<>();
		try {
			response.put("status", true);
			response.put("data", WatchRxFactory.getPatientService().getVitalDataByPatientIdAndDate(patientData));
			return response;
		} catch (Exception e) {
			response.put("status", false);
			response.put("data", "No data available.");
		}
		return response;
	}

	@ApiMethod(name = "getPatientCustomAlerts", path = "getPatientCustomAlerts", httpMethod = ApiMethod.HttpMethod.POST)
	public List<CustomAlertsForPatientVO> getPatientCustomAlerts(Map<String, String> patientData) throws Exception {

		List<CustomAlertsForPatientVO> customAlertsForPatientVO = WatchRxFactory.getPatientService()
				.getCustomAlertInfo(Long.parseLong(patientData.get("patientId")));

		return customAlertsForPatientVO;
	}

	@ApiMethod(name = "sendTextToPatient", path = "sendTextToPatient", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO sendTextMessageToPatient(HttpServletRequest request, ScheduledTextMessageInfo scheduleRequest)
			throws Exception {
		CareGiverTest sdetails = new CareGiverTest();
		StatusVO resp = new StatusVO();
		String messages[] = new String[1];
		try {
			PatientInfo patient = WatchRxFactory.getPatientService().getPatientById(scheduleRequest.getPatientId());
			WatchRxPatientTextMessages patientTextMesssge = null;
			patientTextMesssge = new WatchRxPatientTextMessages();
			patientTextMesssge.setWatchrxPatient(new WatchrxPatient(scheduleRequest.getPatientId()));
			patientTextMesssge.setQuestion(scheduleRequest.getQuestion());
			if (patient.getTimeZone() != null) {
				Date date = getPatientDateUsingTimeZoneId(patient.getTimeZone());
				patientTextMesssge.setCreatedDate(date);
				patientTextMesssge.setUpdatedDate(date);
			} else {
				patientTextMesssge.setCreatedDate(new Date());
				patientTextMesssge.setUpdatedDate(new Date());
			}
			patientTextMesssge.setQuestion_time("");
			patientTextMesssge.setAnswer(scheduleRequest.getAnswer());
			patientTextMesssge.setSenderName(scheduleRequest.getSenderName());
			patientTextMesssge.setMessageStatus("not_sent");
			patientTextMesssge.setMessageResponse("Not yet received");
			patientTextMesssge.setReview(scheduleRequest.getReview());

			WatchRxPatientTextMessages savedMessgae = WatchRxFactory.getPatientService()
					.saveTextMessage(patientTextMesssge);

			JsonObject jo = new JsonObject();
			jo.addProperty("messageType", "textMessage");
			jo.addProperty("caregiverName", scheduleRequest.getSenderName());
			jo.addProperty("question", scheduleRequest.getQuestion());
			jo.addProperty("answer", scheduleRequest.getAnswer());
			jo.addProperty("questionId", savedMessgae.getQuestionId());
			Gson gson = new Gson();
			String jsonStr = gson.toJson(jo);
			sdetails.sendMessageToPatient("message", jsonStr, patient.getGcmId(), patient.getPlatform());

			if (scheduleRequest.getReview() != null && !scheduleRequest.getReview().isEmpty()) {
				if (!scheduleRequest.getReview().equalsIgnoreCase("PERSONAL")) {
					WatchRxFactory.getPatientService().addCummalativeMins(scheduleRequest.getPatientId(),
							scheduleRequest.getReview(), 1.0);
				}
			}

			boolean success = true;
			resp.setSuccess(success);
			messages[0] = "Message Sent Successfully";
			resp.setMessages(messages);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			boolean success = false;
			resp.setSuccess(success);
			messages[0] = "Message Sent Failed";
			resp.setMessages(messages);
		}
		return resp;
	}

	@ApiMethod(name = "getTextMessageResponseByPatient", path = "getTextMessageResponseByPatient", httpMethod = ApiMethod.HttpMethod.POST)
	public @ResponseBody ViewTextMessageResponse getTextMessageResponse(TasksVO vo) {
		ViewTextMessageResponse viewTextMessageResponse = new ViewTextMessageResponse();
		Long count = null;
		List<ViewTextMessage> data = WatchRxFactory.getPatientService()
				.getPatientTextMessageResponseForDate(vo.getPatientId(), vo.getStartDate(), vo.getEndDate());
		count = WatchRxFactory.getPatientService().getTextMessageOnlyReceivedByPatientIdCount(vo.getPatientId());
		viewTextMessageResponse.setResultCount(count);
		viewTextMessageResponse.setViewMessages(data);
		viewTextMessageResponse.setStatus(true);
		return viewTextMessageResponse;

	}

//	@ApiMethod(name = "getValidationForPatient", path = "getValidationForPatient", httpMethod = ApiMethod.HttpMethod.POST)
//	public ValidationVO getValidationForPatient(ValidationVO validationVO) throws Exception {
//		ValidationVO result = new ValidationVO();
//		result = WatchRxFactory.getPatientService().getValidationForPatient(validationVO.getPatientId(),
//				validationVO.getDate());
//		result.setCountInfo(
//				WatchRxFactory.getPatientService().validationInfo(validationVO.getPatientId(), validationVO.getDate()));
//		String[] messages = { "Opertion Successfull" };
//		result.setMessages(messages);
//		result.setSuccess(true);
//		return result;
//	}
//
//	@ApiMethod(name = "saveValidationForPatient", path = "saveValidationForPatient", httpMethod = ApiMethod.HttpMethod.POST)
//	public StatusVO saveValidationForPatient(HttpServletRequest request, ValidationVO validationVO) throws Exception {
//		StatusVO statusVO = new StatusVO();
//		boolean result = WatchRxFactory.getPatientService().updateValidationForPatient(validationVO);
//		if (result) {
//			String[] messages = { "Opertion Successfull" };
//			statusVO.setMessages(messages);
//			statusVO.setSuccess(true);
//		} else {
//			String[] messages = { "Failed to Update Validation" };
//			statusVO.setMessages(messages);
//			statusVO.setSuccess(false);
//		}
//		return statusVO;
//	}

	@ApiMethod(name = "vitalByPatient", path = "vitalByPatient", httpMethod = ApiMethod.HttpMethod.POST)
	public VitalWeeklyGraphListVO vitalGraphWeekly(HttpServletRequest request, VitalRequestListVO vitalRequestListVO) {
		VitalWeeklyGraphListVO response = new VitalWeeklyGraphListVO();

		BaseListVO<VitalScheduleRequestVO> vitalReq = new BaseListVO<>();
		List<VitalScheduleRequestVO> vsrList = new ArrayList<>();
		if (vitalRequestListVO.getVitalTypeNameList() != null) {
			for (String vitalList : vitalRequestListVO.getVitalTypeNameList()) {
				VitalScheduleRequestVO vsr = new VitalScheduleRequestVO();
				vsr.setVitalTypeName(vitalList);
				vsr.setPatientId(vitalRequestListVO.getPatientId());
				vsrList.add(vsr);
			}
		}
		vitalReq.seteList(vsrList);

		if (vitalRequestListVO.getPeriodType() != null) {
			response = WatchRxFactory.getPatientService().getVitalGraphDetails(vitalRequestListVO);
			response.setThresholdConfig(WatchRxFactory.getPatientService().getThresholdConfiguration(vitalReq));
		} else {
			response.setSuccess(false);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "Period Type for Vital Request is not provided";
			response.setMessages(messages);
		}

		response.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		response.setMessages(messages);

		return response;
	}

	@ApiMethod(name = "vitalPedometerByPatient", path = "vitalPedometerByPatient", httpMethod = ApiMethod.HttpMethod.POST)
	public VitalsCountGraphListVO vitalPedometerWeeklyGraph(HttpServletRequest request,
			VitalRequestListVO vitalRequestListVO) {
		VitalsCountGraphListVO response = new VitalsCountGraphListVO();

		if (vitalRequestListVO.getPeriodType() != null) {
			response = WatchRxFactory.getPatientService().getPedometerDetails(vitalRequestListVO);
			response.setThresholdConfigVO(
					WatchRxFactory.getPatientService().getThresholdConfig(vitalRequestListVO.getPatientId()));
		} else {
			response.setSuccess(false);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "Period Type for Vital Request is not provided";
			response.setMessages(messages);
		}

		response.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		response.setMessages(messages);

		return response;
	}

	@ApiMethod(name = "vitalSleepMonitorByPatient", path = "vitalSleepMonitorByPatient", httpMethod = ApiMethod.HttpMethod.POST)
	public VitalsCountGraphListVO vitalSleepMonitorWeeklyGraph(HttpServletRequest request,
			VitalRequestListVO vitalRequestListVO) {
		VitalsCountGraphListVO response = new VitalsCountGraphListVO();

		if (vitalRequestListVO.getPeriodType() != null) {
			response = WatchRxFactory.getPatientService().getSleepMonitorDetails(vitalRequestListVO);
			response.setThresholdConfigVO(
					WatchRxFactory.getPatientService().getThresholdConfig(vitalRequestListVO.getPatientId()));
		} else {
			response.setSuccess(false);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "Period Type for Vital Request is not provided";
			response.setMessages(messages);
		}

		response.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		response.setMessages(messages);

		return response;
	}

	private Date getPatientDateUsingTimeZoneId(String timeZoneId) {
		try {
			LocalDateTime dt = LocalDateTime.now();
			ZoneId systemZone = ZoneId.systemDefault();
			ZonedDateTime zoneDateTime = LocalDateTime
					.parse(dt.toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS")).atZone(systemZone);

			LocalDateTime afterConvertToSystemTime = LocalDateTime.ofInstant(zoneDateTime.toInstant(),
					ZoneId.of(timeZoneId));

			Date d = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").parse(afterConvertToSystemTime.toString());
			return d;
		} catch (Exception e) {
			return new Date();
		}

	}

	@ApiMethod(name = "alertsCntByDate", path = "alertsCntByDate", httpMethod = ApiMethod.HttpMethod.POST)
	public Map<String, Long> getAlertsByClinicianByPaginantionByType(HttpServletRequest request,
			AlertRequestVO alertsRequestBody) throws Exception {
		Map<String, Long> patientData = new HashMap<>();
		try {
			alertsRequestBody.setFilterValue("CaregiverApp");
			log.info("alertsCntByDate: " + alertsRequestBody.toString());
			List<MedicationAlert> tmpdata = WatchRxFactory.getAlertService()
					.getAlertsForCaregiverFilterV1(alertsRequestBody, alertsRequestBody.getOrgId());
			log.info("Alertd Data:" + tmpdata.size());
			patientData = tmpdata.stream()
					.collect(Collectors.groupingBy(MedicationAlert::getAlertType, Collectors.counting()));
			int counts = WatchRxFactory.getPatientChatService()
					.getUnReadMessagesPatient(alertsRequestBody.getUserId(), alertsRequestBody.getOrgId()).size();
			patientData.put("messageCount", Long.valueOf(counts));
			return patientData;
		} catch (Exception e) {
			return patientData;
		}
	}

	@ApiMethod(name = "savePrescription", path = "savePrescription", httpMethod = ApiMethod.HttpMethod.POST)
	public CreatePrescriptionResponseVO savePatienPrescriptions(HttpServletRequest request,
			CreatePrescriptionRequestVO createPatientRequestMedicationVO) throws Exception {
		CreatePrescriptionResponseVO response = new CreatePrescriptionResponseVO();
		try {

			Long createdPrescriptionId = WatchRxFactory.getPatientService()
					.createPrescription(createPatientRequestMedicationVO);
			response.setCreatedPrescription(createdPrescriptionId);
			response.setResponseCode(Constants.ResponseCode.PRESCRIPTIONCREATED);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.PRESCRIPTIONCREATED;
			response.setMessages(messages);
			response.setSuccess(true);
			return response;
		} catch (Exception e) {
			e.printStackTrace();
			throw new WatchRxServiceException("Error while updating the medications.");
		}
	}

	@ApiMethod(name = "editPrescription", path = "editPrescription", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO editPrescription(HttpServletRequest request,
			CreatePatientRequestMedicationVO createPatientRequestMedicationVO) throws Exception {

		StatusVO response = new StatusVO();
		try {

			if (WatchRxFactory.getPatientService()
					.getPrescriptionById(createPatientRequestMedicationVO.getPrescriptionId()) == null) {
				response.setResponseCode(Constants.ResponseCode.PRESCRIPTIONNOTFOUND);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.PRESCRIPTIONNOTFOUND;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}

			WatchRxFactory.getPatientService().editPrescription(createPatientRequestMedicationVO);
			sendMedicationInfoByPrsecriptionId(createPatientRequestMedicationVO.getPrescriptionId());

			response.setResponseCode(Constants.ResponseCode.PRESCRIPTIONEDITED);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.PRESCRIPTIONEDITED;
			response.setMessages(messages);
			response.setSuccess(true);
			return response;
		} catch (Exception e) {
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}

	@ApiMethod(name = "deletePrescription", path = "deletePrescription", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO deletePrescription(HttpServletRequest request, @Named("prescriptionId") Long prescriptionId)
			throws Exception {
		WatchRxFactory.getPatientService().deletePrescription(prescriptionId);
		PatientPrescriptionVO vo = WatchRxFactory.getPatientService().getPatientPrescription(prescriptionId);
		sendMedicationInfo(vo);
		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(true);
		List<String> errorMsgs = new ArrayList<String>();
		statusVO.setMessages(errorMsgs.toArray(new String[errorMsgs.size()]));
		return statusVO;
	}

	private void sendMedicationInfo(PatientPrescriptionVO prescriptionVO) {
		CareGiverTest sdetails = new CareGiverTest();
		RegisterWatch watch = new RegisterWatch();
		PatientVO pdvo = WatchRxFactory.getPatientService().getPatient(prescriptionVO.getPatient().getPatientId());
		if (pdvo != null && pdvo.getWatch() != null) {
			String imeiNo = pdvo.getWatch().getImeiNumber();
			if (imeiNo != null) {
				watch.setImeiNo(imeiNo);

				PatientDetails pd = WatchRxFactory.getPatientService().getPatientDetailsByImei(watch);

				PatientInfo gcmId = WatchRxFactory.getPatientService()
						.getPatientById(prescriptionVO.getPatient().getPatientId());
				if (gcmId.getGcmId() != null && gcmId.getGcmId().length() > 0) {
					JsonObject jo = new JsonObject();
					jo.addProperty("messageType", "UpdatedMedicationInfo");
					jo.addProperty("patientDetails", pd.toString());

					// jo.addProperty("visitVerificationCode", tempString);
					Gson gson = new Gson();
					String jsonStr = gson.toJson(jo);
					try {
						sdetails.sendMessageToPatient("message", jsonStr, gcmId.getGcmId(), gcmId.getPlatform());

					} catch (IOException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}
	}

	private void sendMedicationInfoByPrsecriptionId(Long prescriptionId) {

		Long patientId = WatchRxFactory.getPatientService().getPatientPrescription(prescriptionId).getPatient()
				.getPatientId();
		CareGiverTest sdetails = new CareGiverTest();
		RegisterWatch watch = new RegisterWatch();
		PatientVO pdvo = WatchRxFactory.getPatientService().getPatient(patientId);
		if (pdvo != null && pdvo.getWatch() != null) {
			String imeiNo = pdvo.getWatch().getImeiNumber();
			if (imeiNo != null) {
				watch.setImeiNo(imeiNo);

				PatientDetails pd = WatchRxFactory.getPatientService().getPatientDetailsByImei(watch);

				PatientInfo gcmId = WatchRxFactory.getPatientService().getPatientById(patientId);
				if (gcmId.getGcmId() != null && gcmId.getGcmId().trim().length() > 0) {
					JsonObject jo = new JsonObject();
					jo.addProperty("messageType", "UpdatedMedicationInfo");
					jo.addProperty("patientDetails", pd.toString());

					// jo.addProperty("visitVerificationCode", tempString);
					Gson gson = new Gson();
					String jsonStr = gson.toJson(jo);
					try {
						sdetails.sendMessageToPatient("message", jsonStr, gcmId.getGcmId(), gcmId.getPlatform());

					} catch (IOException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}
	}

	@ApiMethod(name = "getLatestCarePlanDetails", path = "getLatestCarePlanDetails", httpMethod = ApiMethod.HttpMethod.POST)
	public Map<String, Object> getLatestCarePlanDetails(Map<String, String> patientData) throws Exception {

		return WatchRxFactory.getPatientCarePlanService()
				.getLatestCarePlanDetails(Long.parseLong(patientData.get("patientId")));
	}

	@ApiMethod(name = "patientCarePlanDetails", path = "patientCarePlanDetails", httpMethod = ApiMethod.HttpMethod.POST)
	public List<PatientCarePlanVO> getLatestCarePlanDetailsByPlanId(Map<String, String> patientData) throws Exception {

		return WatchRxFactory.getPatientCarePlanService().getCarePlanByPatientIdAmdCarePlanId(
				Long.parseLong(patientData.get("patientId")), Long.parseLong(patientData.get("carePlanId")));
	}

	@ApiMethod(name = "getPatientsForTasks", path = "getPatientsForTasks", httpMethod = ApiMethod.HttpMethod.POST)
	public PatientMinimalListVO getPatientsByCaseMangersByUserId(Map<String, String> userData) throws Exception {
		PatientMinimalListVO response = new PatientMinimalListVO();
		try {
			log.info("Get Patient for Tasks: " + userData);

			response = WatchRxFactory.getClinicianService().getAllPatientByOrg(Long.parseLong(userData.get("orgId")),
					Long.parseLong(userData.get("roleId")), Long.parseLong(userData.get("userId")));

			response.setSuccess(true);
			response.setResponseCode(Constants.ResponseCode.CLINICIANSSOBTAINEDSUCCESSFULLY);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.CLINICIANSSOBTAINEDSUCCESSFULLY;
			response.setMessages(messages);
			return response;

		} catch (Exception e) {
			e.printStackTrace();
			log.info("Excption:" + e.getMessage());
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}
	}

	@ApiMethod(name = "patientAddressBillingInfo", path = "patientAddressBillingInfo", httpMethod = ApiMethod.HttpMethod.POST)
	public PatientWatchCaregiverListVO getPatientAddressBillingStatus(Map<String, String> userData) throws Exception {

		List<Object[]> patientsList = new ArrayList<>();
		Long patientsCount = WatchRxFactory.getPatientService().getAllPatientsCountByOrgId(5,
				Long.parseLong(userData.get("orgId")), Long.parseLong(userData.get("userId")));

		patientsList = WatchRxFactory.getPatientService().getPatientBillingAddressInfo(
				Long.parseLong(userData.get("orgId")), 0, patientsCount.intValue(),
				Long.parseLong(userData.get("orgId")));

		List<PatientWatchCaregiverInfoVO> data = new ArrayList<PatientWatchCaregiverInfoVO>();
		PatientWatchCaregiverListVO patientsInfo = new PatientWatchCaregiverListVO();
		String msg[] = new String[1];
		if (patientsList.size() > 0) {
			for (Object[] patient : patientsList) {
				Map<String, Object> dataCnt = WatchRxFactory.getPatientService()
						.validationInfo(((BigInteger) patient[0]).longValue(), null);
				PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
				pwcInfo.setTotalMins(((Double) dataCnt.get("phMins")).intValue());
				pwcInfo.setNoOfDays((Integer) dataCnt.get("daysCounted"));
				pwcInfo.setPatientId(patient[0].toString());
				pwcInfo.setPatientName(patient[1].toString());
				if (patient[2] != null) {
					pwcInfo.setdob(patient[2].toString());
				}

				if (dataCnt != null && ((int) dataCnt.get("rpmMins") >= 20 || (int) dataCnt.get("ccmMins") >= 20)
						&& (int) dataCnt.get("daysCounted") >= 16) {
					pwcInfo.setBillingStatus("VALIDATION PENDING");
				} else {
					pwcInfo.setBillingStatus("IN PROGRESS");
				}

				List<String> cptCodeList = new ArrayList<String>();
				if (patient[0].toString() != null) {
					String pId = patient[0].toString();
					if (pId != null && !pId.isEmpty()) {
						cptCodeList = WatchRxFactory.getPatientService().getCPTCodeByPatientId(Long.parseLong(pId));
					}
				}
				pwcInfo.setcptCode(cptCodeList);
				data.add(pwcInfo);
			}
			patientsInfo.setPatientsInfo(data);
			patientsInfo.setCount(patientsCount);
			patientsInfo.setSuccess(true);
			msg[0] = new String("Operation Successful");
			patientsInfo.setMessages(msg);
		} else {
			WatchRxException excep = new WatchRxException(WatchRxExceptionCodes.EMPTY_PATIENTLIST);
			msg[0] = new String(excep.getErrDesc());
			patientsInfo.setResponseCode(Integer.parseInt(excep.getErrCode()));
			patientsInfo.setMessages(msg);
			patientsInfo.setSuccess(false);
		}
		return patientsInfo;
	}

	@ApiMethod(name = "getValidationForPatient", path = "getValidationForPatient", httpMethod = ApiMethod.HttpMethod.POST)
	public ValidationVO getValidationForPatient(Map<String, String> valData) throws Exception {
		ValidationVO result = new ValidationVO();
		result = WatchRxFactory.getPatientService().getValidationForPatient(Long.parseLong(valData.get("patientId")),
				valData.get("date"));
		result.setCountInfo(WatchRxFactory.getPatientService().validationInfo(Long.parseLong(valData.get("patientId")),
				valData.get("date")));
		String[] messages = { "Opertion Successfull" };
		result.setMessages(messages);
		result.setSuccess(true);
		return result;
	}

	@ApiMethod(name = "saveValidationForPatient", path = "saveValidationForPatient", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO saveValidationForPatient(ValidationVO validationVO) throws Exception {
		StatusVO statusVO = new StatusVO();

		Long userId = WatchRxFactory.getClinicianService()
				.getUserIdByClinicianId(validationVO.getLastUpdatedByUserId());
		log.info("Last updated user Id:" + userId);
		validationVO.setLastUpdatedByUserId(userId);

		boolean result = WatchRxFactory.getPatientService().updateValidationForPatient(validationVO);
		if (result) {
			String[] messages = { "Opertion Successfull" };
			statusVO.setMessages(messages);
			statusVO.setSuccess(true);
		} else {
			String[] messages = { "Failed to Update Validation" };
			statusVO.setMessages(messages);
			statusVO.setSuccess(false);
		}
		return statusVO;
	}

	@ApiMethod(name = "saveTasks", path = "saveTasks", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO saveTasks(TasksVO tasksVO) throws Exception {

//		Long userId = WatchRxFactory.getClinicianService().getUserIdByClinicianId(tasksVO.getUserId());
//		tasksVO.setUserId(userId);

		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(WatchRxFactory.getClinicianService().saveTask(tasksVO));
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		statusVO.setMessages(messages);
		return statusVO;
	}

	@ApiMethod(name = "deleteTask", path = "deleteTask", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO deleteTask(TasksVO tasksVO) throws Exception {
		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(WatchRxFactory.getClinicianService().deleteTask(tasksVO));
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		statusVO.setMessages(messages);
		return statusVO;
	}

	@ApiMethod(name = "getOrganization", path = "getOrganization", httpMethod = ApiMethod.HttpMethod.POST)
	public Map<String, Object> getOrganization(Map<String, String> request) throws Exception {
		Map<String, Object> response = new HashMap<String, Object>();
		List<Group> orgs = new ArrayList<Group>();
		response.put("success", false);
		response.put("data", orgs);
		try {
			log.info("Request for getting Orgs :" + request);
			if (request != null && request.get("userId") != null) {
				orgs = WatchRxFactory.getClinicianService()
						.getGroupsAndRolesForUser(Long.parseLong(request.get("userId")));
				response.put("success", true);
				response.put("data", orgs);
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.warning("Exception while getting Orgs for UserID :" + request.get("userId"));
		}
		return response;
	}

	@ApiMethod(name = "zoomAccessToken", path = "zoomAccessToken", httpMethod = ApiMethod.HttpMethod.POST)
	public Map<String, Object> getZoomToken(Map<String, Object> request) throws Exception {
		Map<String, Object> response = new HashMap<String, Object>();
		response.put("success", false);
		try {
			if (request != null && request.get("userId") != null) {
				response = WatchRxFactory.getZoomCallService().getVideoCallAccessTokenForCaregiverApp(request);
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.warning("Exception while getting Orgs for UserID :" + request.get("userId"));
		}
		return response;
	}

	@ApiMethod(name = "getTodayTasks", path = "getTodayTasks", httpMethod = ApiMethod.HttpMethod.POST)
	public Map<String, Object> getTodayTasks(Map<String, Object> request) throws Exception {
		Map<String, Object> response = new HashMap<String, Object>();
		response.put("success", false);
		try {
			log.info("Request for getTodayTasks:" + request);
			if (request != null && request.get("userId") != null) {
				Long userId = Long.valueOf((String) request.get("userId"));
				Long orgId = Long.valueOf((String) request.get("orgId"));

				String date = (String) request.get("date");
				String timeZone = (String) request.get("timezone");
				List<WatchRxCalendarTaskVo> data = WatchRxFactory.getClinicianService().getTodayCalendarTasksV2(orgId,
						userId, date, timeZone);
				if (data != null && !data.isEmpty()) {
					response.put("data", data);
					response.put("success", true);
				} else {
					response.put("data", new ArrayList<>());
					response.put("success", false);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	@ApiMethod(name = "createCalendarTask", path = "createCalendarTask", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO createCalendarTask(WatchRxCalendarTaskVo request) throws Exception {
		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(false);
		try {
			log.info("Request for createCalendarTask: {}" + request);
			statusVO = WatchRxFactory.getClinicianService().createCalendarTask(request);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return statusVO;
	}

	@ApiMethod(name = "getCalendarTasks", path = "getCalendarTasks", httpMethod = ApiMethod.HttpMethod.POST)
	public Map<String, Object> getCalendarTasks(Map<String, Object> request) throws Exception {
		Map<String, Object> response = new HashMap<String, Object>();
		response.put("success", false);
		try {
			log.info("Request for getCalendarTasks:  " + request);
			if (request != null && request.get("userId") != null) {
				Long userId = Long.valueOf((String) request.get("userId"));
				Long orgId = Long.valueOf((String) request.get("orgId"));

				List<WatchRxCalendarTaskVo> data = WatchRxFactory.getClinicianService()
						.getAllCalendarTaskByUserId(orgId, userId);
				if (data != null && !data.isEmpty()) {
					response.put("data", data);
					response.put("success", true);
				} else {
					response.put("data", new ArrayList<>());
					response.put("success", false);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			response.put("data", new ArrayList<>());
			response.put("success", false);
		}
		return response;
	}

	@ApiMethod(name = "editCalendarTask", path = "editCalendarTask", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO editCalendarTask(WatchRxCalendarTaskVo request) throws Exception {
		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(false);
		try {
			log.info("Request for editCalendarTask:  " + request);
			statusVO = WatchRxFactory.getClinicianService().editCalendarTask(request);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return statusVO;
	}

	@ApiMethod(name = "deleteCalendarTask", path = "deleteCalendarTask", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO deleteCalendarTask(WatchRxCalendarTaskVo request) throws Exception {
		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(false);
		try {
			log.info("Request for editCalendarTask:  " + request);
			statusVO = WatchRxFactory.getClinicianService().deleteCalendarTask(request.getId());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return statusVO;
	}

	@ApiMethod(name = "monthlySummary", path = "monthlySummary", httpMethod = ApiMethod.HttpMethod.POST)
	public Map<String, Object> monthlySummary(Map<String, Object> request) throws Exception {
		Map<String, Object> response = new HashMap<String, Object>();
		response.put("success", false);
		try {
			Long userId = Long.valueOf((String) request.get("userId"));
			Long roleType = Long.valueOf((String) request.get("roleType"));
			Long clinicianId = WatchRxFactory.getClinicianService().getClinicianIdForUserId(userId, roleType);
			Long orgId = Long.valueOf((String) request.get("orgId"));

			String startDate = (String) request.get("startDate");
			String endDate = (String) request.get("endDate");

			Map<String, Object> patientStats = WatchRxFactory.getPatientService().dashboardPatientCount(orgId,
					clinicianId, roleType.intValue(), startDate, endDate);
			response.put("success", true);
			response.put("patientStats", patientStats);

			Map<String, Object> minsStats = WatchRxFactory.getPatientService().dashboardPatientGraph(orgId, clinicianId,
					roleType.intValue(), startDate, endDate);
			response.put("minsStats", minsStats);

		} catch (Exception e) {
			response.put("success", false);
			response.put("message", e.getMessage());
		}
		return response;
	}

	@ApiMethod(name = "patientSummary", path = "patientSummary", httpMethod = ApiMethod.HttpMethod.POST)
	public PatientVO patientSummary(Map<String, Object> request) {
		PatientVO patient = new PatientVO();
		try {
			Long patientId = Long.valueOf(String.valueOf(request.get("patientId")));

			PatientService patientService = WatchRxFactory.getPatientService();
			WatchService watchService = WatchRxFactory.getWatchService();

			patient = patientService.getPatient(patientId);
			patient.setMrn(patient.getMrn() == null ? String.valueOf(patientId) : patient.getMrn());

			List<WatchVO> deviceList = watchService.getAllMedicalDevicesForPatient(patientId);
			List<PatientWatchCaregiverInfoVO> assignedDevices = new ArrayList<>();

			for (WatchVO watch : deviceList) {
				PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
				pwcInfo.setimgPath(watch.getPicPath());

				if (watch.getWatchMake() != null) {
					String assignedWatch = watch.getWatchMake();
					if (watch.getWatchModel() != null) {
						assignedWatch += " " + watch.getWatchModel();
					}
					pwcInfo.setAssignedWatch(assignedWatch);
				}

				pwcInfo.setWatchId(String.valueOf(watch.getWatchId()));
				pwcInfo.setImei(watch.getImeiNumber());
				pwcInfo.setWatchPhoneNumber(watch.getPhoneNumber());
				pwcInfo.setWatchPlan("Basic");
				pwcInfo.setIsActive(watch.getIsActive());
				pwcInfo.setDeviceType(watch.getDeviceType());
				pwcInfo.setDeviceMeasures(watch.getDeviceMeasures());
				pwcInfo.setDeviceMeasuresArray(watch.getDeviceMeasuresArray());
				pwcInfo.setDeviceName(watch.getDeviceName());

				assignedDevices.add(pwcInfo);
			}
			patient.setAssignedDevices(assignedDevices);
			patient.setThresholdData(patient.getThresholdData());
			patient.setThresholdMinMax(patient.getThresholdMinMax());

			clearSensitiveOrHeavyFields(patient);
		} catch (Exception e) {
			patient = new PatientVO();
		}
		return patient;
	}

	private void clearSensitiveOrHeavyFields(PatientVO patient) {
		patient.setCptVOList(null);
		patient.setAlerts(null);
		patient.setBillingStatus(null);
		patient.setThresholdData(null);
		patient.setThresholdMinMax(null);
		patient.setAddress(null);
		patient.setCreatedDate(null);
		patient.setUpdatedDate(null);
		patient.setFileModified(false);

		// Clear meal and sleep timing
		patient.setEarlyMorningHour(null);
		patient.setEarlyMorningMin(null);
		patient.setBedHour(null);
		patient.setBedMin(null);
		patient.setBreakFastHour(null);
		patient.setBreakFastMin(null);
		patient.setLunchHour(null);
		patient.setLunchMin(null);
		patient.setNoonSnackHour(null);
		patient.setNoonSnackMin(null);
		patient.setDinnerHour(null);
		patient.setDinnerMin(null);
	}

	@ApiMethod(name = "saveEncounter", path = "saveEncounter", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO saveEncounter(EncounterNewVO request) throws Exception {
		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(false);
		try {
			log.info("Request for editCalendarTask:  " + request);
			WatchRxFactory.getPatientService().saveEncountersNew(request);
			statusVO.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			statusVO.setMessages(messages);
		} catch (Exception e) {
			statusVO.setSuccess(false);
			String messages[] = new String[1];
			messages[0] = e.getMessage();
			statusVO.setMessages(messages);
		}
		return statusVO;
	}

	@ApiMethod(name = "getEncounters", path = "getEncounters", httpMethod = ApiMethod.HttpMethod.POST)
	public Map<String, Object> getEncounters(Map<String, Object> request) throws Exception {
		Map<String, Object> response = new HashMap<String, Object>();
		response.put("success", false);
		try {
			log.info("Request for getEncounters:  " + request);
			if (request != null && request.get("userId") != null) {
				Long patientId = Long.valueOf((String) request.get("patientId"));
				String startDate = (String) request.get("startDate");
				String endDate = (String) request.get("endDate");
				String program = (String) request.get("program");

				PatientVO patientVO = new PatientVO();
				patientVO.setPatientId(patientId);

				List<EncounterNewVO> encounterNewList = WatchRxFactory.getPatientService()
						.getEncounterForCaregiverApp(patientVO, 0, 100, startDate, endDate, program);

				if (encounterNewList != null && !encounterNewList.isEmpty()) {
					response.put("data", encounterNewList);
					response.put("success", true);
				} else {
					response.put("data", new ArrayList<>());
					response.put("success", true);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			response.put("data", new ArrayList<>());
			response.put("success", true);
		}
		return response;
	}

	@ApiMethod(name = "deleteEncounter", path = "deleteEncounter", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO deleteEncounter(EncounterNewVO request) throws Exception {
		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(false);
		try {
			log.info("Request for editCalendarTask:  " + request);
			statusVO = WatchRxFactory.getPatientService().deleteEncounterNew(request);
		} catch (Exception e) {
			statusVO.setSuccess(false);
			String messages[] = new String[1];
			messages[0] = e.getMessage();
			statusVO.setMessages(messages);
		}
		return statusVO;
	}

	@ApiMethod(name = "saveNewCustomAlerts", path = "saveNewCustomAlerts", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO saveNewCustomAlerts(CustomAlertVO customAlertsInfoVO) throws Exception {
		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(false);
		try {
			List<CustomAlertsForPatientVO> alertsForPatientVOs = customAlertsInfoVO.getCustomAlerts();
			PatientVO patient = new PatientVO();
			patient.setPatientId(customAlertsInfoVO.getPatientId());

			CustomAlertVO customAlertsForPatientVO = new CustomAlertVO();
			customAlertsForPatientVO.setCustomAlerts(alertsForPatientVOs);

			WatchRxFactory.getPatientService().saveCustomAlerts(patient, customAlertsForPatientVO);
			sendCustomAlertInfoByPatientId(customAlertsInfoVO.getPatientId());
			statusVO.setSuccess(true);
		} catch (Exception e) {
			statusVO.setSuccess(false);
			String messages[] = new String[1];
			messages[0] = e.getMessage();
			statusVO.setMessages(messages);
		}
		return statusVO;
	}

	@ApiMethod(name = "deleteAnItem", path = "deleteAnItem", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO deleteAnItem(HttpServletRequest request, @RequestBody DeleteAnRecordVO itemVO) throws Exception {
		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(false);
		try {
			String itemName = itemVO.getItemName();
			long itemId = itemVO.getItemId();

			WatchRxFactory.getPatientService().deleteById(itemName, itemId, itemVO.getPatientId());

			StatusVO status = new StatusVO();
			status.setSuccess(true);

		} catch (Exception e) {
			statusVO.setSuccess(false);
			String messages[] = new String[1];
			messages[0] = e.getMessage();
			statusVO.setMessages(messages);
		}
		return statusVO;
	}

	@ApiMethod(name = "saveEditedCustomAlert", path = "saveEditedCustomAlert", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO saveEditedCustomAlert(HttpServletRequest request,
			@RequestBody CustomAlertsForPatientVO customAlertsForPatientVO) throws Exception {
		StatusVO resp = new StatusVO();
		resp.setSuccess(WatchRxFactory.getPatientService().saveExistingCustomAlert(customAlertsForPatientVO));
		sendCustomAlertInfoByPatientId(Long.valueOf(customAlertsForPatientVO.getPatientId()));
		return resp;
	}

	private void sendCustomAlertInfoByPatientId(Long patientId) {

		log.info("Sending send Custom Alert info by PatientId info GCM::");
		CareGiverTest sdetails = new CareGiverTest();
		RegisterWatch watch = new RegisterWatch();
		PatientVO pdvo = WatchRxFactory.getPatientService().getPatient(patientId);
		log.info("Sending medication info patient id::" + patientId);
		if (pdvo != null && pdvo.getWatch() != null) {
			String imeiNo = pdvo.getWatch().getImeiNumber();
			if (imeiNo != null) {
				log.info("Sending send Custom Alert  info by PatientId info GCM IMEI NO::" + imeiNo);
				watch.setImeiNo(imeiNo);

				PatientDetails pd = WatchRxFactory.getPatientService().getPatientDetailsByImei(watch);

				PatientInfo gcmId = WatchRxFactory.getPatientService().getPatientById(patientId);
				log.info("Sending medication info GCM ID::" + gcmId);
				if (gcmId.getGcmId() != null && gcmId.getGcmId().trim().length() > 0) {
					JsonObject jo = new JsonObject();
					jo.addProperty("messageType", "UpdatedMedicationInfo");
					jo.addProperty("patientDetails", pd.toString());
					log.info("Sending send Custom Alert  info by PatientId info GCM ID details::" + pd.toString());
					Gson gson = new Gson();
					String jsonStr = gson.toJson(jo);
					try {
						sdetails.sendMessageToPatient("message", jsonStr, gcmId.getGcmId(), gcmId.getPlatform());
						log.info("Sending send Custom Alert  info by PatientId info GCM ID Send::");
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
		}
	}

	@ApiMethod(name = "sendChatMessage", path = "sendChatMessage", httpMethod = ApiMethod.HttpMethod.POST)
	public StatusVO sendChatMessage(HttpServletRequest request, @RequestBody ChatRequest chatRequest) throws Exception {
		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(false);
		try {
			String resp = WatchRxFactory.getPatientChatService().saveChatRequest(chatRequest, chatRequest.getUserId());
			statusVO.setSuccess(resp != null && resp.equalsIgnoreCase("SUCCESS") ? true : false);
		} catch (Exception e) {
			statusVO.setSuccess(false);
			String messages[] = new String[1];
			messages[0] = e.getMessage();
			statusVO.setMessages(messages);
		}
		return statusVO;
	}

	@ApiMethod(name = "getChatMessage", path = "getChatMessage", httpMethod = ApiMethod.HttpMethod.POST)
	public Map<String, Object> getChatMessage(ChatRequest chatRequest) throws Exception {
		Map<String, Object> response = new HashMap<String, Object>();
		response.put("success", false);
		try {
			log.info("Request for getChatMessage:  " + chatRequest);
			if (chatRequest != null && chatRequest.getPatientId() != null) {
				List<ChatResponse> messageList = WatchRxFactory.getPatientChatService().chatData(chatRequest);
				if (messageList != null && !messageList.isEmpty()) {
					response.put("data", messageList);
					response.put("success", true);
				} else {
					response.put("data", new ArrayList<>());
					response.put("success", true);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			response.put("data", new ArrayList<>());
			response.put("success", true);
		}
		return response;
	}

	@ApiMethod(name = "pushToken", path = "pushToken", httpMethod = ApiMethod.HttpMethod.POST)
	public VoiceCallModel getPushToken(HttpServletRequest request, Map<String, Object> patientDtl)
			throws JsonMappingException, JsonProcessingException {

		Long patientId = Long.valueOf((String) patientDtl.get("patientId"));

		return WatchRxFactory.getPatientService().getVoipTokenByPatientId(patientId);

	}

	@ApiMethod(name = "getPatientSummary", path = "getPatientSummary", httpMethod = ApiMethod.HttpMethod.POST)
	public Map<String, Object> getPatientSummary(Map<String, Object> request) {
		Map<String, Object> response = new HashMap<>();
		try {
			Long patientId = Long.valueOf(String.valueOf(request.get("patientId")));

			PatientService patientService = WatchRxFactory.getPatientService();
			WatchService watchService = WatchRxFactory.getWatchService();

			PatientVO patient = patientService.getPatient(patientId);
			patient.setMrn(patient.getMrn() == null ? String.valueOf(patientId) : patient.getMrn());

			List<WatchVO> deviceList = watchService.getAllMedicalDevicesForPatient(patientId);
			List<PatientWatchCaregiverInfoVO> assignedDevices = new ArrayList<>();

			for (WatchVO watch : deviceList) {
				PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
				pwcInfo.setimgPath(watch.getPicPath());

				if (watch.getWatchMake() != null) {
					String assignedWatch = watch.getWatchMake();
					if (watch.getWatchModel() != null) {
						assignedWatch += " " + watch.getWatchModel();
					}
					pwcInfo.setAssignedWatch(assignedWatch);
				}

				pwcInfo.setWatchId(String.valueOf(watch.getWatchId()));
				pwcInfo.setImei(watch.getImeiNumber());
				pwcInfo.setWatchPhoneNumber(watch.getPhoneNumber());
				pwcInfo.setWatchPlan("Basic");
				pwcInfo.setIsActive(watch.getIsActive());
				pwcInfo.setDeviceType(watch.getDeviceType());
				pwcInfo.setDeviceMeasures(watch.getDeviceMeasures());
				pwcInfo.setDeviceMeasuresArray(watch.getDeviceMeasuresArray());
				pwcInfo.setDeviceName(watch.getDeviceName());

				assignedDevices.add(pwcInfo);
			}
			patient.setAssignedDevices(assignedDevices);

			clearSensitiveOrHeavyFields(patient);

			Map<String, Object> vitalData = WatchRxFactory.getVitalsService().getLatest5VitalsForPatient(patientId);

			response.put("success", true);
			response.put("patient", patient);
			response.put("latestVitals", vitalData);

		} catch (Exception e) {
			log.severe("Error in getPatientSummary: " + e.getMessage());
			response.put("success", false);
			response.put("error", "Failed to retrieve patient summary: " + e.getMessage());
		}
		return response;
	}

	@ApiMethod(name = "clearNotification", path = "clearNotification", httpMethod = ApiMethod.HttpMethod.POST)
	public Map<String, Object> clearNotification(HttpServletRequest request, Map<String, Object> patientDtl)
			throws JsonMappingException, JsonProcessingException {
		Map<String, Object> response = new HashMap<String, Object>();
		response.put("success", false);
		try {
			Long patientId = Long.valueOf((String) patientDtl.get("patientId"));
			response.put("success", WatchRxFactory.getPatientChatService().clearUnreadMessageByPatientId(patientId));
		} catch (Exception e) {
			response.put("success", false);
		}
		return response;
	}

	@ApiMethod(name = "unReadMessages", path = "unReadMessages", httpMethod = ApiMethod.HttpMethod.POST)
	public Map<String, Object> unReadMessages(HttpServletRequest request, Map<String, Object> patientDtl)
			throws JsonMappingException, JsonProcessingException {
		Map<String, Object> response = new HashMap<String, Object>();
		response.put("success", false);
		try {
			Long userId = Long.valueOf((String) patientDtl.get("userId"));
			Long orgId = Long.valueOf((String) patientDtl.get("orgId"));
			if (userId != null && orgId != null) {
				List<ChatNotification> messagesList = WatchRxFactory.getPatientChatService()
						.getUnReadMessagesPatient(userId, orgId);
				response.put("data", messagesList);
				response.put("success", true);
			}
		} catch (Exception e) {
			response.put("success", false);
		}
		return response;
	}

	@ApiMethod(name = "getpatientsfornurseV2", path = "getpatientsfornurseV2", httpMethod = ApiMethod.HttpMethod.POST)
	public PatientGCInfos getPatientsForNurseV2(CareGiverDetails request) {
		log.info("Care Giver Service invoked the method getPatientsForNurse");
		PatientGCInfos patientGCInfos = new PatientGCInfos();
		Long roleTypeId = 5l;
		if (request.getRoleType() != null && request.getRoleType().equalsIgnoreCase("physician")) {
			roleTypeId = 3l;
		}
		patientGCInfos = WatchRxFactory.getPatientService().getAllPatientsByOrgIdAndUserId(request.getOrgId(),
				Long.valueOf(request.getCareGiverId()), roleTypeId, request.getPageNumber(), request.getPageSize());
		patientGCInfos.setResponseMessage("Operationsuccessful");
		return patientGCInfos;
	}
}
