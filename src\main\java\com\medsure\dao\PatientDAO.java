/**
 * 
 */
package com.medsure.dao;

import java.math.BigInteger;
import java.util.List;
import java.util.Set;

import javax.validation.constraints.NotBlank;

import com.medsure.model.WatchrxClinician;
import com.medsure.model.WatchrxPatient;
import com.medsure.ui.entity.server.PatientVO;
import com.medsure.ui.entity.server.adminreport.EncounterReport;
import com.medsure.ui.entity.server.adminreport.VitalReport;

/**
 * <AUTHOR>
 *
 */
public interface PatientDAO extends BaseDAO<WatchrxPatient> {

	public int updatePatientVisitVerifyCode(Long pId, Long vCode);

	public Long getVisitVerificationCodeByPatientId(Long pId);

	public List<Object[]> patientCountYearCaseManager(Long clinicianId);

	public List<Object[]> patientCountYearPhysician(Long physicianId);

	public List<Object[]> patientCountWeekPhysician(Long physicianId);

	public List<Object[]> patientCountMonthPhysician(Long physicianId);

	public List<Object[]> patientCountWeekCaseManager(Long clinicianId);

	public List<Object[]> patientCountMonthCaseManager(Long clinicianId);

	public List<WatchrxPatient> patientsPhysicianCaseManagerPaginated(Long physicianId, Long caseManagerId,
			Integer index, Integer pageSize);

	public Long patientCountPhysicianCaseManager(Long physicianId, Long caseManagerId);

	public Long patientTotalCountMonthPhysician(Long physicianId);

	public Long patientTotalCountMonthCaseManager(Long clinicianId);

	public Long patientTotalCountCaseManager(Long clinicianId);

	public List<WatchrxPatient> getPatientsByPhysicianByName(String name, Long physicianId);

	public List<String> getCptCodeForPatient(Long patientId);

	public List<WatchrxClinician> getCaseManagerForPatientId(Long patientId);

	// Added by KV for Pagination
	public List<PatientVO> getPhysicianPatientsByUserId(Long userId, Integer index, Integer pageSize);

	// Added by KV for Pagination
	public List<PatientVO> getClinicianPatientsByUserId(Long userId, Integer index, Integer pageSize);

	public List<PatientVO> getAllPatientSummary(Integer index, Integer pageSize);

	public List<Object[]> getAllPatientAddressBilling(Integer index, Integer pageSize, String status);

	public List<Object[]> getPhysicianPatientsAddressBilling(Long userId, Integer index, Integer pageSize,
			String status);

	public List<Object[]> getClinicianPatientAddressBilling(List<Long> userId, Integer index, Integer pageSize,
			String status);

	public String getTimeZoneByPatientId(Long pId);

	List<WatchrxPatient> authenticatePatient(String email, String otp);

	List<WatchrxPatient> authorizePatient(String email, String password);

	List<WatchrxPatient> validatePatientOTPStatus(String email, String otpStataus);

	List<WatchrxPatient> findByMRN(String value);

	List<WatchrxPatient> findByDeviceTypeAndPlatform(String devType, String platform);

	public List<PatientVO> getCareGiverPatientsByUserId(Long userId, Integer index, Integer pageSize);

	public WatchrxPatient doPatientLogin(String email, String password);

	Long patientTotalCountCaseManager(List<Long> clinicianId);

	List<PatientVO> getClinicianPatientsByUserIds(List<Long> userId, Integer index, Integer pageSize);

	List<PatientVO> getPhysicianPatientsByUserIds(List<Long> userId, Integer index, Integer pageSize);

	public List<Object[]> getPhysicianPatientsAddressBilling(List<Long> userId, Integer index, Integer pageSize,
			String status);

	public List<WatchrxPatient> getAllPatientsForOrg(List<Long> userId, Long roleType);

	public List<WatchrxPatient> getAllPatientsForOrgV1(Long orgId, Long userId);

	public List<WatchrxPatient> getAllPatientsForOrg(Set<Long> userId, Long roleType);

	public Long getAllPatientsCountByOrgId(Long orgId, Long userId);

	public Long getAllPatientsCountByOrgIdNoStatus(Long orgId, Long userId);

	public List<Object[]> getAllPatientsBillingAdressInfo(Long orgId, Integer index, Integer pageSize, Long userId);

	public Long patientTotalCountMonthOrgId(Long orgId);

	public List<PatientVO> getAllPatientsByOrgId(Long orgId, Integer index, Integer pageSize, Long userId);

	public List<WatchrxPatient> getAllPatientsByOrgId(Long orgId);

	public Long getPatientsCountByOrgIdAndSearch(Long orgId, String search, Long userId);

	public List<Object[]> getAllPatientsBillingAdressInfoAndSearch(Long orgId, Integer index, Integer pageSize,
			String search);

	public int updateStatus(Long patientId, @NotBlank String status);

	public Long getAllCountByStatus(String status);

	public Long getAllCountByStatusOrgIdAndClinicianId(String status, Long orgId, Long clinicianId);

	public Long getAllPatientsCountByOrgIdAndStatus(Long orgId, String status, Long clinicianId);

	public List<PatientVO> getAllPatientSummaryByStatus(Integer index, Integer pageSize, String status);

	public List<PatientVO> getAllPatientsByOrgIdAndStatus(Long orgId, Integer index, Integer pageSize, String status,
			Long userId);

	public List<Object[]> dashboardPatientCount(Long orgId, Long clinicianId, Integer role);

	public List<BigInteger> activePatientsForOrgAndClinician(Long orgId, Long clinicianId, Integer role);

	public List<WatchrxPatient> getAllPatientsByOrgIdAndClinicianId(Long orgId, Long clinicianId);

	public List<Object[]> getAllPatientsBillingAdressInfoV3(Long orgId, Integer index, Integer pageSize);

	public Long getAllPatientsCountByOrgIdV3(Long orgId, Long careManagerId, Long roleType);

	public Long getAllCountByStatusOrgIdAndForPhysician(String status, Long orgId);

	public Long getAllCountByStatusOrgIdAndForPhysicianWithFilters(String status, Long orgId, Long clinicianId,
			Long phyId);

	public WatchrxPatient findByPatientId(Long patientId);

	public WatchrxPatient findByPhoneNumber(String phoneNum);

	public List<WatchrxPatient> getAllActivePatientsByOrgId(Long orgId);

	public Long getPatientsCountByPatientId(Long patientId);

	public List<Long> getActivePatientIdsByOrgId(Long orgId);

	public int updateStatusByOrg(List<Long> patientIds);

	List<PatientVO> getAllPatientsByOrgIdForPhysician(Long orgId, Integer index, Integer pageSize, String status,
			Long userId);

	List<PatientVO> getAllPatientsByOrgIdForPhysicianFilters(Long orgId, Integer index, Integer pageSize, String status,
			Long userId, Long phyId);

	Long patientCountPhysician(Long physicianId, Long orgId);

	List<EncounterReport> adminEncounterReports(Long orgId, List<Long> userId, Long duration);

	List<VitalReport> adminVitalReports(Long orgId, List<Long> userId, Long duration);

	List<VitalReport> admin16DayReports(Long orgId, List<Long> userId, Long duration);

	List<WatchrxPatient> findByEmailOrPhoneNo(String eamilOrPhone);

	List<PatientVO> getAllPatientsByOrgIdWithStatusN(Long orgId, Integer index, Integer pageSize, String status);

	public Long getPatientCountByBasicInfo(String firstName, String lastName, String dob, String address);

	public List<WatchrxPatient> getPatientsByOrgIdAndPhysician(Long orgId, Long physicianId, Integer index,
			Integer pageSize);

	public Long getPatientsByOrgIdAndPhysicianCount(Long orgId, Long physicianId);

	public List<WatchrxPatient> getAllPatientsByOrgIdAndClinicianIdV2(Long orgId, Long clinicianId, Integer index,
			Integer size);
}
