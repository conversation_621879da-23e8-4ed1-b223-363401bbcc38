package com.medsure.ui.service.server;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.mail.MessagingException;
import javax.mail.internet.AddressException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jxls.template.SimpleExporter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.appengine.api.appidentity.AppIdentityService;
import com.google.appengine.api.appidentity.AppIdentityServiceFactory;
import com.google.appengine.tools.cloudstorage.GcsFileOptions;
import com.google.appengine.tools.cloudstorage.GcsFilename;
import com.google.appengine.tools.cloudstorage.GcsOutputChannel;
import com.google.appengine.tools.cloudstorage.GcsService;
import com.google.appengine.tools.cloudstorage.GcsServiceFactory;
import com.google.appengine.tools.cloudstorage.RetryParams;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Chunk;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.text.pdf.draw.VerticalPositionMark;
import com.medsure.common.Constants;
import com.medsure.config.ScheduledForMessage;
import com.medsure.dao.ClinicianDAO;
import com.medsure.dao.EncountersNewDAO;
import com.medsure.dao.GroupDAO;
import com.medsure.dao.GroupUserAssignmentDAO;
import com.medsure.dao.PatientAlertDAO;
import com.medsure.dao.PatientDAO;
import com.medsure.dao.PhysicianDAO;
import com.medsure.dao.ScreeningConditionDAO;
import com.medsure.dao.EncounterDraftsDAO;
import com.medsure.exception.WatchRxException;
import com.medsure.exception.WatchRxExceptionCodes;
import com.medsure.exception.WatchRxServiceException;
import com.medsure.external.ExternalAPIs.AudioRequest;
import com.medsure.factory.WatchRxFactory;
import com.medsure.model.WatchRxEncountersNew;
import com.medsure.model.WatchRxPatientDiares;
import com.medsure.model.WatchrxGroup;
import com.medsure.model.WatchrxGroupUserAssignment;
import com.medsure.model.WatchrxPatient;
import com.medsure.model.WatchrxPatientAlert;
import com.medsure.model.WatchRxEncounterDrafts;
import com.medsure.service.ClinicianService;
import com.medsure.service.DataTransformService;
import com.medsure.service.DiyvaService;
import com.medsure.service.GPSService;
import com.medsure.service.PatientCarePlanService;
import com.medsure.service.PatientService;
import com.medsure.service.UserService;
import com.medsure.test.caregiver.CareGiverTest;
import com.medsure.ui.entity.caregiver.request.GetAlerts;
import com.medsure.ui.entity.caregiver.request.MedicationIds;
import com.medsure.ui.entity.caregiver.response.PatientCGInfo;
import com.medsure.ui.entity.caregiver.response.PatientGCInfos;
import com.medsure.ui.entity.caregiver.response.PatientInfo;
import com.medsure.ui.entity.diyva.DiyvaDialog;
import com.medsure.ui.entity.patient.common.MedicationInfo;
import com.medsure.ui.entity.patient.common.ScheduledTextMessageInfo;
import com.medsure.ui.entity.patient.request.MedicationAlert;
import com.medsure.ui.entity.patient.request.RegisterWatch;
import com.medsure.ui.entity.patient.response.HeartRatesResponse;
import com.medsure.ui.entity.patient.response.PatientDetails;
import com.medsure.ui.entity.server.AddressVO;
import com.medsure.ui.entity.server.AlertRequestVO;
import com.medsure.ui.entity.server.AlertResponseVO;
import com.medsure.ui.entity.server.AlertVO;
import com.medsure.ui.entity.server.AlertsCountRequestVO;
import com.medsure.ui.entity.server.AllChronicConditionsVO;
import com.medsure.ui.entity.server.BaseListVO;
import com.medsure.ui.entity.server.BaseListWithCountVO;
import com.medsure.ui.entity.server.BillingDetails;
import com.medsure.ui.entity.server.CareGiverMinimalVO;
import com.medsure.ui.entity.server.CaregiverPatientsListVO;
import com.medsure.ui.entity.server.ClinicianVO;
import com.medsure.ui.entity.server.CollectVitalsFromMedicalDeviceConfigurationVO;
import com.medsure.ui.entity.server.CollectedHeartRateCountListVO;
import com.medsure.ui.entity.server.CollectedHeartRateXlsVO;
import com.medsure.ui.entity.server.CptCodeVO;
import com.medsure.ui.entity.server.CptVO;
import com.medsure.ui.entity.server.CumulativeMinutesListVO;
import com.medsure.ui.entity.server.CustomAlertVO;
import com.medsure.ui.entity.server.CustomAlertsForPatientVO;
import com.medsure.ui.entity.server.DataFilterResponse;
import com.medsure.ui.entity.server.DeviceTypeVO;
import com.medsure.ui.entity.server.EncounterNewListVO;
import com.medsure.ui.entity.server.EncounterNewVO;
import com.medsure.ui.entity.server.GPSInfoVO;
import com.medsure.ui.entity.server.HeartRateInfo;
import com.medsure.ui.entity.server.HeartRateRequestVO;
import com.medsure.ui.entity.server.InsuranceVO;
import com.medsure.ui.entity.server.LoginStatusVO;
import com.medsure.ui.entity.server.NeedAttentionVO;
import com.medsure.ui.entity.server.OTPDetails;
import com.medsure.ui.entity.server.PatientAlertInfoVO;
import com.medsure.ui.entity.server.PatientAlertResponse;
import com.medsure.ui.entity.server.PatientBillingStatusVO;
import com.medsure.ui.entity.server.PatientCarePlanVO;
import com.medsure.ui.entity.server.PatientCriticalAlertsCountVO;
import com.medsure.ui.entity.server.PatientDiaryRequestVO;
import com.medsure.ui.entity.server.PatientDiaryResponseVO;
import com.medsure.ui.entity.server.PatientDiaryResponseVOList;
import com.medsure.ui.entity.server.PatientDocumentationListVO;
import com.medsure.ui.entity.server.PatientDocumentationVO;
import com.medsure.ui.entity.server.PatientListVO;
import com.medsure.ui.entity.server.PatientMinimal2VO;
import com.medsure.ui.entity.server.PatientPrescriptionVO;
import com.medsure.ui.entity.server.PatientProgramRequestVO;
import com.medsure.ui.entity.server.PatientTextMessageVO;
import com.medsure.ui.entity.server.PatientVO;
import com.medsure.ui.entity.server.PatientWatchCaregiverInfoVO;
import com.medsure.ui.entity.server.PatientWatchCaregiverListVO;
import com.medsure.ui.entity.server.PatientsAssignedClinicianListVO;
import com.medsure.ui.entity.server.PatientsAssignedClinicianWCListVO;
import com.medsure.ui.entity.server.PedometerConfigurationVO;
import com.medsure.ui.entity.server.PedometerReadingVO;
import com.medsure.ui.entity.server.PhoneCommunicationListVO;
import com.medsure.ui.entity.server.PhoneCommunicationVO;
import com.medsure.ui.entity.server.PhoneCommunicationXlsVO;
import com.medsure.ui.entity.server.RpmCcmReportVO;
import com.medsure.ui.entity.server.ScheduleMessageVO;
import com.medsure.ui.entity.server.ScheduleTextMessageVO;
import com.medsure.ui.entity.server.ScreeningConditionListVO;
import com.medsure.ui.entity.server.ScreeningConditionVO;
import com.medsure.ui.entity.server.ScreeningListVO;
import com.medsure.ui.entity.server.ScreeningRequestVO;
import com.medsure.ui.entity.server.ScreeningValuesListVO;
import com.medsure.ui.entity.server.SecCGInviteInfo;
import com.medsure.ui.entity.server.SecCGInviteReqVO;
import com.medsure.ui.entity.server.SleepMonitoringConfigurationVO;
import com.medsure.ui.entity.server.SleepMonitoringVO;
import com.medsure.ui.entity.server.StatusVO;
import com.medsure.ui.entity.server.TargetListVO;
import com.medsure.ui.entity.server.TargetsVO;
import com.medsure.ui.entity.server.TextMessageAlertsCountVO;
import com.medsure.ui.entity.server.TextMessageResponseListVO;
import com.medsure.ui.entity.server.ThresholdConfigVO;
import com.medsure.ui.entity.server.ThresholdConfigurationVO;
import com.medsure.ui.entity.server.UserVO;
import com.medsure.ui.entity.server.ValidationCountVO;
import com.medsure.ui.entity.server.ValidationListVO;
import com.medsure.ui.entity.server.ValidationVO;
import com.medsure.ui.entity.server.ViewTextMessage;
import com.medsure.ui.entity.server.ViewTextMessageResponse;
import com.medsure.ui.entity.server.VitalListVO;
import com.medsure.ui.entity.server.VitalReadingVO;
import com.medsure.ui.entity.server.VitalRequestListVO;
import com.medsure.ui.entity.server.VitalScheduleRequestVO;
import com.medsure.ui.entity.server.VitalScheduleVO;
import com.medsure.ui.entity.server.VitalVO;
import com.medsure.ui.entity.server.VitalWeeklyGraphListVO;
import com.medsure.ui.entity.server.VitalsCountGraphListVO;
import com.medsure.ui.entity.server.VitalsCountListVO;
import com.medsure.ui.entity.server.WatchVO;
import com.medsure.ui.entity.server.DraftVO;
import com.medsure.ui.entity.server.DraftListVO;
import com.medsure.ui.entity.server.alertdashboard.MedicationAlertListResponse;
import com.medsure.ui.entity.server.alertdashboard.MedicationAlertRequestVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestAddressVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestBasicInformationVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestChronicConditionsListVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestChronicConditionsVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestCptDataVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestDailyScheduleVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestEmergencyContactListVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestEmergencyContactVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestInsuranceVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestMedicationListVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestMedicationVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientRequestVO;
import com.medsure.ui.entity.server.createpatientflow.CreatePatientResponseVO;
import com.medsure.ui.entity.server.createpatientflow.DeleteAnRecordVO;
import com.medsure.ui.entity.server.createpatientflow.MedicationImageExistenceVO;
import com.medsure.ui.entity.server.createpatientflow.SaveMedicationImageRequestVO;
import com.medsure.ui.entity.server.createpatientflow.SavePatientImageRequestVO;
import com.medsure.ui.entity.server.createpatientflow.UpdatePatientEmergencyContactsVO;
import com.medsure.ui.entity.server.createpatientflow.updateMedicationRequestVO;
import com.medsure.ui.entity.server.editpatientflow.CreatePrescriptionRequestVO;
import com.medsure.ui.entity.server.editpatientflow.CreatePrescriptionResponseVO;
import com.medsure.ui.entity.server.editpatientflow.GetPrescriptionsResponseVO;
import com.medsure.ui.entity.server.editpatientflow.PrescriptionRequestVO;
import com.medsure.ui.entity.server.editpatientflow.PrescriptionVO;
import com.medsure.ui.entity.server.medications.MedicationSummaryVO;
import com.medsure.ui.entity.server.patientsaveedit.CreatePatientBasicInformationResponseVO;
import com.medsure.ui.entity.server.patientsaveedit.SavePatientInfoResponseVO;
import com.medsure.ui.util.JsonUtils;
import com.medsure.ui.util.WatchRxUtils;
import com.medsure.util.AESUtil;
import com.medsure.util.DropdownUtils;
import com.medsure.util.GeoResponse;
import com.medsure.util.SendMail;

@RestController
@RequestMapping("/service/patient")
public class PatientController {

	private static Logger logger = Logger.getLogger(PatientController.class);

	@Autowired
	UserService userService;

	@Autowired
	PatientService patientService;

	@Autowired
	SendMail email;

	@Autowired
	ClinicianService clinicianService;

	@Autowired
	GPSService gpsService;

	@Autowired
	PatientDAO patientDAO;

	@Autowired
	ClinicianDAO clinicianDAO;

	@Autowired
	PatientAlertDAO patientAlertDAO;

	@Autowired
	PatientCarePlanService patientCarePlanService;

	@Autowired
	DiyvaService dService;

	@Autowired
	DataTransformService dataTransformService;

	@Autowired
	private GroupUserAssignmentDAO grpUsrDao;

	@Autowired
	private GroupDAO groupDAO;

	@Autowired
	EncountersNewDAO encountersNewDAO;

	@Autowired
	ScreeningConditionDAO screeningConditionDAO;

	@Autowired
	PhysicianDAO physicianDAO;

	@Autowired
	EncounterDraftsDAO encounterDraftsDAO;

	@RequestMapping(value = "/patientSummary", method = RequestMethod.GET)
	public @ResponseBody PatientListVO getPatientSummary(HttpServletRequest request) throws Exception {
		logger.info("inside getPatientSummary:::: ");
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<PatientVO> patientList = patientService.getPatientList(user);
		PatientListVO patientsVO = new PatientListVO();
		patientsVO.setPatients(patientList);

		// ModelAndView mav = new ModelAndView();
		// mav.addObject("patientList", patientList);
		// mav.setViewName("patientSummary");

		return patientsVO;
	}

	@RequestMapping(value = "/patientSummaryInfo", method = RequestMethod.GET)
	public @ResponseBody PatientWatchCaregiverListVO getPatientSummaryInfo(HttpServletRequest request)
			throws Exception {
		PatientWatchCaregiverListVO patientsInfo = new PatientWatchCaregiverListVO();
		try {
			UserVO user = new ObjectMapper()
					.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
			List<PatientVO> patientList = new ArrayList<>();
			if (user == null) {
				patientsInfo.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				patientsInfo.setMessages(messages);
				patientsInfo.setSuccess(false);
				return patientsInfo;
			}
			Set<Long> userId = getUsersByOrg(user.getUserId());
			for (Long uid : userId) {
				Long nurseId = WatchRxFactory.getPatientService().getClinicianByUserId(uid);
				if (nurseId != null) {
					PatientListVO parentList = patientService.getParentsByCaregiver(nurseId);
					patientList.addAll(parentList.getPatients());
				}
			}
			logger.info("pat list" + patientList.size());
			List<PatientWatchCaregiverInfoVO> data = new ArrayList<>();

			String msg[] = new String[1];
			for (PatientVO patient : patientList) {
				System.out.println("Inside patient list");
				logger.info(" patient id" + patient.getPatientId());
				PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
				List<ClinicianVO> clinicianList = new ArrayList<>();
				clinicianList = clinicianService.getCaseManagersByPatient(patient.getPatientId());
				String names = new String();

				if (clinicianList.size() > 0 && clinicianList.size() == 1) {
					pwcInfo.setCaregiverName(
							clinicianList.get(0).getFirstName() + " " + clinicianList.get(0).getLastName());
				} else {
					for (int i = 0; i < clinicianList.size(); i++) {
						if (i == 0) {
							names = clinicianList.get(i).getFirstName() + " " + clinicianList.get(0).getLastName();
						} else {
							names = names + " / " + clinicianList.get(i).getFirstName() + " "
									+ clinicianList.get(i).getLastName();
						}
					}
					pwcInfo.setCaregiverName(names);

				}
				pwcInfo.setimgPath(patient.getPicPath());
				pwcInfo.setPatientId(patient.getPatientId().toString());
				String name = patient.getFirstName() + " " + patient.getLastName();
				pwcInfo.setPatientNamePhone(name + " " + patient.getPhoneNumber());
				AddressVO address = patient.getAddress();
				String patientAdd = address.getAddress1() + ", " + address.getCity() + ", " + address.getState();
				pwcInfo.setPatientAddress(patientAdd);
				if (patient.getWatch() != null) {
					if (patient.getWatch().getWatchMake() != null) {
						String assignedWatch = patient.getWatch().getWatchMake();
						if (patient.getWatch().getWatchModel() != null) {
							assignedWatch = assignedWatch + " " + patient.getWatch().getWatchModel();
						}
						pwcInfo.setAssignedWatch(assignedWatch);
					}
				}
				// pwcInfo.setCaregiverName(patient.getClinician().getFirstName());
				pwcInfo.setCaregiverRelationship(patient.getcaregiverRelationship());
				data.add(pwcInfo);
			}
			logger.info("pat list**" + data.size());
			patientsInfo.setPatientsInfo(data);
			patientsInfo.setSuccess(true);
			msg[0] = new String("Operation Successful");
			patientsInfo.setMessages(msg);
			return patientsInfo;
		} catch (WatchRxServiceException e) {
			logger.error("ERROR:{}", e);
			logger.info("Service Exception");
			patientsInfo.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = e.getMessage();
			patientsInfo.setMessages(messages);
			patientsInfo.setSuccess(false);
			return patientsInfo;
		} catch (Exception e) {
			logger.error("ERROR:{}", e);
			logger.info("Exception : " + e.getMessage());
			patientsInfo.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			patientsInfo.setMessages(messages);
			patientsInfo.setSuccess(false);
			return patientsInfo;
		}
	}

	@RequestMapping(value = "/patientAddressBillingInfo/{index}/{pageSize}/{startDate}/{endDate}", method = RequestMethod.GET)
	public @ResponseBody PatientWatchCaregiverListVO getPatientAddressBillingStatus(HttpServletRequest request,
			@PathVariable Integer index, @PathVariable Integer pageSize, @PathVariable String startDate,
			@PathVariable String endDate) throws Exception {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<Object[]> patientsList = new ArrayList<>();
		Long patientsCount = 0l;
		if (user != null) {
			patientsCount = patientService.getAllPatientsCountByOrgIdAndStatus(user.getRoleType(), user.getOrgId(), "Y",
					user.getUserId(), false);
			patientsList = patientService.getPatientBillingAddressInfo(user.getOrgId(), index, pageSize,
					user.getUserId());
		} else {
			logger.info("Unauthorized access");
			PatientWatchCaregiverListVO response = new PatientWatchCaregiverListVO();
			response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

		List<PatientWatchCaregiverInfoVO> data = new ArrayList<>();
		PatientWatchCaregiverListVO patientsInfo = new PatientWatchCaregiverListVO();
		String msg[] = new String[1];
		if (patientsList.size() > 0) {
			Set<String> dataId = new HashSet<>();

			DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Date sDate = sdf.parse(startDate);

			Calendar startDateCal = Calendar.getInstance();
			startDateCal.setTime(sDate);
			startDateCal.set(Calendar.HOUR_OF_DAY, 00);
			startDateCal.set(Calendar.MINUTE, 00);
			startDateCal.set(Calendar.SECOND, 00);

			Date eDate = sdf.parse(endDate);
			Calendar endDateCal = Calendar.getInstance();
			endDateCal.setTime(eDate);
			endDateCal.set(Calendar.HOUR_OF_DAY, 23);
			endDateCal.set(Calendar.MINUTE, 59);
			endDateCal.set(Calendar.SECOND, 59);

			for (Object[] patient : patientsList) {
				if (dataId.add(patient[0].toString())) {

//					Map<String, Integer> dataCnt = patientService.validationInfo(((BigInteger) patient[0]).longValue(),
//							null);
					Map<String, Integer> dataCnt = patientService.validationInfoByStartAndEndDate(
							((BigInteger) patient[0]).longValue(), startDateCal.getTime(), endDateCal.getTime());

					PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
					pwcInfo.setRpmCcMMins("RPM: " + dataCnt.get("rpmMins") + "\nCCM: " + dataCnt.get("ccmMins")
							+ "\nPCM: " + dataCnt.get("pcmMins"));
					pwcInfo.setNoOfDays(dataCnt.get("daysCounted"));
					pwcInfo.setPatientId(patient[0].toString());
					pwcInfo.setPatientName(patient[1].toString());
					if (patient[2] != null) {
						pwcInfo.setdob(patient[2].toString());
					}

					if (dataCnt != null && (dataCnt.get("rpmMins") >= 20 || dataCnt.get("ccmMins") >= 20)
							&& dataCnt.get("daysCounted") >= 16) {
						pwcInfo.setBillingStatus("VALIDATION PENDING");
					} else {
						pwcInfo.setBillingStatus("IN PROGRESS");
					}

					List<String> cptCodeList = new ArrayList<>();
					cptCodeList = patientDAO.getCptCodeForPatient(Long.parseLong(patient[0].toString()));
					pwcInfo.setcptCode(cptCodeList);

					if (patient[3] != null) {
						pwcInfo.setCaseManagerId(Long.parseLong(patient[3].toString()));
					}
					if (patient[5] != null) {
						pwcInfo.setCaseManagerName(patient[5].toString());
					}
					data.add(pwcInfo);
				}
			}
			patientsInfo.setPatientsInfo(data);
			patientsInfo.setCount(patientsCount);
			patientsInfo.setSuccess(true);
			msg[0] = new String("Operation Successful");
			patientsInfo.setMessages(msg);
		} else {
			WatchRxException excep = new WatchRxException(WatchRxExceptionCodes.EMPTY_PATIENTLIST);
			msg[0] = new String(excep.getErrDesc());
			patientsInfo.setResponseCode(Integer.parseInt(excep.getErrCode()));
			patientsInfo.setMessages(msg);
			patientsInfo.setSuccess(false);
		}
		return patientsInfo;
	}

	@RequestMapping(value = "/patientSummaryInfo/alerts/{patientId}", method = RequestMethod.GET)
	public @ResponseBody PatientVO getAllPatientAlerts(HttpServletRequest request, @PathVariable Long patientId)
			throws Exception {
		PatientVO patientVO = null;
		patientVO = patientService.getPatientAlerts(patientId);
		patientVO.setPatientId(patientId);
		return patientVO;
	}

	@RequestMapping(value = "/patientSummaryInfo/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody PatientWatchCaregiverListVO getPatientSummaryInfoByIndex(HttpServletRequest request,
			@PathVariable Integer index, @PathVariable Integer pageSize) throws Exception {
		logger.info(">>>>>>>>>>>>>>>>>>>>>>>>inside getPatientSummaryInfo:::: ");

		Long totlaNumberOfPat = 0L;
		List<PatientVO> patientsList = null;
		List<PatientWatchCaregiverInfoVO> data = new ArrayList<>();
		PatientWatchCaregiverListVO patientsInfo = new PatientWatchCaregiverListVO();

		try {
			UserVO user = new ObjectMapper()
					.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
			if (user == null) {
				patientsInfo.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				patientsInfo.setMessages(messages);
				patientsInfo.setSuccess(false);
				return patientsInfo;
			}
			totlaNumberOfPat = patientService.getAllPatientsCountByOrgIdAndStatus(user.getRoleType(), user.getOrgId(),
					"Y", user.getUserId(), false);
			patientsList = patientService.getAllPatientsByOrgId(user.getOrgId(), user.getRoleType(), index, pageSize,
					user.getUserId());

			String msg[] = new String[1];
			Set<Long> pats = new HashSet<>();
			for (PatientVO patient : patientsList) {
				if (pats.add(patient.getPatientId())) {
					patient.setMrn(
							String.valueOf(patient.getMrn() == null ? patient.getPatientId() : patient.getMrn()));
					PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
					List<ClinicianVO> clinicianList = new ArrayList<>();
					clinicianList = clinicianService.getCaseManagersByPatient(patient.getPatientId());
					String names = new String();

					if (clinicianList.size() > 0 && clinicianList.size() == 1) {
						pwcInfo.setCaseManagerName(
								clinicianList.get(0).getFirstName() + " " + clinicianList.get(0).getLastName());
						pwcInfo.setCaseManagerId(clinicianList.get(0).getClinicianId());

					} else if (clinicianList.size() > 0) {
						for (int i = 0; i < clinicianList.size(); i++) {
							if (i == 0) {
								names = clinicianList.get(i).getFirstName() + " " + clinicianList.get(0).getLastName();
							} else {
								names = names + " / " + clinicianList.get(i).getFirstName() + " "
										+ clinicianList.get(i).getLastName();
							}
						}
						pwcInfo.setCaseManagerName(names);
						pwcInfo.setCaseManagerId(clinicianList.get(0).getClinicianId());

					}
					List<CareGiverMinimalVO> careGiverMinimalVOList = new ArrayList<>();

					clinicianList = clinicianService.getCareGiversByPatient(patient.getPatientId());

					for (ClinicianVO clinicianVO : clinicianList) {
						CareGiverMinimalVO careGiverMinimalVO = new CareGiverMinimalVO();
						careGiverMinimalVO.setCareGiverId(clinicianVO.getClinicianId());
						careGiverMinimalVO.setCareGiverFirstName(clinicianVO.getFirstName());
						careGiverMinimalVO.setCareGiverLastName(clinicianVO.getLastName());
						careGiverMinimalVOList.add(careGiverMinimalVO);

					}
					pwcInfo.setCareGiverVOList(careGiverMinimalVOList);
					pwcInfo.setimgPath(patient.getPicPath());
					pwcInfo.setPatientId(patient.getPatientId().toString());
					String name = patient.getFirstName() + " " + patient.getLastName();
					pwcInfo.setPatientNamePhone(name + " " + patient.getPhoneNumber());
					pwcInfo.setPatientName(name);
					pwcInfo.setPatientPhoneNumber(patient.getPhoneNumber());
					pwcInfo.setChronicConditions(patient.getChronicConditions());
					pwcInfo.setPhysicianName(patient.getPhysicianName());
					pwcInfo.setMrnNo(patient.getMrn());
					data.add(pwcInfo);
				}
			}
			// totlaNumberOfPat = (long) pats.size();
			patientsInfo.setPatientsInfo(data);
			patientsInfo.setCount(totlaNumberOfPat);
			patientsInfo.setSuccess(true);
			msg[0] = new String("Operation Successful");
			patientsInfo.setMessages(msg);
			return patientsInfo;
		} catch (WatchRxServiceException e) {
			logger.error("ERROR:{}", e);
			logger.info("Service Exception");
			patientsInfo.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = e.getMessage();
			patientsInfo.setMessages(messages);
			patientsInfo.setSuccess(false);
			return patientsInfo;
		} catch (Exception e) {
			logger.error("ERROR:{}", e);
			logger.info("Exception : " + e.getMessage());
			patientsInfo.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			patientsInfo.setMessages(messages);
			patientsInfo.setSuccess(false);
			return patientsInfo;
		}
	}

	@RequestMapping(value = "/patientSummaryInfo/{pname}", method = RequestMethod.GET)
	public @ResponseBody PatientWatchCaregiverListVO getPatientSummaryInfoByName(HttpServletRequest request,
			@PathVariable String pname) throws Exception {

		PatientListVO parentList = null;
		Long totlaNumberOfPat = 0L;
		List<PatientVO> patientList = new ArrayList<>();
		PatientWatchCaregiverListVO patientsInfo = new PatientWatchCaregiverListVO();
		try {
			UserVO user = new ObjectMapper()
					.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
			if (user == null) {
				patientsInfo.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				patientsInfo.setMessages(messages);
				patientsInfo.setSuccess(false);
				return patientsInfo;
			}

			parentList = patientService.getPatientsByNameAndOrgId(user.getOrgId(), user.getRoleType(), pname,
					user.getUserId());
			patientList = parentList.getPatients();
			totlaNumberOfPat = (Long.valueOf(patientList.size()));

			List<PatientWatchCaregiverInfoVO> data = new ArrayList<>();

			String msg[] = new String[1];
			for (PatientVO patient : patientList) {
				patient.setMrn(String.valueOf(patient.getMrn() == null ? patient.getPatientId() : patient.getMrn()));
				PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
				List<ClinicianVO> clinicianList = new ArrayList<>();
				clinicianList = clinicianService.getCaseManagersByPatient(patient.getPatientId());
				String names = new String();

				if (clinicianList.size() > 0 && clinicianList.size() == 1) {
					pwcInfo.setCaseManagerName(
							clinicianList.get(0).getFirstName() + " " + clinicianList.get(0).getLastName());
					pwcInfo.setCaseManagerId(clinicianList.get(0).getClinicianId());

				} else if (clinicianList.size() > 0) {
					for (int i = 0; i < clinicianList.size(); i++) {
						if (i == 0) {
							names = clinicianList.get(i).getFirstName() + " " + clinicianList.get(0).getLastName();
						} else {
							names = names + " / " + clinicianList.get(i).getFirstName() + " "
									+ clinicianList.get(i).getLastName();
						}
					}
					pwcInfo.setCaseManagerName(names);
					pwcInfo.setCaseManagerId(clinicianList.get(0).getClinicianId());

				}
				List<CareGiverMinimalVO> careGiverMinimalVOList = new ArrayList<>();

				clinicianList = clinicianService.getCareGiversByPatient(patient.getPatientId());

				for (ClinicianVO clinicianVO : clinicianList) {
					CareGiverMinimalVO careGiverMinimalVO = new CareGiverMinimalVO();
					careGiverMinimalVO.setCareGiverId(clinicianVO.getClinicianId());
					careGiverMinimalVO.setCareGiverFirstName(clinicianVO.getFirstName());
					careGiverMinimalVO.setCareGiverLastName(clinicianVO.getLastName());
					careGiverMinimalVOList.add(careGiverMinimalVO);

				}
				pwcInfo.setCareGiverVOList(careGiverMinimalVOList);

				pwcInfo.setimgPath(patient.getPicPath());
				pwcInfo.setPatientId(patient.getPatientId().toString());
				String name = patient.getFirstName() + " " + patient.getLastName();
				pwcInfo.setPatientNamePhone(name + " " + patient.getPhoneNumber());
				pwcInfo.setPatientName(name);
				pwcInfo.setPatientPhoneNumber(patient.getPhoneNumber());

				AddressVO address = patient.getAddress();
				String patientAdd = address.getAddress1() + ", " + address.getCity() + ", " + address.getState();
				pwcInfo.setPatientAddress(patientAdd);
				if (patient.getWatch() != null) {
					if (patient.getWatch().getWatchMake() != null) {
						String assignedWatch = patient.getWatch().getWatchMake();
						if (patient.getWatch().getWatchModel() != null) {
							assignedWatch = assignedWatch + " " + patient.getWatch().getWatchModel();
						}
						pwcInfo.setAssignedWatch(assignedWatch);
					}
				}
				pwcInfo.setCaregiverRelationship(patient.getcaregiverRelationship());
				pwcInfo.setPhysicianId(patient.getPhysicianId());
				pwcInfo.setPhysicianName(patient.getPhysicianName());
				pwcInfo.setAlerts(patient.getAlerts());
				pwcInfo.setBillingStatus(patient.getBillingStatus());
				pwcInfo.setMrnNo(patient.getMrn());
				data.add(pwcInfo);
			}
			patientsInfo.setPatientsInfo(data);
			patientsInfo.setCount(totlaNumberOfPat);
			patientsInfo.setSuccess(true);
			msg[0] = new String("Operation Successful");
			patientsInfo.setMessages(msg);
			return patientsInfo;
		} catch (WatchRxServiceException e) {
			logger.info("Service Exception");
			patientsInfo.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = e.getMessage();
			patientsInfo.setMessages(messages);
			patientsInfo.setSuccess(false);
			return patientsInfo;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("Exception : " + e.getMessage());
			patientsInfo.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			patientsInfo.setMessages(messages);
			patientsInfo.setSuccess(false);
			return patientsInfo;
		}
	}

	@RequestMapping(value = "/updatePatientInformation", method = RequestMethod.POST)
	public @ResponseBody StatusVO updatePatientInformation(HttpServletRequest request, @RequestBody PatientVO patientVO)
			throws Exception {
		StatusVO status = new StatusVO();
		System.out.println("Inside Save Patient");

		long patientId = patientVO.getPatientId();
		PatientVO patient = patientService.getPatient(patientId);
		patient.setAddress(patientVO.getAddress());
		System.out.println(" PatientVO Address1: " + patient.getAddress().getAddress1());
		patient.setFirstName(patientVO.getFirstName());
		patient.setLastName(patientVO.getLastName());
		patient.setPhoneNumber(patientVO.getPhoneNumber());
		patient.setDob(patientVO.getDob());
		patient.setEarlyMorningHour(patientVO.getEarlyMorningHour());
		patient.setEarlyMorningMin(patientVO.getEarlyMorningMin());
		patient.setBreakFastHour(patientVO.getBreakFastHour());
		patient.setBreakFastMin(patientVO.getBreakFastMin());
		patient.setLunchHour(patientVO.getLunchHour());
		patient.setLunchMin(patientVO.getLunchMin());
		patient.setNoonSnackHour(patientVO.getNoonSnackHour());
		patient.setNoonSnackMin(patientVO.getNoonSnackMin());
		patient.setDinnerHour(patientVO.getDinnerHour());
		patient.setDinnerMin(patientVO.getDinnerMin());
		patient.setBedHour(patientVO.getBedHour());
		patient.setBedMin(patientVO.getBedMin());
		patient.setTimeSlots(patientVO.getTimeSlots());
		boolean returnStatus = patientService.savePatient(patient, null);

		status.setSuccess(returnStatus);

		return status;
	}

	@RequestMapping(value = "/updateEditPatientBasicInformation", method = RequestMethod.POST)
	public @ResponseBody StatusVO updateEditPatientBasicInformation(HttpServletRequest request,
			@RequestBody PatientVO patientVO) throws Exception {
		StatusVO status = new StatusVO();
		System.out.println("Inside Save Patient");

		long patientId = patientVO.getPatientId();
		PatientVO patient = patientService.getPatient(patientId);
		patient.setAddress(patientVO.getAddress());
		patient.setFirstName(patientVO.getFirstName());
		patient.setLastName(patientVO.getLastName());
		patient.setPhoneNumber(patientVO.getPhoneNumber());
		patient.setDob(patientVO.getDob());
		patient.setMrn(patientVO.getMrn());
		patient.setEmail(patientVO.getEmail());
		patient.setConnectingDevice(patientVO.getConnectingDevice());
		patient.setTimezone(patientVO.getTimezone() != null ? patientVO.getTimezone().replaceAll("\t", "")
				: patientVO.getTimezone());
		patient.setPersPhoneNumber(patientVO.getPersPhoneNumber());
//		patient.setIsConsentTaken(patientVO.getIsConsentTaken());
		patient.setConsentStatus(patientVO.getConsentStatus());
		patient.setCheckedPrograms(patientVO.getCheckedPrograms());
		patient.setGender(patientVO.getGender());
		boolean returnStatus = patientService.saveEditPatientBasicInfo(patient, null);
		status.setSuccess(returnStatus);
		return status;
	}

	@RequestMapping(value = "/savePatient", method = RequestMethod.POST)
	public @ResponseBody StatusVO savePatient(HttpServletRequest request, @RequestBody PatientVO patientVO)
			throws Exception {
		StatusVO status = new StatusVO();
		System.out.println("Inside Save Patient");
		/*
		 * logger.info("patientVO::hasNext::"+patientVO.getImageFile().getBytes(
		 * ).length);
		 * logger.info("iter::getImageFile::"+patientVO.getImageFile().getName() ); if
		 * (patientVO.getImageFile().getBytes().length>0) { GcsService gcsService =
		 * GcsServiceFactory.createGcsService(RetryParams.getDefaultInstance());
		 * AppIdentityService appIdentityService =
		 * AppIdentityServiceFactory.getAppIdentityService(); String defaultBucketName =
		 * appIdentityService.getDefaultGcsBucketName(); GcsFilename fileName = new
		 * GcsFilename(defaultBucketName+"/patient",
		 * patientVO.getImageFile().getOriginalFilename()); GcsOutputChannel
		 * outputChannel = gcsService.createOrReplace(fileName,
		 * GcsFileOptions.getDefaultInstance());
		 *
		 * outputChannel.write(ByteBuffer.wrap(patientVO.getImageFile().getBytes ()));
		 * outputChannel.close(); logger.info("Done writing...");
		 * patientVO.setPicPath("patient/"+patientVO.getImageFile().
		 * getOriginalFilename()); patientVO.setFileModified(true); }else{
		 * if(patientVO.getPicPath() != null && !"".equals(patientVO.getPicPath())){
		 * patientVO.setFileModified(false); }
		 *
		 * }
		 */
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		System.out.println("Country: " + patientVO.getAddress().getCountry());
		patientService.savePatient(patientVO, user);

		List<PatientVO> patientList = patientService.getPatientList(user);
		System.out.println("Patient List size: " + patientList.size());
		status.setSuccess(true);
		String[] msg = new String[] { "Added Patient Successfully" };

		status.setMessages(msg);

		return status;
	}

	@RequestMapping(value = "/editPrescription", method = RequestMethod.POST)
	public @ResponseBody StatusVO editPrescription(HttpServletRequest request,
			@RequestBody CreatePatientRequestMedicationVO createPatientRequestMedicationVO) throws Exception {
		logger.info("In editPrescription method");
		logger.info("CreatePatientRequestMedicationVO is: " + createPatientRequestMedicationVO.toString());
		StatusVO response = new StatusVO();
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null && (user.getRoleType() == Constants.UserType.CAREGIVER
					|| user.getRoleType() == Constants.UserType.CASEMANAGER
					|| user.getRoleType() == Constants.UserType.PHYSICIAN
					|| user.getRoleType() == Constants.UserType.PATIENT)) {

				if (patientService.getPrescriptionById(createPatientRequestMedicationVO.getPrescriptionId()) == null) {
					response.setResponseCode(Constants.ResponseCode.PRESCRIPTIONNOTFOUND);
					String messages[] = new String[1];
					messages[0] = Constants.ResponseString.PRESCRIPTIONNOTFOUND;
					response.setMessages(messages);
					response.setSuccess(false);
					return response;
				}

				patientService.editPrescription(createPatientRequestMedicationVO);
				response.setResponseCode(Constants.ResponseCode.PRESCRIPTIONEDITED);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.PRESCRIPTIONEDITED;
				response.setMessages(messages);
				response.setSuccess(true);
				sendMedicationInfoByPrsecriptionId(createPatientRequestMedicationVO.getPrescriptionId());
				return response;
			} else {
				logger.info("Unauthorized access");
				if (user != null) {
					logger.info("Unauthorized access by user with username " + user.getUserName());
				}
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}
		} catch (Exception e) {
			logger.error("Error in method editPrescription " + e.getMessage());
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}

	@RequestMapping(value = "/createPrescription", method = RequestMethod.POST)
	public @ResponseBody CreatePrescriptionResponseVO createPrescription(HttpServletRequest request,
			@RequestBody CreatePrescriptionRequestVO createPatientRequestMedicationVO) throws Exception {
		logger.info("In method createPrescription");
		logger.info("CreatePrescriptionRequestVO is: " + createPatientRequestMedicationVO.toString());
		CreatePrescriptionResponseVO response = new CreatePrescriptionResponseVO();
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null && ((user.getRoleType() == Constants.UserType.CAREGIVER)
					|| (user.getRoleType() == Constants.UserType.CASEMANAGER
							|| (user.getRoleType() == Constants.UserType.PHYSICIAN))
					|| (user.getRoleType() == Constants.UserType.PATIENT))) {

				if (patientService.getPatientById(createPatientRequestMedicationVO.getPatientId()) == null) {
					response.setResponseCode(Constants.ResponseCode.PATIENTNOTFOUND);
					String messages[] = new String[1];
					messages[0] = Constants.ResponseString.PATIENTNOTFOUND;
					response.setMessages(messages);
					response.setSuccess(false);
					return response;
				}

				Long createdPrescriptionId = patientService.createPrescription(createPatientRequestMedicationVO);
				response.setCreatedPrescription(createdPrescriptionId);
				response.setResponseCode(Constants.ResponseCode.PRESCRIPTIONCREATED);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.PRESCRIPTIONCREATED;
				response.setMessages(messages);
				response.setSuccess(true);
				sendMedicationInfoByPrsecriptionId(createdPrescriptionId);
				return response;
			} else {
				logger.info("Unauthorized access");
				if (user != null) {
					logger.info("Unauthorized access by user with username " + user.getUserName());
				}
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}

	@RequestMapping(value = "/savePatientflow", method = RequestMethod.POST)
	public @ResponseBody CreatePatientResponseVO savePatientflow(HttpServletRequest request,
			@RequestBody CreatePatientRequestVO createpatientrequestVO) throws Exception {
		logger.info("inside savePatientFlow:::: ");
		CreatePatientResponseVO response = new CreatePatientResponseVO();
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user == null) {
				logger.info("Unauthorized access");
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}
			if (user.getRoleType() == Constants.UserType.CAREGIVER
					|| user.getRoleType() == Constants.UserType.CASEMANAGER) {

				PatientVO patientVO = new PatientVO();
				AddressVO addressvo = new AddressVO();
				if (createpatientrequestVO.getBasicinfo().getId() != null) {
					patientVO.setPatientId(createpatientrequestVO.getBasicinfo().getId());
				}

				setAddress(createpatientrequestVO, addressvo);
				patientVO.setAddress(addressvo);
				setBasicPatientInfo(createpatientrequestVO, patientVO);
				setPatientSchedule(createpatientrequestVO, patientVO);

				PatientVO savedPatient = patientService.savePatientAndReturnPatient(patientVO, user);

				patientService.saveEmergencyContacts(savedPatient, createpatientrequestVO.getEmergencyContacts());
				patientService.saveChronicConditions(savedPatient, createpatientrequestVO.getChronicConditions());

				CreatePatientRequestCptDataVO cptData = createpatientrequestVO.getCptData();
				CreatePatientRequestInsuranceVO insuranceData = createpatientrequestVO.getInsuranceData();

				if (cptData.getCptVOList().size() > 0) {
					savedPatient.setCptVOList(cptData.getCptVOList());
					patientService.saveCptDetails(savedPatient);
				}

				if (insuranceData.getInsuranceVOList().size() > 0) {
					savedPatient.setInsuranceVOList(insuranceData.getInsuranceVOList());
					patientService.saveInsuranceDetails(savedPatient);
				}

				List<MedicationImageExistenceVO> savedPrescriptions = patientService.savePrescriptions(savedPatient,
						createpatientrequestVO.getMedications());

				List<PatientPrescriptionVO> savedPrescriptionDetails = patientService
						.getPatientPrescriptionList(savedPatient.getPatientId());
				List<CreatePatientRequestEmergencyContactVO> savedEmergencyContacts = patientService
						.getPatientEmergencyContacts(savedPatient.getPatientId());

				setAllPatientDetails(savedPatient, savedPrescriptionDetails, savedEmergencyContacts, response);

				response.setSuccess(true);
				response.setResponseCode(com.medsure.common.Constants.ResponseCode.PATIENTCREATED);
				String[] messages = new String[1];
				messages[0] = com.medsure.common.Constants.ResponseString.PATIENTCREATED;
				response.setMessages(messages);
				response.setCreatedPatientId(savedPatient.getPatientId());
				response.setSavedPrescriptions(savedPrescriptions);
				return response;
			} else {
				logger.info("Unauthorized access");
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}

		} catch (ParseException e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INCORRECTDATEFORMAT);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INCORRECTDATEFORMAT;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (WatchRxServiceException e) {
			logger.info("Service Exception");
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = e.getMessage();
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (Exception e) {
			logger.info("Exception : " + e.getMessage());
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}
	}

	private void setAllPatientDetails(PatientVO savedPatient, List<PatientPrescriptionVO> savedPrescriptionDetails,
			List<CreatePatientRequestEmergencyContactVO> savedEmergencyContacts, CreatePatientResponseVO response) {
		// TODO Auto-generated method stub
		CreatePatientBasicInformationResponseVO patientBasicInfo = patientService
				.getPatientBasicInfo(new CreatePatientRequestBasicInformationVO(savedPatient.getPatientId()));

		CreatePatientRequestDailyScheduleVO patDailySchesule = patientService
				.getDailyScdByPatientId(savedPatient.getPatientId());

		CreatePatientRequestEmergencyContactListVO emergencyList = new CreatePatientRequestEmergencyContactListVO();
		emergencyList.setEmergencyContacts(savedEmergencyContacts);
		emergencyList.setPatientId(savedPatient.getPatientId());

		List<CreatePatientRequestMedicationVO> medList = new ArrayList<>(savedPrescriptionDetails.size());
		for (PatientPrescriptionVO patientPrescriptionVO : savedPrescriptionDetails) {
			medList.add(patientService.changePrescriptionFormat(patientPrescriptionVO));
		}

		response.setMedications(new CreatePatientRequestMedicationListVO());
		response.getMedications().setMedications(medList);
		response.setBasicinfo(patientBasicInfo);
		response.setDailySchedule(patDailySchesule);
		response.setEmergencyContacts(emergencyList);

	}

	private void setPatientSchedule(CreatePatientRequestVO createpatientrequestVO, PatientVO patientVO) {
		patientVO.setEarlyMorningHour(createpatientrequestVO.getDailySchedule().getMorning().split(":")[0]);
		patientVO.setEarlyMorningMin(createpatientrequestVO.getDailySchedule().getMorning().split(":")[1]);
		patientVO.setBreakFastHour(createpatientrequestVO.getDailySchedule().getBreakfast().split(":")[0]);
		patientVO.setBreakFastMin(createpatientrequestVO.getDailySchedule().getBreakfast().split(":")[1]);
		patientVO.setLunchHour(createpatientrequestVO.getDailySchedule().getLunch().split(":")[0]);
		patientVO.setLunchMin(createpatientrequestVO.getDailySchedule().getLunch().split(":")[1]);

		patientVO.setNoonSnackHour(createpatientrequestVO.getDailySchedule().getSnack().split(":")[0]);
		patientVO.setNoonSnackMin(createpatientrequestVO.getDailySchedule().getSnack().split(":")[1]);
		patientVO.setDinnerHour(createpatientrequestVO.getDailySchedule().getDinner().split(":")[0]);
		patientVO.setDinnerMin(createpatientrequestVO.getDailySchedule().getDinner().split(":")[1]);
		patientVO.setBedHour(createpatientrequestVO.getDailySchedule().getBedTime().split(":")[0]);
		patientVO.setBedMin(createpatientrequestVO.getDailySchedule().getBedTime().split(":")[1]);
	}

	private void setBasicPatientInfo(CreatePatientRequestVO createpatientrequestVO, PatientVO patientVO)
			throws ParseException {
		patientVO.setFirstName(createpatientrequestVO.getBasicinfo().getFirstname());
		patientVO.setLastName(createpatientrequestVO.getBasicinfo().getLastname());
		patientVO.setPhoneNumber(createpatientrequestVO.getBasicinfo().getPhonenumber());
		patientVO.setPhysicianId(createpatientrequestVO.getBasicinfo().getPhysicianId());
		if (createpatientrequestVO.getBasicinfo().getDob() != null) {
			DateFormat srcDf = new SimpleDateFormat("yyyy-MM-dd");
			Date dateOfBirth = srcDf.parse(createpatientrequestVO.getBasicinfo().getDob());
			patientVO.setDob(dateOfBirth);
		} else {
			patientVO.setDob(null);
		}
		patientVO.setInsuranceVOList(createpatientrequestVO.getInsuranceData().getInsuranceVOList());
	}

	private void setAddress(CreatePatientRequestVO createpatientrequestVO, AddressVO addressvo) {
		addressvo.setAddress1(createpatientrequestVO.getBasicinfo().getAddress1());
		addressvo.setAddress2(createpatientrequestVO.getBasicinfo().getAddress2());
		addressvo.setCity(createpatientrequestVO.getBasicinfo().getCity());
		addressvo.setState(createpatientrequestVO.getBasicinfo().getState());
		addressvo.setZip(createpatientrequestVO.getBasicinfo().getZip());
		addressvo.setCountry(createpatientrequestVO.getBasicinfo().getCountry());
	}

	@RequestMapping(value = "/savePatientGPS", method = RequestMethod.POST)
	public @ResponseBody CreatePatientResponseVO savePatientGPS(HttpServletRequest request,
			@RequestBody CreatePatientRequestVO createpatientrequestVO) throws Exception {
		logger.info("inside savePatientGPS:::: ");

		CreatePatientResponseVO response = new CreatePatientResponseVO();

		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null && (user.getRoleType() == Constants.UserType.CAREGIVER
					|| user.getRoleType() == Constants.UserType.CASEMANAGER)) {

				PatientVO patientVO = new PatientVO();
				AddressVO addressvo = new AddressVO();
				if (createpatientrequestVO.getBasicinfo().getId() != null) {
					patientVO.setPatientId(createpatientrequestVO.getBasicinfo().getId());
				}

				setAddress(createpatientrequestVO, addressvo);
				patientVO.setAddress(addressvo);
				setBasicPatientInfo(createpatientrequestVO, patientVO);
				setPatientSchedule(createpatientrequestVO, patientVO);

				PatientVO savedPatient = patientService.savePatientAndReturnPatient(patientVO, user);

				patientService.saveEmergencyContacts(savedPatient, createpatientrequestVO.getEmergencyContacts());
				patientService.saveChronicConditions(savedPatient, createpatientrequestVO.getChronicConditions());

				CreatePatientRequestCptDataVO cptData = createpatientrequestVO.getCptData();
				CreatePatientRequestInsuranceVO insuranceData = createpatientrequestVO.getInsuranceData();

				if (cptData.getCptVOList().size() > 0) {
					savedPatient.setCptVOList(cptData.getCptVOList());
					patientService.saveCptDetails(savedPatient);
				}

				if (insuranceData.getInsuranceVOList().size() > 0) {
					savedPatient.setInsuranceVOList(insuranceData.getInsuranceVOList());
					patientService.saveInsuranceDetails(savedPatient);

				}

				List<MedicationImageExistenceVO> savedPrescriptions = patientService.savePrescriptions(savedPatient,
						createpatientrequestVO.getMedications());

				List<PatientPrescriptionVO> savedPrescriptionDetails = patientService
						.getPatientPrescriptionList(savedPatient.getPatientId());
				List<CreatePatientRequestEmergencyContactVO> savedEmergencyContacts = patientService
						.getPatientEmergencyContacts(savedPatient.getPatientId());

				setAllPatientDetails(savedPatient, savedPrescriptionDetails, savedEmergencyContacts, response);

				response.setSuccess(true);
				response.setResponseCode(com.medsure.common.Constants.ResponseCode.PATIENTCREATED);
				String[] messages = new String[1];
				messages[0] = com.medsure.common.Constants.ResponseString.PATIENTCREATED;
				response.setMessages(messages);
				response.setCreatedPatientId(savedPatient.getPatientId());
				response.setSavedPrescriptions(savedPrescriptions);

				GPSInfoVO gpsRequest = createpatientrequestVO.getGpsData();
				gpsRequest.setPatientId(response.getCreatedPatientId() + "");

				String address = gpsRequest.getAddress1() + ", " + gpsRequest.getCity() + ", " + gpsRequest.getState()
						+ ", " + gpsRequest.getCountry() + ", " + gpsRequest.getZip();
				logger.info("GPS Address string:::::::" + address);
				HashMap<String, String> location = getLatAndLong(address);
				if (location.get("Status").equals("OK")) {
					gpsRequest.setLatitude(location.get("Latitude"));
					logger.info("Latitude:::::::" + gpsRequest.getLatitude());
					gpsRequest.setLongitude(location.get("Longitude"));
					logger.info("Longitude:::::::" + gpsRequest.getLongitude());
				}
				GPSInfoVO gpsresult = gpsService.saveGPSInfo(gpsRequest);
				response.setGpsData(gpsresult);
				return response;
			} else {
				logger.info("Unauthorized access");
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}

		} catch (ParseException e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INCORRECTDATEFORMAT);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INCORRECTDATEFORMAT;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (WatchRxServiceException e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = e.getMessage();
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}
	}

	@RequestMapping(value = "/saveUpdatePatienPrescriptions", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveUpdatePatienPrescriptions(HttpServletRequest request,
			@RequestBody updateMedicationRequestVO medicationVO) throws Exception {
		try {
			List<PatientPrescriptionVO> medication = medicationVO.getMedications();
			for (PatientPrescriptionVO precription : medication) {
				patientService.savePrescription(precription);
			}
			StatusVO status = new StatusVO();
			status.setSuccess(true);
			status.setResponseCode(200);
			return status;
		} catch (Exception e) {
			throw new WatchRxServiceException("Error while updating the medications.");
		}
	}

	@RequestMapping(value = "/deleteAnItem", method = RequestMethod.POST)
	public @ResponseBody StatusVO deleteAnItem(HttpServletRequest request, @RequestBody DeleteAnRecordVO itemVO)
			throws Exception {

		String itemName = itemVO.getItemName();
		long itemId = itemVO.getItemId();

		patientService.deleteById(itemName, itemId, itemVO.getPatientId());

		StatusVO status = new StatusVO();
		status.setSuccess(true);
		status.setResponseCode(com.medsure.common.Constants.ResponseCode.PATIENTCREATED);
		return status;
	}

	@RequestMapping(value = "/patientImage", method = RequestMethod.POST)
	public @ResponseBody StatusVO savePatientImage(HttpServletRequest request, SavePatientImageRequestVO patientImage)
			throws Exception {
		logger.info("inside savePatientWithImage:::: ");
		StatusVO response = new StatusVO();
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				return patientService.savePatientImage(patientImage);
			} else {

				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}

	@RequestMapping(value = "/medicationImage", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveMedicationImage(HttpServletRequest request,
			SaveMedicationImageRequestVO patientImage) throws Exception {
		StatusVO response = new StatusVO();
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				return patientService.saveMedicationImage(patientImage);
			} else {
				logger.info("Unauthorized access");

				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}
	}

	@RequestMapping(value = "/patient", method = RequestMethod.GET)
	public ModelAndView getPatient(HttpServletRequest request, long patientId) throws Exception {
		logger.info("inside getClinician:::: " + patientId);
		PatientVO patient = null;
		if (patientId > 0) {
			patient = patientService.getPatient(patientId);
		} else {
			patient = new PatientVO();
		}
		ModelAndView mav = new ModelAndView();
		mav.addObject("maritalStatusList", DropdownUtils.getRefDataByRefType(Constants.ReferenceType.MARITAL_STATUS));
		mav.addObject("languages", DropdownUtils.getRefDataByRefType(Constants.ReferenceType.LANGUAGE));
		mav.addObject("hourList", DropdownUtils.hours());
		mav.addObject("minuteList", DropdownUtils.minutes());
		mav.addObject("patientVO", patient);
		mav.setViewName("patientEdit");
		return mav;
	}

	@RequestMapping(value = "/patientInfo", method = RequestMethod.POST)
	public @ResponseBody PatientVO getPatientInfo(HttpServletRequest request,
			@RequestBody PatientWatchCaregiverInfoVO patientInfo) throws Exception {
		logger.info("inside getPatientInfo:::: " + patientInfo.getPatientId());
		String temId = patientInfo.getPatientId();
		long patientId = Long.valueOf(temId);
		PatientVO patient = null;
		if (patientId > 0) {
			patient = patientService.getPatient(patientId);
			patient.setMrn(patient.getMrn() == null ? String.valueOf(patientId) : patient.getMrn());
			System.out.println(patient.getTimezone());
		} else {
			patient = new PatientVO();
		}
		return patient;
	}

	@RequestMapping(value = "/prescriptionSummary", method = RequestMethod.GET)
	public ModelAndView getPrescriptionSummary(HttpServletRequest request, long patientId) throws Exception {
		logger.info("inside prescriptionSummary:::: " + patientId);
		List<PatientPrescriptionVO> prescriptionList = patientService.getPatientPrescriptionList(patientId);
		ModelAndView mav = new ModelAndView();
		mav.addObject("prescriptionList", prescriptionList);
		mav.addObject("patientVO", patientService.getPrescriptionPatient(patientId));
		mav.addObject("hourList", DropdownUtils.hours());
		mav.addObject("minuteList", DropdownUtils.minutes());
		mav.setViewName("patientPrescriptionSummary");
		return mav;
	}

	@RequestMapping(value = "/prescription", method = RequestMethod.POST)
	public @ResponseBody GetPrescriptionsResponseVO getPrescription(HttpServletRequest request,
			@RequestBody PrescriptionRequestVO prescriptionRequest) throws Exception {

		GetPrescriptionsResponseVO response = new GetPrescriptionsResponseVO();
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {

				if (prescriptionRequest.getPatientId() != null) {
					if (patientService.getPatient(prescriptionRequest.getPatientId()) == null) {
						response.setPrescriptions(null);
						response.setSuccess(false);
						response.setResponseCode(Constants.ResponseCode.PATIENTNOTFOUND);
						String[] messages = new String[1];
						messages[0] = Constants.ResponseString.PATIENTNOTFOUND;
						response.setMessages(messages);
						return response;
					}
				}
				List<PrescriptionVO> prescriptionList;
				if (prescriptionRequest.getPatientId() != null) {
					prescriptionList = patientService.getPrescriptionByPatientId(prescriptionRequest.getPatientId());
				} else {
					prescriptionList = patientService
							.getPrescriptionByPrescriptionIds(prescriptionRequest.getPrescriptionIds());
				}

				response.setPrescriptions(prescriptionList);
				response.setSuccess(true);
				response.setResponseCode(Constants.ResponseCode.PRESCRIPTIONSFOUND);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.PRESCRIPTIONSFOUND;
				response.setMessages(messages);
				return response;
			} else {

				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}

	@RequestMapping(value = "/patientPrescriptionSummary", method = RequestMethod.POST)
	public @ResponseBody List<PatientPrescriptionVO> getpatientPrescriptionSummary(HttpServletRequest request,
			@RequestBody PatientWatchCaregiverInfoVO patientInfo) throws Exception {
		String temId = patientInfo.getPatientId();
		long patientId = Long.valueOf(temId);
		logger.info("inside prescriptionSummary:::: " + temId);
		List<PatientPrescriptionVO> prescriptionList = null;
		if (patientId > 0) {
			prescriptionList = patientService.getPatientPrescriptionList(patientId);
		}

		return prescriptionList;
	}

	/*
	 * @RequestMapping(value = "/prescription", method = RequestMethod.GET) public
	 * ModelAndView getPrescription(HttpServletRequest request, long prescriptionId,
	 * long patientId) throws Exception { logger.info( "inside prescriptionId:::: "
	 * + prescriptionId); PatientPrescriptionVO prescription = null; if
	 * (prescriptionId > 0) { prescription =
	 * patientService.getPatientPrescription(prescriptionId); } else { prescription
	 * = new PatientPrescriptionVO(); PatientVO patientVO =
	 * patientService.getPrescriptionPatient(patientId);
	 * prescription.setPatient(patientVO); } ModelAndView mav = new ModelAndView();
	 * mav.addObject("prescriptionVO", prescription);
	 * mav.addObject("medicineFormList",
	 * DropdownUtils.getRefDataByRefType(Constants.ReferenceType.MEDICINE_FORM)) ;
	 * mav.addObject("medicineList",
	 * DropdownUtils.getRefDataByRefType(Constants.ReferenceType.MEDICINE_LIST)) ;
	 * mav.addObject("hourList", DropdownUtils.hours()); mav.addObject("minuteList",
	 * DropdownUtils.minutes()); // mav.addObject("patientVO",
	 * WatchRxUtils.getPatient((int) patientId));
	 * mav.setViewName("patientPrescriptionEdit"); return mav; }
	 */

	/*
	 *
	 * @RequestMapping(value = "/savePatient", method = RequestMethod.POST) public
	 * ModelAndView savePatient(HttpServletRequest request, @Valid PatientVO
	 * patientVO, BindingResult result) throws Exception { ModelAndView mav = new
	 * ModelAndView();
	 *
	 * //if (null!=(patientVO.getSsn())){ if
	 * (patientService.isSSNExists(patientVO)){ result.addError(new
	 * FieldError("patientVO", "ssn", "SSN already exists.")); } }/
	 *
	 * if(result.hasErrors()){
	 *
	 * if (null!=(patientVO.getSsn())){ if (patientService.isSSNExists(patientVO)){
	 * result.addError(new FieldError("patientVO", "ssn", "SSN already exists.")); }
	 * }
	 *
	 * if(result.hasErrors()){
	 *
	 * // // if (null!=(patientVO.getSsn())){ if //
	 * (patientService.isSSNExists(patientVO)){ result.addError(new //
	 * FieldError("patientVO", "ssn", "SSN already exists.")); } } //
	 *
	 * if (result.hasErrors()) { mav.addObject("patientVO", patientVO);
	 * mav.addObject("maritalStatusList",
	 * DropdownUtils.getRefDataByRefType(Constants.ReferenceType.MARITAL_STATUS) );
	 * mav.addObject("languages",
	 * DropdownUtils.getRefDataByRefType(Constants.ReferenceType.LANGUAGE));
	 * mav.setViewName("patientEdit"); return mav; }
	 * logger.info("patientVO::hasNext::" +
	 * patientVO.getImageFile().getBytes().length);
	 * logger.info("iter::getImageFile::" + patientVO.getImageFile().getName()); if
	 * (patientVO.getImageFile().getBytes().length > 0) { GcsService gcsService =
	 * GcsServiceFactory.createGcsService(RetryParams.getDefaultInstance());
	 * AppIdentityService appIdentityService =
	 * AppIdentityServiceFactory.getAppIdentityService(); String defaultBucketName =
	 * appIdentityService.getDefaultGcsBucketName(); GcsFilename fileName = new
	 * GcsFilename(defaultBucketName + "/patient",
	 * patientVO.getImageFile().getOriginalFilename()); GcsOutputChannel
	 * outputChannel = gcsService.createOrReplace(fileName,
	 * GcsFileOptions.getDefaultInstance());
	 *
	 * outputChannel.write(ByteBuffer.wrap(patientVO.getImageFile().getBytes())) ;
	 * outputChannel.close(); logger.info("Done writing...");
	 * patientVO.setPicPath("patient/" +
	 * patientVO.getImageFile().getOriginalFilename());
	 * patientVO.setFileModified(true); } else { if (patientVO.getPicPath() != null
	 * && !"".equals(patientVO.getPicPath())) { patientVO.setFileModified(false); }
	 * else { result.addError(new FieldError("patientVO", "picPath",
	 * "Please select an Image.")); mav.addObject("patientVO", patientVO);
	 * mav.addObject("maritalStatusList",
	 * DropdownUtils.getRefDataByRefType(Constants.ReferenceType.MARITAL_STATUS) );
	 * mav.addObject("languages",
	 * DropdownUtils.getRefDataByRefType(Constants.ReferenceType.LANGUAGE));
	 * mav.setViewName("patientEdit"); return mav;
	 *
	 * } } UserVO user = new
	 * ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails")
	 * , "patient@watCRX"),UserVO.class);
	 *
	 * patientService.savePatient(patientVO, user); List<PatientVO> patientList =
	 * patientService.getPatientList(user); mav.addObject("patientList",
	 * patientList); mav.setViewName("patientSummary"); return mav; }
	 */

	@RequestMapping(value = "/savePatentPrescription", method = RequestMethod.POST)
	public ModelAndView PatentPrescription(HttpServletRequest request,
			@ModelAttribute("prescriptionVO") @Valid PatientPrescriptionVO prescriptionVO, BindingResult result)
			throws Exception {
		ModelAndView mav = new ModelAndView();
		if (result.hasErrors()) {
			logger.info("Error starts " + result.getFieldError());
			mav.addObject("prescriptionVO", prescriptionVO);
			mav.addObject("medicineFormList", DropdownUtils.getRefDataByRefType(Constants.ReferenceType.MEDICINE_FORM));
			mav.addObject("medicineList", DropdownUtils.getRefDataByRefType(Constants.ReferenceType.MEDICINE_LIST));
			mav.addObject("hourList", DropdownUtils.hours());
			mav.addObject("minuteList", DropdownUtils.minutes());
			mav.setViewName("patientPrescriptionEdit");
			return mav;
		}
		logger.info("patientVO::hasNext::" + prescriptionVO.getImageFile().getBytes().length);
		logger.info("iter::getImageFile::" + prescriptionVO.getImageFile().getName());
		if (prescriptionVO.getImageFile().getBytes().length > 0) {
			GcsService gcsService = GcsServiceFactory.createGcsService(RetryParams.getDefaultInstance());
			AppIdentityService appIdentityService = AppIdentityServiceFactory.getAppIdentityService();
			String defaultBucketName = appIdentityService.getDefaultGcsBucketName();
			GcsFilename fileName = new GcsFilename(defaultBucketName + "/medicine",
					prescriptionVO.getImageFile().getOriginalFilename());
			GcsOutputChannel outputChannel = gcsService.createOrReplace(fileName, GcsFileOptions.getDefaultInstance());

			outputChannel.write(ByteBuffer.wrap(prescriptionVO.getImageFile().getBytes()));
			outputChannel.close();
			logger.info("Done writing...");
			prescriptionVO.setPicPath("medicine/" + prescriptionVO.getImageFile().getOriginalFilename());
			prescriptionVO.setFileModified(true);
		} else {
			if (prescriptionVO.getPicPath() != null && !"".equals(prescriptionVO.getPicPath())) {
				prescriptionVO.setFileModified(false);
			} else {

				mav.addObject("prescriptionVO", prescriptionVO);
				mav.addObject("medicineFormList",
						DropdownUtils.getRefDataByRefType(Constants.ReferenceType.MEDICINE_FORM));
				mav.addObject("medicineList", DropdownUtils.getRefDataByRefType(Constants.ReferenceType.MEDICINE_LIST));
				mav.addObject("hourList", DropdownUtils.hours());
				mav.addObject("minuteList", DropdownUtils.minutes());
				mav.setViewName("patientPrescriptionEdit");
				return mav;

			}
		}
		patientService.savePrescription(prescriptionVO);
		sendMedicationInfo(prescriptionVO);
		List<PatientPrescriptionVO> prescriptionList = patientService
				.getPatientPrescriptionList(prescriptionVO.getPatient().getPatientId());
		mav.addObject("prescriptionList", prescriptionList);
		mav.addObject("patientVO", prescriptionVO.getPatient());
		mav.setViewName("patientPrescriptionSummary");
		return mav;
	}

	@RequestMapping(value = "/removePatient", method = RequestMethod.POST)
	public @ResponseBody StatusVO removePatient(HttpServletRequest request,
			@RequestBody PatientWatchCaregiverInfoVO patient) throws Exception {
		logger.info("inside removePatient:::: " + patient.getCaregiverName() + " " + patient.getPatientId());
		String tmpId = patient.getPatientId();
		// System.out.println("PatientId: "+ tmpId);
		// long id = Long.parseLong(tmpId.trim());
		long id = Long.valueOf(tmpId);
		System.out.println("In remove Patient. Patient Id: " + id);
		PatientVO delPatient = patientService.getPatient(id);

		Long patientCount = patientService.getPatientsCountByPatientId(id);

		StatusVO status = new StatusVO();
		WatchVO watch = delPatient.getWatch();
		String[] msg = new String[1];

		if (patientCount > 0) {
			System.out.println("PatientAlerts/PatientVitals/Device Allocation details exists for the patient - " + id);
			status.setSuccess(false);
			WatchRxException excep = new WatchRxException(WatchRxExceptionCodes.WATCH_ASSIGNED);
			status.setResponseCode(Integer.getInteger(excep.getErrCode()));
			msg[0] = excep.getErrDesc();
			status.setMessages(msg);
		} else if (watch != null && StringUtils.isNotBlank(watch.getDeviceType())
				&& !watch.getDeviceType().equalsIgnoreCase("Mobile")) {
			System.out.println("In the if part watchid: " + watch.getWatchId());
			System.out.println("Patient Name: " + delPatient.getFirstName());
			status.setSuccess(false);
			WatchRxException excep = new WatchRxException(WatchRxExceptionCodes.WATCH_ASSIGNED);
			status.setResponseCode(Integer.getInteger(excep.getErrCode()));
			msg[0] = excep.getErrDesc();
			status.setMessages(msg);
		} else {
			System.out.println("In else part");
			patientService.deletePatient(id);
			status.setSuccess(true);
			msg[0] = Constants.ResponseString.REMOVEDPARENT;
			status.setMessages(msg);
		}
		return status;
	}

	@RequestMapping(value = "/deletePatientPrescription", method = RequestMethod.POST)
	public @ResponseBody StatusVO deletePatientPrescription(HttpServletRequest request,
			@RequestBody DeleteAnRecordVO record) throws Exception {
		logger.info("inside deletePrescription:::: " + record.getItemId());
		patientService.deletePrescription(record.getItemId());

		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(true);
		return statusVO;
	}

	@RequestMapping(value = "/removePrescription", method = RequestMethod.POST)
	public @ResponseBody StatusVO removePrescription(HttpServletRequest request, @RequestBody DeleteAnRecordVO record)
			throws Exception {
		logger.info("inside deletePrescription:::: " + record.getItemId());

		StatusVO response = new StatusVO();
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {

				if (patientService.getPrescription(record.getItemId()) == null) {
					response.setSuccess(false);
					response.setResponseCode(Constants.ResponseCode.PRESCRIPTIONNOTFOUND);
					String[] messages = new String[1];
					messages[0] = Constants.ResponseString.PRESCRIPTIONNOTFOUND;
					response.setMessages(messages);
					return response;
				}
				Long patientId = patientService.getPrescription(record.getItemId()).getWatchrxPatient().getPatientId();
				logger.info("Sending gcm to patientId: " + patientId);
				patientService.deletePrescription(record.getItemId());
				response.setSuccess(true);
				response.setResponseCode(Constants.ResponseCode.PRESCRIPTIONDELETED);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.PRESCRIPTIONDELETED;
				response.setMessages(messages);
				sendMedicationInfoByPatientId(patientId);
				return response;
			} else {

				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}

		} catch (Exception e) {
			logger.error("Error in removePrescription: " + e.getMessage());
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}

	@RequestMapping(value = "/deletePatient", method = RequestMethod.POST)
	@ResponseBody
	public String deletePatient(HttpServletRequest request, long patientId) throws Exception {
		logger.info("inside deletePatient:::: " + patientId);
		patientService.deletePatient(patientId);
		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(true);
		List<String> errorMsgs = new ArrayList<>();
		statusVO.setMessages(errorMsgs.toArray(new String[errorMsgs.size()]));
		return JsonUtils.toJSON(statusVO);
	}

	@RequestMapping(value = "/deletePrescription", method = RequestMethod.POST)
	@ResponseBody
	public String deletePrescription(HttpServletRequest request, long prescriptionId) throws Exception {
		logger.info("inside deletePrescription:::: " + prescriptionId);
		patientService.deletePrescription(prescriptionId);
		PatientPrescriptionVO vo = patientService.getPatientPrescription(prescriptionId);
		sendMedicationInfo(vo);
		StatusVO statusVO = new StatusVO();
		statusVO.setSuccess(true);
		List<String> errorMsgs = new ArrayList<>();
		statusVO.setMessages(errorMsgs.toArray(new String[errorMsgs.size()]));
		return JsonUtils.toJSON(statusVO);
	}

	@RequestMapping(value = "/alertSummaryDBrd", method = RequestMethod.GET)
	@ResponseBody
	public ModelAndView alertSummaryDashBoard(HttpServletRequest request) throws Exception {
		// logger.info("inside alertSummarydashBoard:::: ");
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		ModelAndView mav = new ModelAndView();
		List<MedicationAlert> data = new ArrayList<>();
		if (user.getRoleType() == 1) {
			data = WatchRxFactory.getAlertService().getAllAlertsByPatient();
		} else {
			GetAlerts alert = new GetAlerts();
			logger.info("Alertr data user id" + user.getUserId());
			logger.info("Alertr data user id" + user.getUserName());
			Long nurseId = WatchRxFactory.getPatientService().getClinicianByUserId(Long.valueOf(user.getUserId()));
			PatientGCInfos patientGCInfos = WatchRxFactory.getPatientService().getPatientsByClinician(nurseId);
			for (PatientCGInfo patientCGInfo : patientGCInfos.getPatientInfo()) {
				// logger.info("Alertr data patient id" + patientCGInfo.getPatientId());
				alert.setPatientId(patientCGInfo.getPatientId());
				List<MedicationAlert> tmpdata = WatchRxFactory.getAlertService().getAlertsByPatientId(alert);
				// logger.info("Alertr data patient id size " + tmpdata.size());
				// mav.addObject("patientName",patientCGInfo.getPatientName());
				data.addAll(tmpdata);
			}

		}
		logger.info("Alertr data" + data.toString());
		logger.info("Alertr data size" + data.size());
		mav.addObject("alertList", data);

		mav.setViewName("alertSummary");
		return mav;
	}

	@RequestMapping(value = "/saveEditedEmergencyContact", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveEditedEmergencyContact(HttpServletRequest request,
			@RequestBody CreatePatientRequestEmergencyContactVO emergencyContact) throws Exception {
		StatusVO resp = new StatusVO();
		System.out.println(emergencyContact.getNumber());
		resp.setSuccess(patientService.saveExistingEmergencyContacts(emergencyContact));

		return resp;
	}

	@RequestMapping(value = "/getPatientEmergencyContacts", method = RequestMethod.POST)
	public @ResponseBody List<CreatePatientRequestEmergencyContactVO> getPatientEmergencyContacts(
			HttpServletRequest request, @RequestBody PatientVO patient) throws Exception {

		List<CreatePatientRequestEmergencyContactVO> emergencyContacts = patientService
				.getPatientEmergencyContacts(patient.getPatientId());

		return emergencyContacts;
	}

	@RequestMapping(value = "/updateGPSInfo", method = RequestMethod.POST)
	public @ResponseBody GPSInfoVO updateGPSInfo(HttpServletRequest request, @RequestBody GPSInfoVO gps)
			throws Exception {
		GPSInfoVO updatedGps = new GPSInfoVO();
		GPSInfoVO tmpGps = gps;
		String[] messages = new String[1];
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				if (gps.getPatientId() != null) {
					String address = tmpGps.getAddress1() + ", " + tmpGps.getCity() + ", " + tmpGps.getState() + ", "
							+ tmpGps.getCountry() + ", " + tmpGps.getZip();
					System.out.println("Updated GPS Address string:::::::" + address);
					HashMap<String, String> location = getLatAndLong(address);
					if (location.get("Status").equals("OK")) {
						tmpGps.setLatitude(location.get("Latitude"));
						logger.info("Updated Latitude:::::::" + gps.getLatitude());

						tmpGps.setLongitude(location.get("Longitude"));
						logger.info("Updated Longitude:::::::" + gps.getLongitude());

					} else {
						logger.info("Location Status::::::" + location.get("Status"));
					}
					tmpGps.setAddress1(gps.getAddress1());
					System.out.println(gps.getAddress1());
					if (gps.getAddress2() == null || gps.getAddress2().contentEquals("undefined")) {
						tmpGps.setAddress2(null);
					} else {
						tmpGps.setAddress2(gps.getAddress2());
					}
					System.out.println(tmpGps.getAddress2());
					tmpGps.setCity(gps.getCity());
					tmpGps.setState(gps.getState());
					tmpGps.setCountry(gps.getCountry());
					tmpGps.setZip(gps.getZip());
					tmpGps.setRadius(gps.getRadius());
					tmpGps.setGpsStatus(gps.getGPSStatus());
					tmpGps.setTrackStatus(gps.isTrackStatus());
					tmpGps.setUnit(gps.getUnit());

					updatedGps = gpsService.updateGPSInfo(gps);
					if (updatedGps.isSuccess()) {
						updatedGps.setSuccess(true);
						updatedGps.setResponseCode(Constants.ResponseCode.GPSINFOUPDATED);
						messages[0] = Constants.ResponseString.GPSINFOUPDATED;
						updatedGps.setMessages(messages);
					} else {
						updatedGps.setSuccess(false);
						updatedGps.setResponseCode(Constants.ResponseCode.GPSUPDATEFAILED);
						messages[0] = Constants.ResponseString.GPSUPDATEFAILED;
						updatedGps.setMessages(messages);
					}

				}
			} else {
				updatedGps.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				updatedGps.setMessages(messages);
				updatedGps.setSuccess(false);
			}

		} catch (WatchRxServiceException e) {
			logger.error(e.getMessage(), e);
			updatedGps.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			messages[0] = e.getMessage();
			updatedGps.setMessages(messages);
			updatedGps.setSuccess(false);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			updatedGps.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			messages[0] = Constants.ResponseString.INTERNALERROR;
			updatedGps.setMessages(messages);
			updatedGps.setSuccess(false);
		}
		return updatedGps;

	}

	@RequestMapping(value = "/getGPSInfo", method = RequestMethod.POST)
	public @ResponseBody GPSInfoVO getGPSInfo(HttpServletRequest request,
			@RequestBody PatientWatchCaregiverInfoVO patientGps) throws Exception {

		GPSInfoVO response = new GPSInfoVO();
		String[] messages = new String[1];
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null && (user.getRoleType() == Constants.UserType.CAREGIVER
					|| (user.getRoleType() == Constants.UserType.CASEMANAGER))) {
				logger.info("GPS Patient Id::::::::::" + patientGps.getPatientId());
				if (patientGps.getPatientId() != null) {
					/*
					 * response = new GPSInfoVO(); response.setSuccess(true); messages[0] =
					 * "Testing get GPS"; response.setMessages(messages);
					 */
					response = gpsService.getGPSInfo(Long.parseLong(patientGps.getPatientId()));
					if (!response.isSuccess()) {
						response.setResponseCode(Constants.ResponseCode.GPSINFONOTFOUND);
						messages[0] = Constants.ResponseString.GPSINFONOTFOUND;
						response.setMessages(messages);
					} else {
						logger.info("Setting gps Info");
						// response.setSuccess(true);
						response.setResponseCode(Constants.ResponseCode.GPSINFOFOUND);
						messages[0] = Constants.ResponseString.GPSINFOFOUND;
						response.setMessages(messages);
						logger.info("GPS is set in response");
					}
				} else {
					response.setSuccess(false);
					WatchRxException excep = new WatchRxException(WatchRxExceptionCodes.INVALID_PATIENTID);
					messages[0] = excep.getErrDesc();
					response.setMessages(messages);
				}
			} else {
				logger.info("Unauthorized access");
				if (user != null) {
					logger.info("Unauthorized access by user with username " + user.getUserName());
				}
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
			}

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
		}
		logger.info("Returning response");
		return response;
	}

	@RequestMapping(value = "/sendInvite", method = RequestMethod.POST)
	public @ResponseBody StatusVO sendInvite(HttpServletRequest request, @RequestBody SecCGInviteReqVO inviteReq)
			throws Exception {

		StatusVO resp = new StatusVO();
		String[] id = inviteReq.getPatientId();
		StringBuilder patientList = new StringBuilder();
		String[] msg = new String[1];
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		Long nurseId = WatchRxFactory.getPatientService().getClinicianByUserId(Long.valueOf(user.getUserId()));
		ClinicianVO userCaregiver = clinicianService.getClinicianByUserName(user.getUserName());
		long clinicianId = userCaregiver.getClinicianId();
		if (id != null && id.length > 0) {
			for (int i = 0; i < id.length; i++) {
				System.out.println("Id length " + id.length);
				Long patientId = Long.parseLong(id[i]);
				PatientVO patient = WatchRxFactory.getPatientService().getPatient(patientId);
				String[] respMsg = new String[1];
				System.out.println("PatientId " + patientId);
				if (patientService.getPatient(patientId).getWatch() == null) {
					System.out.println("Breaking the loop");
					WatchRxException excep = new WatchRxException(WatchRxExceptionCodes.WATCH_NOT_ASSIGNED);
					msg[0] = excep.getErrDesc();
					resp.setMessages(msg);
					resp.setSuccess(false);
					break;
				} else if (clinicianService.getCaregiverRelationship(clinicianId, patient.getWatch().getWatchId())
						.equalsIgnoreCase(Constants.CaregiverRole.SECONDARY)) {
					System.out.println("Breaking the loop");
					WatchRxException excep = new WatchRxException(
							WatchRxExceptionCodes.SECONDARY_CAREGIVER_CANNOT_SEND_INVITE);
					msg[0] = excep.getErrDesc();
					resp.setMessages(msg);
					resp.setSuccess(false);
					break;

				} else {
					SecCGInviteInfo inviteInfo = new SecCGInviteInfo();
					inviteInfo.setPatientId(patientId);

					patientList.append(patientService.getPatient(patientId).getFirstName() + " "
							+ patientService.getPatient(patientId).getLastName());
					if (i != id.length - 1) {
						patientList.append(", ");
					}

					inviteInfo.setClinicianId(nurseId);
					inviteInfo.setEmail(inviteReq.getEmail());
					inviteInfo.setFirstName(inviteReq.getFirstName());
					inviteInfo.setLastName(inviteReq.getLastName());
					inviteInfo.setStatus(Constants.SecCGStatus.INVITE);
					System.out.println("Status: " + inviteInfo.getStatus());
					resp = patientService.inviteSecondaryCaregiver(inviteInfo);
					// System.out.println("Status " + new
					// Boolean(resp.isSuccess()).toString());

					respMsg = resp.getMessages();
					System.out.println("Response Status " + respMsg[0]);

					if (resp.isSuccess() && respMsg[0].equalsIgnoreCase(Constants.ResponseString.INVITESENT)) {
						System.out.println(respMsg);
						// msg[0] = Constants.ResponseString.INVITESENT;
						// resp.setMessages(msg);

						String body = "Hi " + inviteReq.getFirstName() + " " + inviteReq.getLastName() + ", "
								+ clinicianService.getClinician(nurseId, 5L).getFirstName() + " "
								+ clinicianService.getClinician(nurseId, 5L).getLastName() + " "
								+ "would like you to care for " + patientList.toString() + ". Please go to "
								+ WatchRxUtils.getServingUrl() + " to care.";

						try {
							email.sendEmail(Constants.Email.ADMINEMAIL, inviteReq.getEmail(),
									inviteReq.getFirstName() + " " + inviteReq.getLastName(),
									"Invite to care for patients", body);
						} catch (AddressException e) {
							e.printStackTrace();
							msg[0] = "Could not send email";
							resp.setMessages(msg);
						} catch (MessagingException e) {
							e.printStackTrace();
							msg[0] = "Could not send email";
							resp.setMessages(msg);
						} catch (UnsupportedEncodingException e) {
							e.printStackTrace();
							msg[0] = "Could not send email";
							resp.setMessages(msg);
						}
					}

				}
				msg[0] = " " + respMsg[0];
			}

		} else {
			WatchRxException excep = new WatchRxException(WatchRxExceptionCodes.EMPTY_PATIENTLIST);

			// msg[0] = Constants.ResponseString.INTERNALERROR;
			msg[0] = excep.getErrDesc();
			resp.setMessages(msg);
			resp.setSuccess(false);
		}
		return resp;
	}

	@RequestMapping(value = "/getAlertsByPatinetIdsss", method = RequestMethod.POST)
	public @ResponseBody PatientAlertResponse getAlertsByType(HttpServletRequest request,
			@RequestBody AlertRequestVO alertReq) throws Exception {

		logger.info("inside getAlertsByType:::: ");
		System.out.println("GET ALERTS BY TYPE");

		int index = alertReq.getIndex();
		int pageSize = alertReq.getItemsPerPage();

		PatientAlertResponse patientAlerts = new PatientAlertResponse();
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<PatientWatchCaregiverInfoVO> data = new ArrayList<>();
		List<PatientAlertInfoVO> alertInfoList = new ArrayList<>();
		List<PatientVO> patientList = new ArrayList<>();
		AlertRequestVO tmpReq = alertReq;
		logger.info("Alert Type: " + tmpReq.getAlertType());
		try {
			if (user.getRoleType() == 4) {
				GetAlerts alert = new GetAlerts();
				if (tmpReq.getAlertType() != null) {
					alert.setAlertType(alertReq.getAlertType());
				}

				if (tmpReq.getStartDate() != null || tmpReq.getStartDate() != "") {
					alert.setStartDate(tmpReq.getStartDate());
				}
				if (tmpReq.getEndDate() != null || tmpReq.getEndDate() != " ") {
					alert.setEndDate(tmpReq.getEndDate());
				}
				System.out.println("Start & End Date: " + alert.getStartDate() + " " + alert.getEndDate());
				/*
				 * String[] ids = null; System.out.println("Ids length: " + ids.length);
				 */

				List<Long> listofpatinet = new ArrayList<>();

				/*
				 * if (ids.length == 0) {
				 * System.out.println("In the if loop when ids length is 0"); patientList =
				 * patientService.getPatientList(user); for (PatientVO patient : patientList) {
				 * listofpatinet.add(patient.getPatientId()); } } else { for (int i = 0; i <
				 * ids.length; i++) { System.out.println(" In for loop. Patient Id: " + ids[i]);
				 * PatientVO tmpPatient = patientService.getPatient(Long.parseLong(ids[i]));
				 * listofpatinet.add(Long.parseLong(ids[i])); patientList.add(tmpPatient); } }
				 */
				List<MedicationAlert> tmpdata = WatchRxFactory.getAlertService().getAllAlertsByPatientId(listofpatinet,
						index, pageSize, alert);
				patientAlerts.setTotalCount(
						WatchRxFactory.getAlertService().getTotalCountOfAlertsByPatientId(listofpatinet, alert));

				for (PatientVO patient : patientList) {
					System.out.println("Inside patient list");
					logger.info("Alert data patient id" + patient.getPatientId());
					if (patient.getWatch() != null) {
						PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
						pwcInfo.setPatientId(patient.getPatientId().toString());
						String name = patient.getFirstName() + " " + patient.getLastName();
						pwcInfo.setPatientNamePhone(name + " " + patient.getPhoneNumber());
						AddressVO address = patient.getAddress();
						String patientAdd = address.getAddress1() + " " + address.getCity() + " " + address.getState();
						pwcInfo.setPatientAddress(patientAdd);
						pwcInfo.setCaregiverName(patient.getClinician().getFirstName());
						pwcInfo.setAssignedWatch(patient.getWatch().getWatchMake());
						data.add(pwcInfo);
						alert.setPatientId(patient.getPatientId().toString());
						// System.out.println("Alert Object: "+
						// alert.getAlertType()
						// + " "+alert.getStartDate()+ " "+alert.getEndDate());

					}
				}

				logger.info("Alert data patient id size " + tmpdata.size());
				for (MedicationAlert medAlert : tmpdata) {
					PatientAlertInfoVO alertInfo = new PatientAlertInfoVO();
					alertInfo.setDescription(medAlert.getAlertDescription());
					// String dateTime = medAlert.getCreatedDate().toString();
					// alertInfo.setDateTime(dateTime + " " + medAlert.getMissedTime());
					alertInfo.setSeverity(medAlert.getAlertType());

					SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					String dateTime = dateFormat.format(medAlert.getCreatedDate());
					// System.out.println("Format Date "+dateTime);

					// alertInfo.setDateTime(dateTime + " " +
					// medAlert.getMissedTime());
					alertInfo.setDateTime(dateTime);

					alertInfo.setPatientInfo(medAlert.getPatientName());
					alertInfoList.add(alertInfo);
				}

				patientAlerts.setPatientAlert(alertInfoList);
				patientAlerts.setPatientWatchCaregiver(data);
				logger.info("Alertr data" + data.toString());
				logger.info("Alertr data size" + data.size());
				patientAlerts.setSuccess(true);
				patientAlerts.setResponseCode(Constants.ResponseCode.MEDICATIONALERTSFOUND);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.MEDICATIONALERTSFOUND;
				patientAlerts.setMessages(messages);
				return patientAlerts;
			} else {
				logger.info("Unauthorized access");
				if (user != null) {
					logger.info("Unauthorized access by user with username " + user.getUserName());
				}
				patientAlerts.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				patientAlerts.setMessages(messages);
				patientAlerts.setSuccess(false);
				return patientAlerts;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			patientAlerts.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			patientAlerts.setMessages(messages);
			patientAlerts.setSuccess(false);
			return patientAlerts;

		}

	}

	private HashMap<String, String> getLatAndLong(String address) {

		GeoResponse gpsResp;
		HashMap<String, String> latLong = new HashMap<>();
		try {
			gpsResp = WatchRxFactory.getGPSService().getLatitudeAndLongitude(address);

			if (gpsResp.getStatus().equals("OK")) {
				com.medsure.util.Result[] res = gpsResp.getResults();
				for (com.medsure.util.Result result : res) {
					latLong.put("Status", gpsResp.getStatus());
					latLong.put("Latitude", result.getGeometry().getLocation().getLat());
					System.out.println("Latitude of address is :" + result.getGeometry().getLocation().getLat());
					latLong.put("Longitude", result.getGeometry().getLocation().getLng());
					System.out.println("Longitude of address is :" + result.getGeometry().getLocation().getLng());
					System.out.println("Location is " + result.getGeometry().getLocation_type());
					String longLat = "Lat: " + result.getGeometry().getLocation().getLat() + ", Long:"
							+ result.getGeometry().getLocation().getLng();

					logger.info("Longitude And LatitudelongLat::::::::: " + longLat);
				}
			} else {
				latLong.put("Status", gpsResp.getStatus());
				logger.info("Exception::::::::" + gpsResp.getStatus());
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return latLong;
	}

	@RequestMapping(value = "/getAlertsByClinicians/{index}/{pageSize}", method = RequestMethod.GET)
	@ResponseBody
	public PatientAlertResponse getAlertsByClinicians(HttpServletRequest request, @PathVariable Integer index,
			@PathVariable Integer pageSize) throws Exception {
		logger.info("inside getAlertByClinician:::: ");
		PatientAlertResponse patientAlerts = new PatientAlertResponse();
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<PatientWatchCaregiverInfoVO> data = new ArrayList<>();
		List<PatientAlertInfoVO> alertInfoList = new ArrayList<>();
		try {
			GetAlerts alert = new GetAlerts();
			logger.info("Alert data user id" + user.getUserId());
			logger.info("Alert data user id" + user.getUserName());
			List<PatientVO> patientList = patientService.getPatientList(user);
			List<Long> listofpatinet = new ArrayList<>();
			for (PatientVO patient : patientList) {
				listofpatinet.add(patient.getPatientId());
			}
			if (listofpatinet.size() > 0) {
				List<MedicationAlert> tmpdata = WatchRxFactory.getAlertService().getAllAlertsByPatientId(listofpatinet,
						index, pageSize, alert);
				patientAlerts.setTotalCount(
						WatchRxFactory.getAlertService().getTotalCountOfAlertsByPatientId(listofpatinet, alert));

				for (PatientVO patient : patientList) {
					logger.info("Alert data patient id" + patient.getPatientId());
					PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
					List<ClinicianVO> clinicianList = new ArrayList<>();
					clinicianList = clinicianService.getCaseManagersByPatient(patient.getPatientId());
					String names = new String();

					if (clinicianList.size() > 0 && clinicianList.size() == 1) {
						pwcInfo.setCaregiverName(
								clinicianList.get(0).getFirstName() + " " + clinicianList.get(0).getLastName());
					} else {
						for (int i = 0; i < clinicianList.size(); i++) {
							if (i == 0) {
								names = clinicianList.get(i).getFirstName() + " " + clinicianList.get(i).getLastName();
							} else {
								names = names + " /" + clinicianList.get(i).getFirstName() + " "
										+ clinicianList.get(i).getLastName();
							}
						}
						pwcInfo.setCaregiverName(names);

					}
					pwcInfo.setPatientId(patient.getPatientId().toString());
					String name = patient.getFirstName() + " " + patient.getLastName();
					pwcInfo.setPatientNamePhone(name + ", " + patient.getPhoneNumber());
					patient.getAddress();
					// String patientAdd = address.getAddress1() + ", " +
					// address.getCity() + ", " + address.getState();
					pwcInfo.setPatientAddress(patient.getPatientAddress());
					if (patient.getWatch() != null) {
						if (patient.getWatch().getWatchMake() != null) {
							pwcInfo.setAssignedWatch(patient.getWatch().getWatchMake());
						}
					}
					// pwcInfo.setAssignedWatch(patient.getWatch().getModel());
					data.add(pwcInfo);
					alert.setPatientId(patient.getPatientId().toString());
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
					Calendar calendar = Calendar.getInstance();
					alert.setEndDate(sdf.format(calendar.getTime()));
					// alert.setStartDate(sdf.format(calendar.getTime()));
					System.out.println("End Date: " + alert.getEndDate());
					calendar.add(Calendar.MONTH, -5);
					System.out.println("Start Date : " + sdf.format(calendar.getTime()));
					alert.setStartDate(sdf.format(calendar.getTime()));
					// alert.setEndDate(sdf.format(calendar.getTime()));

				}

				logger.info("Alert data patient id size " + tmpdata.size());
				for (MedicationAlert medAlert : tmpdata) {
					PatientAlertInfoVO alertInfo = new PatientAlertInfoVO();
					alertInfo.setDescription(medAlert.getAlertDescription());

					SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

					String dateTime = dateFormat.format(medAlert.getCreatedDate());

					alertInfo.setDateTime(dateTime);

					alertInfo.setSeverity(medAlert.getAlertType());
					alertInfo.setPatientInfo(medAlert.getPatientName());
					alertInfoList.add(alertInfo);
				}

				patientAlerts.setPatientAlert(alertInfoList);
				// System.out.println("Alert Info: "+ alertInfoList.toString());
				// System.out.println("Patient Watch Caregiver Info:
				// "+data.toString());
				patientAlerts.setPatientWatchCaregiver(data);
				logger.info("Alertr data" + data.toString());
				logger.info("Alertr data size" + data.size());
				patientAlerts.setSuccess(true);
				patientAlerts.setResponseCode(Constants.ResponseCode.MEDICATIONALERTSFOUND);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.MEDICATIONALERTSFOUND;
				patientAlerts.setMessages(messages);
				return patientAlerts;
			} else {
				patientAlerts.setResponseCode(Constants.ResponseCode.PATIENTNOTFOUND);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.PATIENTNOTFOUND;
				patientAlerts.setMessages(messages);
				patientAlerts.setSuccess(false);
				return patientAlerts;
			}

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			patientAlerts.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			patientAlerts.setMessages(messages);
			patientAlerts.setSuccess(false);
			return patientAlerts;

		}
	}

	// @RequestMapping(value = "/alertSummaryDBrdByDate", method =
	// RequestMethod.POST)
	public @ResponseBody void alertSummaryDashBoardByDate(HttpServletRequest request,
			@RequestBody MedicationAlertRequestVO alertRequest) throws Exception {
		/*
		 * logger.info("inside alertSummaryDBrdByDate:::: ");
		 *
		 * MedicationAlertListResponse response = new MedicationAlertListResponse(); try
		 * { UserVO user = null; user = (UserVO)
		 * request.getSession().getAttribute("user"); if (user != null &&
		 * user.getRoleType() == Constants.UserType.ADMIN) { List<MedicationAlert> data;
		 * List<PatientAlertInfoVO> modifiedData = new ArrayList<PatientAlertInfoVO>();
		 * data = WatchRxFactory.getAlertService().getAllAlertsByDate(alertRequest.
		 * getCreationStartDate(), alertRequest.getCreationEndDate());
		 *
		 * Iterator<MedicationAlert> dataIterator = data.iterator(); PatientAlertInfoVO
		 * currentPatientAlertInfoVO; MedicationAlert currentMedicationAlert; while
		 * (dataIterator.hasNext()) { currentPatientAlertInfoVO = new
		 * PatientAlertInfoVO(); currentMedicationAlert = dataIterator.next();
		 * currentPatientAlertInfoVO.setDescription(currentMedicationAlert.
		 * getAlertDescription());
		 * currentPatientAlertInfoVO.setSeverity(currentMedicationAlert.getAlertType());
		 *
		 * currentPatientAlertInfoVO.setDateTime(currentMedicationAlert.getCreatedDate()
		 * .toString());
		 * currentPatientAlertInfoVO.setPatientInfo(currentMedicationAlert.
		 * getPatientName());
		 *
		 * modifiedData.add(currentPatientAlertInfoVO); }
		 *
		 * response.setMedicationalertList(modifiedData); response.setSuccess(true);
		 * response.setResponseCode(Constants.ResponseCode.MEDICATIONALERTSFOUND);
		 * String[] messages = new String[1]; messages[0] =
		 * Constants.ResponseString.MEDICATIONALERTSFOUND;
		 * response.setMessages(messages); return response; } else {
		 * logger.info("Unauthorized access"); if (user != null) {
		 * logger.info("Unauthorized access by user with username " +
		 * user.getUserName()); }
		 * response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS); String
		 * messages[] = new String[1]; messages[0] =
		 * Constants.ResponseString.UNAUTHORIZEDACCESS; response.setMessages(messages);
		 * response.setSuccess(false); return response; }
		 *
		 * } catch (Exception e) { logger.error(e.getMessage(), e);
		 * response.setResponseCode(Constants.ResponseCode.INTERNALERROR); String
		 * messages[] = new String[1]; messages[0] =
		 * Constants.ResponseString.INTERNALERROR; response.setMessages(messages);
		 * response.setSuccess(false); return response; }
		 */}

	@RequestMapping(value = "/getMedicationInfoById", method = RequestMethod.GET)
	@ResponseBody
	public ModelAndView medicationInfoByListOfIds(MedicationIds details) {
		logger.info("inside medicationInfoByListOfIds:::: " + details.getMedicationIds());
		ModelAndView mav = new ModelAndView();

		List<MedicationInfo> result = WatchRxFactory.getAlertService()
				.getMedicationByMedicationIds(details.getMedicationIds());
		/*
		 * for(MedicationInfo info : result){
		 * mav.addObject("beforeOrAfterFood",info.getBeforeOrAfterFood());
		 * mav.addObject("color",info.getColor());
		 * mav.addObject("daysOfWeek",info.getDaysOfWeek());
		 * mav.addObject("description",info.getDescription());
		 * mav.addObject("dosage",info.getDosage());
		 * mav.addObject("image",info.getImage());
		 * mav.addObject("medicineName",info.getMedicineName());
		 * mav.addObject("strength",info.getStrength());
		 * mav.addObject("timeSlots",info.getTimeSlots()); }
		 */
		logger.info("result size :: " + result.size());
		mav.setViewName("medMissedSummary");
		mav.addObject("infoList", result);
		return mav;
	}

	private void sendMedicationInfo(PatientPrescriptionVO prescriptionVO) {
		logger.info("Sending medication info GCM::");
		CareGiverTest sdetails = new CareGiverTest();
		RegisterWatch watch = new RegisterWatch();
		PatientVO pdvo = WatchRxFactory.getPatientService().getPatient(prescriptionVO.getPatient().getPatientId());
		logger.info("Sending medication info patient id::" + prescriptionVO.getPatient().getPatientId());
		if (pdvo != null && pdvo.getWatch() != null) {
			String imeiNo = pdvo.getWatch().getImeiNumber();
			if (imeiNo != null) {
				logger.info("Sending medication info GCM IMEI NO::" + imeiNo);
				watch.setImeiNo(imeiNo);

				PatientDetails pd = WatchRxFactory.getPatientService().getPatientDetailsByImei(watch);

				PatientInfo gcmId = WatchRxFactory.getPatientService()
						.getPatientById(prescriptionVO.getPatient().getPatientId());
				logger.info("Sending medication info GCM ID::" + gcmId);
				if (gcmId.getGcmId() != null && gcmId.getGcmId().length() > 0) {
					JsonObject jo = new JsonObject();
					jo.addProperty("messageType", "UpdatedMedicationInfo");
					jo.addProperty("patientDetails", pd.toString());
					logger.info("Sending medication info GCM ID details::" + pd.toString());

					// jo.addProperty("visitVerificationCode", tempString);
					Gson gson = new Gson();
					String jsonStr = gson.toJson(jo);
					try {
						sdetails.sendMessageToPatient("message", jsonStr, gcmId.getGcmId(), gcmId.getPlatform());
						logger.info("Sending medication info GCM ID Send::");

					} catch (IOException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}
	}

	private void sendMedicationInfoByPatientId(Long patientId) {

		logger.info("Sending medication info GCM::");
		CareGiverTest sdetails = new CareGiverTest();
		RegisterWatch watch = new RegisterWatch();
		PatientVO pdvo = WatchRxFactory.getPatientService().getPatient(patientId);
		logger.info("Sending medication info patient id::" + patientId);
		if (pdvo != null && pdvo.getWatch() != null) {
			String imeiNo = pdvo.getWatch().getImeiNumber();
			if (imeiNo != null) {
				logger.info("Sending medication info GCM IMEI NO::" + imeiNo);
				watch.setImeiNo(imeiNo);

				PatientDetails pd = WatchRxFactory.getPatientService().getPatientDetailsByImei(watch);

				PatientInfo gcmId = WatchRxFactory.getPatientService().getPatientById(patientId);
				logger.info("Sending medication info GCM ID::" + gcmId.getGcmId());
				if (gcmId.getGcmId() != null && gcmId.getGcmId().trim().length() > 0) {
					JsonObject jo = new JsonObject();
					jo.addProperty("messageType", "UpdatedMedicationInfo");
					jo.addProperty("patientDetails", pd.toString());
					logger.info("Sending medication info GCM ID details::" + pd.toString());

					// jo.addProperty("visitVerificationCode", tempString);
					Gson gson = new Gson();
					String jsonStr = gson.toJson(jo);
					try {
						sdetails.sendMessageToPatient("message", jsonStr, gcmId.getGcmId(), gcmId.getPlatform());
						logger.info("Sending medication info GCM ID Send::");

					} catch (IOException e) {
						// TODO Auto-generated catch block
						logger.error("Error in sending GCM message:: " + e.getMessage());
						e.printStackTrace();
					}
				}
			}
		}
	}

	private void sendMedicationInfoByPrsecriptionId(Long prescriptionId) {

		Long patientId = patientService.getPatientPrescription(prescriptionId).getPatient().getPatientId();
		logger.info("Sending medication info GCM::");
		CareGiverTest sdetails = new CareGiverTest();
		RegisterWatch watch = new RegisterWatch();
		PatientVO pdvo = WatchRxFactory.getPatientService().getPatient(patientId);
		logger.info("Sending medication info patient id::" + patientId);
		if (pdvo != null && pdvo.getWatch() != null) {
			String imeiNo = pdvo.getWatch().getImeiNumber();
			if (imeiNo != null) {
				logger.info("Sending medication info GCM IMEI NO::" + imeiNo);
				watch.setImeiNo(imeiNo);

				PatientDetails pd = WatchRxFactory.getPatientService().getPatientDetailsByImei(watch);

				PatientInfo gcmId = WatchRxFactory.getPatientService().getPatientById(patientId);
				logger.info("Sending medication info GCM ID::" + gcmId);
				if (gcmId.getGcmId() != null && gcmId.getGcmId().trim().length() > 0) {
					JsonObject jo = new JsonObject();
					jo.addProperty("messageType", "UpdatedMedicationInfo");
					jo.addProperty("patientDetails", pd.toString());
					logger.info("Sending medication info GCM ID details::" + pd.toString());

					// jo.addProperty("visitVerificationCode", tempString);
					Gson gson = new Gson();
					String jsonStr = gson.toJson(jo);
					try {
						sdetails.sendMessageToPatient("message", jsonStr, gcmId.getGcmId(), gcmId.getPlatform());
						logger.info("Sending medication info GCM ID Send::");

					} catch (IOException e) {
						// TODO Auto-generated catch block
						logger.error("Something went wrong while sending GCM: " + e.getMessage());
						e.printStackTrace();
					}
				}
			}
		}
	}

	@RequestMapping(value = "/alertSummaryByPatient", method = RequestMethod.GET)
	@ResponseBody
	public String alertSummaryByPatient(HttpServletRequest request, String patientId) throws Exception {
		logger.info("inside alertSummarydashBoard as graph:::: ");
		List<MedicationAlert> data = new ArrayList<>();
		Map<Date, Integer> finalData = null;
		if (patientId.equalsIgnoreCase("All")) {
			finalData = new HashMap<>();
			data = WatchRxFactory.getAlertService().getAllAlertsByPatient();
			for (Iterator<MedicationAlert> iterator = data.iterator(); iterator.hasNext();) {
				MedicationAlert medicationAlert = iterator.next();
				if (medicationAlert.getAlertType() != null
						&& medicationAlert.getAlertType().equalsIgnoreCase("MissedRegularReminder")) {
					Date date = DateUtils.truncate(medicationAlert.getCreatedDate(), java.util.Calendar.DAY_OF_MONTH);
					if (finalData.get(date) != null) {
						finalData.put(date, finalData.get(date) + 1);
					} else {
						finalData.put(date, 1);

					}
				}
			}
		} else {
			GetAlerts alert = new GetAlerts();
			alert.setPatientId(patientId);
			finalData = new HashMap<>();
			data = WatchRxFactory.getAlertService().getAlertsByPatientId(alert);
			for (Iterator<MedicationAlert> iterator = data.iterator(); iterator.hasNext();) {
				MedicationAlert medicationAlert = iterator.next();
				if (medicationAlert.getAlertType() != null
						&& medicationAlert.getAlertType().equalsIgnoreCase("MissedRegularReminder")) {
					Date date = DateUtils.truncate(medicationAlert.getCreatedDate(), java.util.Calendar.DAY_OF_MONTH);
					if (finalData.get(date) != null) {
						finalData.put(date, finalData.get(date) + 1);
					} else {
						finalData.put(date, 1);

					}
				}
			}

		}
		logger.info("Alertr data" + data.toString());
		logger.info("Alertr data size" + data.size());
		Map<Date, Integer> m1 = new TreeMap<>(finalData);
		JsonArray jArray = new JsonArray();
		for (Entry<Date, Integer> e : m1.entrySet()) {
			JsonObject jo = new JsonObject();
			jo.addProperty("date", convertDate(e.getKey().toString()));
			jo.addProperty("alertCount", e.getValue());
			jArray.add(jo);
		}

		return jArray.toString();
	}

	private String convertDate(String date) {
		// String dateStr = "Mon Jun 18 00:00:00 IST 2012";
		DateFormat formatter = new SimpleDateFormat("E MMM dd HH:mm:ss Z yyyy");
		Date date1 = null;
		try {
			date1 = formatter.parse(date);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		// System.out.println(date);

		Calendar cal = Calendar.getInstance();
		cal.setTime(date1);
		String formatedDate = cal.get(Calendar.DATE) + "/" + (cal.get(Calendar.MONTH) + 1) + "/"
				+ cal.get(Calendar.YEAR);
		// System.out.println("formatedDate : " + formatedDate);
		return formatedDate;
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

	@RequestMapping(value = "/savePatientBasicInfo", method = RequestMethod.POST)
	public @ResponseBody SavePatientInfoResponseVO savePatientBasicInfo(HttpServletRequest request,
			@RequestBody CreatePatientRequestBasicInformationVO patient) throws Exception {
		SavePatientInfoResponseVO response = new SavePatientInfoResponseVO();
		logger.info("SavePatientInfoResponseVO savePatientBasicInfo");
		logger.info("patient.getGender : " + patient.getGender());
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				if (patient.getPrimaryCaseManagerId() != null) {
					if (patient.getDob() != null) {
						if (patientService.isPatientExist(patient.getFirstname(), patient.getLastname(), patient.getDob(),
								patient.getAddress1()) > 0) {
							response.setResponseCode(Constants.ResponseCode.PATIENTCREATIONFAILED);
							String messages[] = new String[1];
							messages[0] = "Patient already exists";
							response.setMessages(messages);
							response.setSuccess(false);
							return response;
						}
					}
					if (patient.getEmail() != null && !patient.getEmail().trim().isEmpty()) {
					if (!patientService.isPatientEmailExist(patient.getEmail())) {
						response.setResponseCode(Constants.ResponseCode.PATIENTCREATIONFAILED);
						String messages[] = new String[1];
						messages[0] = "Email is already assocaiated with other patient.";
						response.setMessages(messages);
						response.setSuccess(false);
						return response;
					}
					}
					CreatePatientBasicInformationResponseVO savedPatient = patientService.savePatientBasicInfo(patient,
							user);

					response.setPatient(savedPatient);
					response.setSuccess(true);
					response.setResponseCode(Constants.ResponseCode.PATIENTSAVED);
					String[] messages = new String[1];
					messages[0] = Constants.ResponseString.PATIENTSAVED;
					response.setMessages(messages);
					return response;
				} else {
					logger.info("Primary Case Manager is not provided");

					response.setResponseCode(Constants.ResponseCode.PATIENTCREATIONFAILED);
					String messages[] = new String[1];
					messages[0] = "Primary Case Manager is not provided";
					response.setMessages(messages);
					response.setSuccess(false);
					return response;
				}

			} else {
				logger.info("Unauthorized access");

				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}

		} catch (ParseException e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INCORRECTDATEFORMAT);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INCORRECTDATEFORMAT;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}

	/*
	 * @RequestMapping(value = "/getPatientBasicInfo", method = RequestMethod.POST)
	 * public @ResponseBody GetPatientBasicInfoResponseVO
	 * getPatientBasicInfo(HttpServletRequest request,
	 *
	 * @RequestBody CreatePatientRequestBasicInformationVO patient) throws Exception
	 * { GetPatientBasicInfoResponseVO response = new
	 * GetPatientBasicInfoResponseVO();
	 *
	 *
	 * try { UserVO user = null; user = (UserVO)
	 * request.getSession().getAttribute("user"); if (user != null &&
	 * user.getRoleType() == Constants.UserType.CAREGIVER) {
	 *
	 * CreatePatientBasicInformationResponseVO savedPatient =
	 * patientService.getPatientBasicInfo(patient); if(savedPatient == null){
	 * response.setPatient(savedPatient); response.setSuccess(false);
	 * response.setResponseCode(Constants.ResponseCode.PATIENTNOTFOUND); String[]
	 * messages = new String[1]; messages[0] =
	 * Constants.ResponseString.PATIENTNOTFOUND; response.setMessages(messages);
	 * }else{ response.setPatient(savedPatient); response.setSuccess(true);
	 * response.setResponseCode(Constants.ResponseCode.PATIENTSAVED); String[]
	 * messages = new String[1]; messages[0] =
	 * Constants.ResponseString.PATIENTSAVED; response.setMessages(messages); }
	 * return response; } else { logger.info("Unauthorized access"); if (user !=
	 * null) { logger.info("Unauthorized access by user with username " +
	 * user.getUserName()); }
	 * response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS); String
	 * messages[] = new String[1]; messages[0] =
	 * Constants.ResponseString.UNAUTHORIZEDACCESS; response.setMessages(messages);
	 * response.setSuccess(false); return response; }
	 *
	 * } catch (Exception e) { logger.error(e.getMessage(), e);
	 * response.setResponseCode(Constants.ResponseCode.INTERNALERROR); String
	 * messages[] = new String[1]; messages[0] =
	 * Constants.ResponseString.INTERNALERROR; response.setMessages(messages);
	 * response.setSuccess(false); return response; }
	 *
	 * }
	 */

	@RequestMapping(value = "/savePatientDailySchedule", method = RequestMethod.POST)
	public @ResponseBody SavePatientInfoResponseVO savePatientDailySchedule(HttpServletRequest request,
			@RequestBody CreatePatientRequestVO patient) throws Exception {
		SavePatientInfoResponseVO response = new SavePatientInfoResponseVO();

		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {

				response = patientService.savePatientBasicAndEmergencyAndDailyScheduleInfo(patient, user);

				response.setSuccess(true);
				response.setResponseCode(Constants.ResponseCode.PATIENTSAVED);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.PATIENTSAVED;
				response.setMessages(messages);
				return response;
			} else {
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}

		} catch (ParseException e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INCORRECTDATEFORMAT);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INCORRECTDATEFORMAT;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (WatchRxServiceException e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.PATIENTCREATIONFAILED);
			String messages[] = new String[1];
			messages[0] = "Provided data is already assocaiated with other patient.";
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}

	@RequestMapping(value = "/savePatientDailyScheduleInsurance", method = RequestMethod.POST)
	public @ResponseBody SavePatientInfoResponseVO savePatientDailyScheduleAndInsurance(HttpServletRequest request,
			@RequestBody CreatePatientRequestVO patient) throws Exception {
		SavePatientInfoResponseVO response = new SavePatientInfoResponseVO();

		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {

				response = patientService.savePatientBasicAndEmergencyAndDailyScheduleInfoInsurance(patient, user);

				response.setSuccess(true);
				response.setResponseCode(Constants.ResponseCode.PATIENTSAVED);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.PATIENTSAVED;
				response.setMessages(messages);
				return response;
			} else {
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}

		} catch (ParseException e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INCORRECTDATEFORMAT);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INCORRECTDATEFORMAT;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (WatchRxServiceException e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.PATIENTCREATIONFAILED);
			String messages[] = new String[1];
			messages[0] = "Provided data is already assocaiated with other patient.";
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}

	@RequestMapping(value = "/savePatientDailyScheduleInsuranceChronic", method = RequestMethod.POST)
	public @ResponseBody SavePatientInfoResponseVO savePatientDailyScheduleAndInsuranceChronicCondtions(
			HttpServletRequest request, @RequestBody CreatePatientRequestVO patient) throws Exception {
		SavePatientInfoResponseVO response = new SavePatientInfoResponseVO();

		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {

				response = patientService.savePatientBasicAndEmergencyAndDailyScheduleInfoInsuranceAndChronic(patient,
						user);

				response.setSuccess(true);
				response.setResponseCode(Constants.ResponseCode.PATIENTSAVED);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.PATIENTSAVED;
				response.setMessages(messages);
				return response;
			} else {
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}

		} catch (ParseException e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INCORRECTDATEFORMAT);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INCORRECTDATEFORMAT;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (WatchRxServiceException e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.PATIENTCREATIONFAILED);
			String messages[] = new String[1];
			messages[0] = "Provided data is already assocaiated with other patient.";
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}

	@RequestMapping(value = "/savePatientEmergencyContacts", method = RequestMethod.POST)
	public @ResponseBody SavePatientInfoResponseVO savePatientEmergencyContacts(HttpServletRequest request,
			@RequestBody CreatePatientRequestVO patient) throws Exception {
		SavePatientInfoResponseVO response = new SavePatientInfoResponseVO();

		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {

				response = patientService.savePatientBasicAndEmergencyInfo(patient, user);

				response.setSuccess(true);
				response.setResponseCode(Constants.ResponseCode.PATIENTSAVED);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.PATIENTSAVED;
				response.setMessages(messages);
				return response;
			} else {

				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}

		} catch (ParseException e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INCORRECTDATEFORMAT);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INCORRECTDATEFORMAT;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (WatchRxServiceException e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.PATIENTCREATIONFAILED);
			String messages[] = new String[1];
			messages[0] = "Provided data is already assocaiated with other patient.";
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}

	@RequestMapping(value = "/saveNewEmergencyContact", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveNewEmergencyContact(HttpServletRequest request,
			@RequestBody UpdatePatientEmergencyContactsVO emergencyContact) throws Exception {
		StatusVO resp = new StatusVO();
		List<CreatePatientRequestEmergencyContactVO> emergencyContacts = emergencyContact.getEmergencyContacts();
		PatientVO patient = new PatientVO();
		patient.setPatientId(emergencyContact.getPatientId());
		CreatePatientRequestEmergencyContactListVO contacts = new CreatePatientRequestEmergencyContactListVO();
		contacts.setEmergencyContacts(emergencyContacts);
		patientService.saveEmergencyContacts(patient, contacts);

		boolean success = true;
		resp.setSuccess(success);
		return resp;
	}

	@RequestMapping(value = "/saveNewPERSContact", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveNewPERSContact(HttpServletRequest request,
			@RequestBody UpdatePatientEmergencyContactsVO emergencyContact) throws Exception {
		StatusVO resp = new StatusVO();
		List<CreatePatientRequestEmergencyContactVO> emergencyContacts = emergencyContact.getEmergencyContacts();
		PatientVO patient = new PatientVO();
		patient.setPatientId(emergencyContact.getPatientId());
		CreatePatientRequestEmergencyContactListVO contacts = new CreatePatientRequestEmergencyContactListVO();
		contacts.setEmergencyContacts(emergencyContacts);
		patientService.saveEmergencyContacts(patient, contacts);

		boolean success = true;
		resp.setSuccess(success);
		return resp;
	}

	@RequestMapping(value = "/savePatientCustomAlerts", method = RequestMethod.POST)
	public @ResponseBody StatusVO savePatientCustomAerts(HttpServletRequest request,
			@RequestBody CustomAlertVO customAlertsInfoVO) throws Exception {

		logger.info("inside savePatient Custom alerts:::: ");
		StatusVO response = new CreatePatientResponseVO();

		List<CustomAlertsForPatientVO> alertsForPatientVOs = customAlertsInfoVO.getCustomAlerts();
		PatientVO patient = new PatientVO();
		patient.setPatientId(customAlertsInfoVO.getPatientId());

		CustomAlertVO customAlertsForPatientVO = new CustomAlertVO();

		customAlertsForPatientVO.setCustomAlerts(alertsForPatientVOs);

		patientService.saveCustomAlerts(patient, customAlertsForPatientVO);

		boolean success = true;
		response.setSuccess(success);

		return response;
	}

	@RequestMapping(value = "/getPatientCustomAlerts", method = RequestMethod.POST)
	public @ResponseBody List<CustomAlertsForPatientVO> getPatientCustomAlerts(HttpServletRequest request,
			@RequestBody PatientVO patient) throws Exception {

		List<CustomAlertsForPatientVO> customAlertsForPatientVO = patientService
				.getCustomAlertInfo(patient.getPatientId());

		return customAlertsForPatientVO;
	}

	@RequestMapping(value = "/saveEditedCustomAlert", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveEditedCustomAlert(HttpServletRequest request,
			@RequestBody CustomAlertsForPatientVO customAlertsForPatientVO) throws Exception {
		StatusVO resp = new StatusVO();
		resp.setSuccess(patientService.saveExistingCustomAlert(customAlertsForPatientVO));

		sendCustomAlertInfoByPatientId(Long.valueOf(customAlertsForPatientVO.getPatientId()));

		return resp;
	}

	@RequestMapping(value = "/saveNewCustomAlerts", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveNewCustomAlerts(HttpServletRequest request,
			@RequestBody CustomAlertVO customAlertsInfoVO) throws Exception {

		logger.info("inside savePatient Custom alerts:::: ");
		StatusVO response = new CreatePatientResponseVO();

		List<CustomAlertsForPatientVO> alertsForPatientVOs = customAlertsInfoVO.getCustomAlerts();
		PatientVO patient = new PatientVO();
		patient.setPatientId(customAlertsInfoVO.getPatientId());

		CustomAlertVO customAlertsForPatientVO = new CustomAlertVO();

		customAlertsForPatientVO.setCustomAlerts(alertsForPatientVOs);

		patientService.saveCustomAlerts(patient, customAlertsForPatientVO);
		sendCustomAlertInfoByPatientId(customAlertsInfoVO.getPatientId());

		boolean success = true;
		response.setSuccess(success);

		return response;
	}

	private void sendCustomAlertInfoByPatientId(Long patientId) {

		logger.info("Sending send Custom Alert info by PatientId info GCM::");
		CareGiverTest sdetails = new CareGiverTest();
		RegisterWatch watch = new RegisterWatch();
		PatientVO pdvo = WatchRxFactory.getPatientService().getPatient(patientId);
		logger.info("Sending medication info patient id::" + patientId);
		if (pdvo != null && pdvo.getWatch() != null) {
			String imeiNo = pdvo.getWatch().getImeiNumber();
			if (imeiNo != null) {
				logger.info("Sending send Custom Alert  info by PatientId info GCM IMEI NO::" + imeiNo);
				watch.setImeiNo(imeiNo);

				PatientDetails pd = WatchRxFactory.getPatientService().getPatientDetailsByImei(watch);

				PatientInfo gcmId = WatchRxFactory.getPatientService().getPatientById(patientId);
				logger.info("Sending medication info GCM ID::" + gcmId);
				if (gcmId.getGcmId() != null && gcmId.getGcmId().trim().length() > 0) {
					JsonObject jo = new JsonObject();
					jo.addProperty("messageType", "UpdatedMedicationInfo");
					jo.addProperty("patientDetails", pd.toString());
					logger.info("Sending send Custom Alert  info by PatientId info GCM ID details::" + pd.toString());
					Gson gson = new Gson();
					String jsonStr = gson.toJson(jo);
					try {
						sdetails.sendMessageToPatient("message", jsonStr, gcmId.getGcmId(), gcmId.getPlatform());
						logger.info("Sending send Custom Alert  info by PatientId info GCM ID Send::");

					} catch (IOException e) {
						// TODO Auto-generated catch block
						logger.error("Something went wrong while sending GCM: " + e.getMessage());
						e.printStackTrace();
					}
				}
			}
		}
	}

	@RequestMapping(value = "/exportToXls", method = RequestMethod.GET)
	public void export(HttpServletRequest request, HttpServletResponse response)
			throws JsonMappingException, JsonProcessingException {

		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);

		List<PatientVO> patientList = patientService.getPatientList(user);
		List<Long> listofpatinet = new ArrayList<>();
		for (PatientVO patient : patientList) {
			listofpatinet.add(patient.getPatientId());
		}

		List<MedicationAlert> tmpdata = null;
		if (listofpatinet.size() > 0) {
			tmpdata = WatchRxFactory.getAlertService().getAllAlertsByClinician(listofpatinet, null, null);
		}

		List<String> headers = Arrays.asList("Date and Time", "Severity", "Patient Name", "Phone Number",
				"Alert Description ");

		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Date date = new Date();

		String fileName = user.getFirstName() + "_" + sdf.format(date) + ".xls";
		try {
			response.addHeader("Content-disposition", "attachment; filename=" + fileName);
			response.setContentType("application/vnd.ms-excel");
			new SimpleExporter().gridExport(headers, tmpdata,
					"dateTime,alertType,patientName,patientPhoneNumber,alertDescription,", response.getOutputStream());
			response.flushBuffer();
		} catch (IOException e) {
			System.out.println(e.getMessage());
		}
	}

	@RequestMapping(value = "/downloadCSV", method = RequestMethod.POST)
	public void downloadCSV(HttpServletRequest request, HttpServletResponse response,
			@RequestBody AlertRequestVO alertReq) throws IOException {
		try {
			DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Date date = new Date();

			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			String fileName = user.getFirstName() + "_" + sdf.format(date) + ".csv";

			response.setContentType("text/csv");
			response.setHeader("Content-disposition", "attachment;filename=" + fileName);

			ArrayList<String> rows = new ArrayList<>();
			rows.add("Date and Time,Severity,Patient Name,Phone Number,Alert Description");
			rows.add("\n");

			List<PatientVO> patientList = new ArrayList<>();
			Long[] ids = alertReq.getPatientId();
			List<Long> listofpatinet = new ArrayList<>();

			if (ids != null && ids.length == 0) {
				Long totlaNumberOfPat = patientService.getAllPatientsCountByOrgId(user.getRoleType(), user.getOrgId(),
						user.getUserId());
				patientList = patientService.getAllPatientsByOrgId(user.getOrgId(), user.getRoleType(), 0,
						totlaNumberOfPat.intValue(), user.getUserId());
				listofpatinet = patientList.stream().map(PatientVO::getPatientId).collect(Collectors.toList());
			} else {
				for (Long id : ids) {
					listofpatinet.add(id);
				}
			}
			List<MedicationAlert> tmpdata = new ArrayList<>();
			if (listofpatinet.size() > 0) {
				if (alertReq.getStartDate() != null && alertReq.getEndDate() != null) {
					tmpdata = WatchRxFactory.getAlertService().getAllAlertsByClinician(listofpatinet,
							alertReq.getStartDate(), alertReq.getEndDate());
				} else {
					tmpdata = WatchRxFactory.getAlertService().getAllAlertsByClinician(listofpatinet, null, null);
				}
			}

			for (MedicationAlert medAlert : tmpdata) {

				String dateTime = medAlert.getDateTime();
				String severity = medAlert.getAlertType();
				String patientName = medAlert.getPatientName();
				String patinetPhone = medAlert.getPatientPhoneNumber();
				String description = medAlert.getAlertDescription();

				String row = " " + dateTime + "," + severity + "," + patientName + "," + " " + patinetPhone + ","
						+ description;
				rows.add(row);
				rows.add("\n");
			}

			for (String outputString : rows) {
				PrintWriter out = response.getWriter();
				out.print(outputString);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@RequestMapping(value = "/downloadXls", method = RequestMethod.POST)
	public @ResponseBody List<MedicationAlert> getAlertsForXls(HttpServletRequest request,
			@RequestBody AlertRequestVO alertReq) throws Exception {

		List<MedicationAlert> tmpdata = new ArrayList<>();
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		if (user != null) {
			List<PatientVO> patientList = new ArrayList<>();
			Long[] ids = alertReq.getPatientId();
			List<Long> listofpatinet = new ArrayList<>();

			if (ids != null && ids.length == 0) {
				Long totlaNumberOfPat = patientService.getAllPatientsCountByOrgId(user.getRoleType(), user.getOrgId(),
						user.getUserId());
				patientList = patientService.getAllPatientsByOrgId(user.getOrgId(), user.getRoleType(), 0,
						totlaNumberOfPat.intValue(), user.getUserId());
				listofpatinet = patientList.stream().map(PatientVO::getPatientId).collect(Collectors.toList());
			} else {
				for (Long id : ids) {
					listofpatinet.add(id);
				}
			}

			if (listofpatinet.size() > 0) {
				if (alertReq.getStartDate() != null && alertReq.getEndDate() != null) {
					tmpdata = WatchRxFactory.getAlertService().getAllAlertsByClinician(listofpatinet,
							alertReq.getStartDate(), alertReq.getEndDate());
				} else {
					tmpdata = WatchRxFactory.getAlertService().getAllAlertsByClinician(listofpatinet, null, null);
				}
			}
		}
		return tmpdata;
	}

	@RequestMapping(value = "/sendTextToPatient", method = RequestMethod.POST)
	public @ResponseBody StatusVO sendTextMessageToNurse(HttpServletRequest request,
			@RequestBody ScheduleMessageVO scheduleRequest) throws Exception {

		logger.info("Caregiver status is: " + scheduleRequest.toString());

		CareGiverTest sdetails = new CareGiverTest();
		StatusVO resp = new StatusVO();

		String messages[] = new String[1];

		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null && user.getRoleType() == Constants.UserType.CAREGIVER) {

				PatientInfo gcmId = patientService.getPatientById(scheduleRequest.getPatientId());
				logger.info("GCM id is: " + gcmId.getGcmId());

				JsonObject jo = new JsonObject();
				jo.addProperty("messageType", "nurseOnTheWay");
				jo.addProperty("caregiverName", user.getFirstName());
				jo.addProperty("status", scheduleRequest.getMessage());
				Gson gson = new Gson();
				String jsonStr = gson.toJson(jo);
				logger.info("GCM message to be sent is " + jsonStr);
				sdetails.sendMessageToPatient("message", jsonStr, gcmId.getGcmId(), gcmId.getPlatform());

				boolean success = true;
				resp.setSuccess(success);
				messages[0] = "Message Sent Successfully";
				resp.setMessages(messages);
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			logger.info("Something went wrong in careGiverStatus method " + e.getMessage());
			e.printStackTrace();
			boolean success = false;
			resp.setSuccess(success);
			messages[0] = "Message Sent Failed";
			resp.setMessages(messages);
		}
		return resp;
	}

	@RequestMapping(value = "/saveschedulemessage", method = RequestMethod.POST)
	public @ResponseBody StatusVO savePatientScheduleMessage(HttpServletRequest request,
			@RequestBody ScheduleMessageVO scheduleRequest) throws Exception {

		logger.info("inside savePatientScheduleMessage:::: " + scheduleRequest.toString());
		StatusVO response = new CreatePatientResponseVO();
		PatientVO patient = new PatientVO();
		patient.setPatientId(scheduleRequest.getPatientId());
		response = patientService.saveScheduleMessage(patient, scheduleRequest);

		return response;
	}

	@RequestMapping(value = "/savePatientHeartRateInfo", method = RequestMethod.POST)
	public @ResponseBody StatusVO savePatientHeartRateInfo(HttpServletRequest request,
			@RequestBody HeartRateRequestVO heartRateVO) throws Exception {

		logger.info("inside savePatientHeartRateInfo " + heartRateVO);

		PatientVO patient = new PatientVO();
		patient.setPatientId(heartRateVO.getPatientId());
		StatusVO response = patientService.savePatientHeartInfoFromServer(patient, heartRateVO);

		response.setSuccess(true);
		return response;
	}

	@RequestMapping(value = "/getHeartRatesByPatient/{patientId}/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody HeartRatesResponse getHeartRatesByPatientId(HttpServletRequest request,
			@PathVariable Long patientId, @PathVariable Integer index, @PathVariable Integer pageSize) {
		logger.info("inside getHeartRatesByPatientId" + patientId);
		HeartRatesResponse ratesResponse = new HeartRatesResponse();
		HeartRateInfo heartRates = patientService.getHeartRate(patientId, index, pageSize);
		ratesResponse.setHeartRatesInfo(heartRates);
		ratesResponse.setResultCount(patientService.getHeartRateResultCount(patientId));
		ratesResponse.setStatus(true);
		return ratesResponse;

	}

	@RequestMapping(value = "/getHeartRateSetting/{patientId}", method = RequestMethod.GET)
	public @ResponseBody StatusVO getHeartRateSetting(HttpServletRequest request, @PathVariable Long patientId) {
		logger.info("inside getHeartRatesByPatientId" + patientId);
		StatusVO heartRates = patientService.getHeartRateSettingInfoByPatientId(patientId);
		return heartRates;

	}

	@RequestMapping(value = "/sendHeartRateRequestToPatient", method = RequestMethod.POST)
	public @ResponseBody StatusVO sendHeartRateRequestToPatient(HttpServletRequest request,
			@RequestBody HeartRateRequestVO heartRateVO) throws Exception {

		logger.info("Caregiver status is: " + heartRateVO.toString());

		CareGiverTest sdetails = new CareGiverTest();
		StatusVO resp = new StatusVO();

		String messages[] = new String[1];

		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				PatientInfo gcmId = patientService.getPatientById(heartRateVO.getPatientId());
				logger.info("GCM id is: " + gcmId.getGcmId());
				if (gcmId.getGcmId() != null) {
					JsonObject jo = new JsonObject();
					jo.addProperty("messageType", "sendHearRate");
					Gson gson = new Gson();
					String jsonStr = gson.toJson(jo);
					logger.info("GCM message to be sent is " + jsonStr);
					sdetails.sendMessageToPatient("message", jsonStr, gcmId.getGcmId(), gcmId.getPlatform());

					boolean success = true;
					resp.setSuccess(success);
					messages[0] = "Heart Rate Request Sent Successfully";
					resp.setMessages(messages);
				} else {
					boolean success = false;
					resp.setSuccess(success);
					messages[0] = "Heart Rate Request Failed";
					resp.setMessages(messages);
				}
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			logger.info("Something went wrong in careGiverStatus method " + e.getMessage());
			e.printStackTrace();
			boolean success = false;
			resp.setSuccess(success);
			messages[0] = "Message Sent Failed";
			resp.setMessages(messages);
		}
		return resp;
	}

	@RequestMapping(value = "/getpatientforclinician", method = RequestMethod.GET)
	@ResponseBody
	public CaregiverPatientsListVO getpatientforclinician(HttpServletRequest request) throws Exception {

		CaregiverPatientsListVO patientResponse = new CaregiverPatientsListVO();
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<PatientWatchCaregiverInfoVO> patientsList = new ArrayList<>();
		try {
			if (user.getRoleType() == 4 || user.getRoleType() == 5) {
				List<PatientVO> patientList = patientService.getPatientList(user);
				for (PatientVO patient : patientList) {
					PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
					List<ClinicianVO> clinicianList = new ArrayList<>();
					clinicianList = clinicianService.getCaseManagersByPatient(patient.getPatientId());
					String names = new String();

					if (clinicianList.size() > 0 && clinicianList.size() == 1) {
						pwcInfo.setCaregiverName(
								clinicianList.get(0).getFirstName() + " " + clinicianList.get(0).getLastName());
					} else {
						for (int i = 0; i < clinicianList.size(); i++) {
							if (i == 0) {
								names = clinicianList.get(i).getFirstName() + " " + clinicianList.get(i).getLastName();
							} else {
								names = names + " /" + clinicianList.get(i).getFirstName() + " "
										+ clinicianList.get(i).getLastName();
							}
						}
						pwcInfo.setCaregiverName(names);
					}
					pwcInfo.setPatientId(patient.getPatientId().toString());
					String name = patient.getFirstName() + " " + patient.getLastName();
					pwcInfo.setPatientNamePhone(name + ", " + patient.getPhoneNumber());
					pwcInfo.setPatientName(name);
					pwcInfo.setPatientAddress(patient.getPatientAddress());
					if (patient.getWatch() != null) {
						if (patient.getWatch().getWatchMake() != null) {
							pwcInfo.setAssignedWatch(patient.getWatch().getWatchMake());
						}
					}
					patientsList.add(pwcInfo);
				}

				patientResponse.setPatientWatchCaregiver(patientsList);
				patientResponse.setSuccess(true);
				patientResponse.setResponseCode(Constants.ResponseCode.MEDICATIONALERTSFOUND);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.MEDICATIONALERTSFOUND;
				patientResponse.setMessages(messages);
				return patientResponse;
			} else {
				patientResponse.setResponseCode(Constants.ResponseCode.PATIENTNOTFOUND);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.PATIENTNOTFOUND;
				patientResponse.setMessages(messages);
				patientResponse.setSuccess(false);
				return patientResponse;
			}
		} catch (Exception e) {
			patientResponse.setResponseCode(Constants.ResponseCode.PATIENTNOTFOUND);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.PATIENTNOTFOUND;
			patientResponse.setMessages(messages);
			patientResponse.setSuccess(false);
			return patientResponse;
		}
	}

	@RequestMapping(value = "/getAlertsByClinician/{index}/{pageSize}", method = RequestMethod.GET)
	@ResponseBody
	public PatientAlertResponse getAlertsByCaregiver(HttpServletRequest request, @PathVariable Integer index,
			@PathVariable Integer pageSize) throws Exception {
		PatientAlertResponse patientAlerts = new PatientAlertResponse();
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<PatientAlertInfoVO> alertInfoList = new ArrayList<>();
		try {
			if (user.getRoleType() == 4 || user.getRoleType() == 5) {
				GetAlerts alert = new GetAlerts();
				Long caregiverId = patientService.getClinicianByUserId(user.getUserId());
				List<MedicationAlert> alerList = WatchRxFactory.getAlertService().getAllAlertsForCaregiver(index,
						pageSize, alert, caregiverId);
				patientAlerts.setTotalCount(
						WatchRxFactory.getAlertService().getCountOfAlertsForCaregiver(alert, caregiverId));
				for (MedicationAlert medAlert : alerList) {
					PatientAlertInfoVO alertInfo = new PatientAlertInfoVO();
					alertInfo.setDescription(medAlert.getAlertDescription());
					alertInfo.setDateTime(medAlert.getDateTime());
					alertInfo.setSeverity(medAlert.getAlertType());
					alertInfo.setPatientInfo(medAlert.getPatientName());
					alertInfo.setPatientId(Long.parseLong(medAlert.getPatientId()));
					alertInfo.setAcknowledgementStatus(medAlert.getAcknowledgementStatus());
					alertInfoList.add(alertInfo);
				}

				patientAlerts.setPatientAlert(alertInfoList);
				patientAlerts.setSuccess(true);
				patientAlerts.setResponseCode(Constants.ResponseCode.MEDICATIONALERTSFOUND);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.MEDICATIONALERTSFOUND;
				patientAlerts.setMessages(messages);
				return patientAlerts;

			} else {
				logger.info("Unauthorized access");
				if (user != null) {
					logger.info("Unauthorized access by user with username " + user.getUserName());
				}
				patientAlerts.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				patientAlerts.setMessages(messages);
				patientAlerts.setSuccess(false);
				return patientAlerts;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			patientAlerts.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			patientAlerts.setMessages(messages);
			patientAlerts.setSuccess(false);
			return patientAlerts;

		}
	}

	@RequestMapping(value = "/getAlertsByPatinetIds", method = RequestMethod.POST)
	public @ResponseBody PatientAlertResponse getAlertsForCaregiverByPatientId(HttpServletRequest request,
			@RequestBody AlertRequestVO requestVO) throws Exception {

		PatientAlertResponse response = new PatientAlertResponse();
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<PatientWatchCaregiverInfoVO> data = new ArrayList<>();
		List<PatientAlertInfoVO> alertInfoList = new ArrayList<>();

		try {
			if (user != null) {
				Long caregiverId = 0L;
				if (user.getRoleType() == 3) {

				} else {
					caregiverId = patientService.getClinicianByUserId(user.getUserId());
				}

				List<MedicationAlert> alertList = WatchRxFactory.getAlertService()
						.getAlertsByPatientIdsAndDate(requestVO, caregiverId);

				response.setTotalCount(
						WatchRxFactory.getAlertService().getAlertsCountByPatientIdsAndDate(requestVO, caregiverId));

				for (MedicationAlert medAlert : alertList) {
					PatientAlertInfoVO alertInfo = new PatientAlertInfoVO();
					alertInfo.setDescription(medAlert.getAlertDescription());
					alertInfo.setDateTime(medAlert.getDateTime());
					alertInfo.setSeverity(medAlert.getAlertType());
					alertInfo.setPatientInfo(medAlert.getPatientName());
					alertInfo.setPatientId(Long.parseLong(medAlert.getPatientId()));
					alertInfo.setAcknowledgementStatus(medAlert.getAcknowledgementStatus());
					alertInfoList.add(alertInfo);
				}

				response.setPatientAlert(alertInfoList);
				response.setPatientWatchCaregiver(data);
				response.setSuccess(true);
				response.setResponseCode(Constants.ResponseCode.MEDICATIONALERTSFOUND);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.MEDICATIONALERTSFOUND;
				response.setMessages(messages);
				return response;
			} else {
				logger.info("Unauthorized access");
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;

		}
	}

	@RequestMapping(value = "/getAlertsByClinicianForFilter", method = RequestMethod.POST)
	public @ResponseBody PatientAlertResponse getAlertsByClinicianByPaginantionByType(HttpServletRequest request,
			@RequestBody AlertRequestVO alertsRequestBody) throws Exception {
		PatientAlertResponse patientAlerts = new PatientAlertResponse();
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<PatientAlertInfoVO> alertInfoList = new ArrayList<>();
		try {
			if (user != null) {
				List<PatientVO> patientList = new ArrayList<>();
				Long totlaNumberOfPat = patientService.getAllPatientsCountByOrgId(user.getRoleType(), user.getOrgId(),
						user.getUserId());
				patientList = patientService.getAllPatientsByOrgId(user.getOrgId(), user.getRoleType(), 0,
						totlaNumberOfPat.intValue(), user.getUserId());
				if (patientList != null && !patientList.isEmpty() && alertsRequestBody.getPatientId().length == 0
						&& user.getRoleType() == 3) {
					Long[] array = new Long[patientList.stream().map(p -> p.getPatientId()).collect(Collectors.toList())
							.size()];
					array = patientList.stream().map(p -> p.getPatientId()).collect(Collectors.toList()).toArray(array);
					alertsRequestBody.setPatientId(array);
				}
				System.out.println(patientList.size());

				List<MedicationAlert> tmpdata = WatchRxFactory.getAlertService()
						.getAlertsForCaregiverFilter(alertsRequestBody, user.getUserId(), user.getOrgId());
				System.out.println(tmpdata.size());
				Long count = WatchRxFactory.getAlertService().getAlertsCountForCaregiverFilter(alertsRequestBody,
						user.getUserId(), user.getOrgId());
				System.out.println(count);
				Map<Long, String> patientData = new HashMap<>();
				for (MedicationAlert medAlert : tmpdata) {
					PatientAlertInfoVO alertInfo = new PatientAlertInfoVO();
					alertInfo.setDescription(medAlert.getAlertDescription());
					alertInfo.setDateTime(medAlert.getDateTime());
					alertInfo.setSeverity(medAlert.getAlertType());
					alertInfo.setPatientInfo(medAlert.getPatientName());
					alertInfo.setPatientId(Long.parseLong(medAlert.getPatientId()));
					alertInfo.setAcknowledgementStatus(medAlert.getAcknowledgementStatus());
					alertInfo.setAlertId(Long.valueOf(medAlert.getAlertId()));

					alertInfoList.add(alertInfo);
				}

				for (PatientVO vo : patientList) {
					PatientWatchCaregiverInfoVO iVo = new PatientWatchCaregiverInfoVO();
					if (!patientData.containsKey(vo.getPatientId())) {
						patientData.put(vo.getPatientId(), vo.getPatientName());
						iVo.setPatientId(String.valueOf(vo.getPatientId()));
						iVo.setPatientName(vo.getFirstName() + " " + vo.getLastName());
						patientAlerts.getPatientWatchCaregiver().add(iVo);
					}
				}
				patientAlerts.setPatientAlert(alertInfoList);
				patientAlerts.setTotalCount(count);
				patientAlerts.setSuccess(true);
				patientAlerts.setResponseCode(Constants.ResponseCode.MEDICATIONALERTSFOUND);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.MEDICATIONALERTSFOUND;
				patientAlerts.setMessages(messages);
				return patientAlerts;
			} else {
				patientAlerts.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				patientAlerts.setMessages(messages);
				patientAlerts.setSuccess(false);
				return patientAlerts;
			}
		} catch (Exception e) {
			logger.error("Message is .... : " + e.getMessage(), e);
			patientAlerts.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			patientAlerts.setMessages(messages);
			patientAlerts.setSuccess(false);
			return patientAlerts;
		}
	}

	private String getMinsSecs(double mins) {
		int minutes = (int) mins;
		int seconds = (int) ((mins - minutes) * 60);
		return minutes + "m:" + seconds + "s";
	}

	@RequestMapping(value = "/downloadXlsForAlerts", method = RequestMethod.POST)
	public @ResponseBody List<MedicationAlert> getAlertsForXlsForCaregiver(HttpServletRequest request,
			@RequestBody AlertRequestVO alertsRequestBody) throws Exception {

		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<MedicationAlert> alertsList = new ArrayList<>();
		if (user != null) {
			alertsList = WatchRxFactory.getAlertService().getAlertsForXls(alertsRequestBody, user.getUserId(),
					user.getRoleType());
		}
		return alertsList;
	}

	@RequestMapping(value = "/downloadXlsForEncounters", method = RequestMethod.POST)
	public @ResponseBody Map<String, String> getEncountersForXlsForCaregiver(HttpServletRequest request,
			@RequestBody AlertRequestVO alertsRequestBody) throws Exception {

		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);

		if (user != null) {
			Date startDate = null;
			Date endDate = null;

			Calendar calendar = Calendar.getInstance();
			Calendar cal = Calendar.getInstance();
			String[] dateParts = alertsRequestBody.getStartDate()
					.substring(0, alertsRequestBody.getStartDate().indexOf("T")).split("-");
			calendar.set(Calendar.DAY_OF_MONTH, Integer.valueOf(dateParts[2]));
			calendar.set(Calendar.MONTH, Integer.valueOf(dateParts[1]) - 1);
			calendar.set(Calendar.YEAR, Integer.valueOf(dateParts[0]));
			calendar.set(Calendar.HOUR_OF_DAY, 00);
			calendar.set(Calendar.MINUTE, 00);
			calendar.set(Calendar.SECOND, 00);
			System.out.println(calendar.get(Calendar.DAY_OF_MONTH));
			String[] dateParts1 = alertsRequestBody.getEndDate()
					.substring(0, alertsRequestBody.getEndDate().indexOf("T")).split("-");
			cal.set(Calendar.DAY_OF_MONTH, Integer.valueOf(dateParts1[2]));
			cal.set(Calendar.MONTH, Integer.valueOf(dateParts1[1]) - 1);
			cal.set(Calendar.YEAR, Integer.valueOf(dateParts1[0]));
			cal.set(Calendar.HOUR_OF_DAY, 23);
			cal.set(Calendar.MINUTE, 59);
			cal.set(Calendar.SECOND, 59);

			startDate = calendar.getTime();
			endDate = cal.getTime();
			List<WatchRxEncountersNew> enList = encountersNewDAO.reasonByPatientId(alertsRequestBody.getUserId(),
					alertsRequestBody.getFilterType(), startDate, endDate);
			SimpleDateFormat formater1 = new SimpleDateFormat("MM-dd-yyyy HH:mm:ss");

			try {
				// creation of the document with a certain size and certain margins
				Document document = new Document(PageSize.A4, 20, 20, 20, 20);

				// creating table and set the column width
				PdfPTable table = new PdfPTable(5);
				float widths[] = { 1, 3, 6, 3, 3 };
				table.setWidths(widths);
				table.setHeaderRows(1);

				// add cell of table - header cell
				PdfPCell cell = new PdfPCell(new Phrase("No."));
				cell.setBackgroundColor(new BaseColor(0, 173, 239));
				table.addCell(cell);

				cell = new PdfPCell(new Phrase("Reason"));
				cell.setBackgroundColor(new BaseColor(0, 173, 239));
				table.addCell(cell);

				cell = new PdfPCell(new Phrase("Description"));
				cell.setBackgroundColor(new BaseColor(0, 173, 239));
				table.addCell(cell);

				cell = new PdfPCell(new Phrase("Duration"));
				cell.setBackgroundColor(new BaseColor(0, 173, 239));
				table.addCell(cell);

				cell = new PdfPCell(new Phrase("DateTime"));
				cell.setBackgroundColor(new BaseColor(0, 173, 239));
				table.addCell(cell);

				Phrase ph;
				// looping the table cell for adding definition
				String patient = null;
				int i = 0;
				for (WatchRxEncountersNew watchRxEncountersNew : enList) {
					i = i + 1;
					if (patient == null) {
						patient = watchRxEncountersNew.getWatchrxPatient().getFirstName() + "_"
								+ watchRxEncountersNew.getWatchrxPatient().getLastName();
					}
					cell = new PdfPCell();
					ph = new Phrase(String.valueOf(i));
					cell.addElement(ph);
					table.addCell(cell);

					cell = new PdfPCell();
					ph = new Phrase(watchRxEncountersNew.getEncounterReason());
					cell.addElement(ph);
					table.addCell(cell);

					cell = new PdfPCell();
					ph = new Phrase(watchRxEncountersNew.getEncounterDescription());
					cell.addElement(ph);
					table.addCell(cell);

					cell = new PdfPCell();
					ph = new Phrase(getMinsSecs(watchRxEncountersNew.getDuration()));
					cell.addElement(ph);
					table.addCell(cell);

					cell = new PdfPCell();
					ph = new Phrase(formater1.format(watchRxEncountersNew.getEncounterDatetime()));
					cell.addElement(ph);
					table.addCell(cell);
				}

				Chunk glue = new Chunk(new VerticalPositionMark());
				Paragraph p = new Paragraph("Name: " + new String(patient).replaceAll("_", " "));
				p.add(new Chunk(glue));
				p.add("Report: "
						+ alertsRequestBody.getStartDate().substring(0, alertsRequestBody.getStartDate().indexOf("T"))
						+ " to "
						+ alertsRequestBody.getEndDate().substring(0, alertsRequestBody.getEndDate().indexOf("T")));

				ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
				PdfWriter.getInstance(document, byteArrayOutputStream);
				document.open();
				document.add(p);
				document.add(Chunk.NEWLINE);
				document.add(table);
				document.close();
				GcsService gcsService = GcsServiceFactory.createGcsService(RetryParams.getDefaultInstance());
				AppIdentityService appIdentityService = AppIdentityServiceFactory.getAppIdentityService();
				String defaultBucketName = appIdentityService.getDefaultGcsBucketName();
				GcsFilename fileName = new GcsFilename(defaultBucketName + "/" + "encounter",
						"Encounter_" + patient + ".pdf");

				GcsFileOptions instance = GcsFileOptions.getDefaultInstance();
				// GcsFilename gcsFile = new GcsFilename(fileName);
				GcsOutputChannel outputChannel = gcsService.createOrReplace(fileName, instance);

				outputChannel.write(ByteBuffer.wrap(byteArrayOutputStream.toByteArray()));
				outputChannel.close();
				/*
				 * HttpHeaders headers = new HttpHeaders();
				 * headers.setContentType(MediaType.APPLICATION_PDF); // Here you have to set
				 * the actual filename of your pdf String filename = "Encounter"+patient+".pdf";
				 * headers.setContentDispositionFormData(filename, filename);
				 * headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");
				 */
				logger.info("$$$$$" + WatchRxUtils.readTextFileOnly("encounter/Encounter_" + patient + ".pdf"));
				Map<String, String> mp = new HashMap<>();
				mp.put("url", WatchRxUtils.readTextFileOnly("encounter/Encounter_" + patient + ".pdf"));
				return mp;
			} catch (DocumentException e) {
				e.printStackTrace();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	@RequestMapping(value = "/downloadCsvForAlerts", method = RequestMethod.POST)
	public @ResponseBody void getAlertsForCsvForCaregiver(HttpServletRequest request, HttpServletResponse response,
			@RequestBody AlertRequestVO alertsRequestBody) throws Exception {

		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<MedicationAlert> alertsList = new ArrayList<>();
		if (user != null) {
			alertsList = WatchRxFactory.getAlertService().getAlertsForXls(alertsRequestBody, user.getUserId(),
					user.getRoleType());

			DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Date date = new Date();

			String fileName = user.getFirstName() + "_" + sdf.format(date) + ".csv";

			response.setContentType("text/csv");
			response.setHeader("Content-disposition", "attachment;filename=" + fileName);

			ArrayList<String> rows = new ArrayList<>();
			rows.add("Date and Time,Severity,Patient Name,Phone Number,Alert Description");
			rows.add("\n");
			for (MedicationAlert medAlert : alertsList) {

				String dateTime = medAlert.getDateTime();
				String severity = medAlert.getAlertType();
				String patientName = medAlert.getPatientName();
				String patinetPhone = medAlert.getPatientPhoneNumber();
				String description = medAlert.getAlertDescription().replace(",", " ");

				String row = " " + dateTime + "," + severity + "," + patientName + "," + " " + patinetPhone + ","
						+ description;
				rows.add(row);
				rows.add("\n");
			}

			for (String outputString : rows) {
				PrintWriter out = response.getWriter();
				out.print(outputString);
			}
		}
	}

	@RequestMapping(value = "/getalertsforadminbydate", method = RequestMethod.POST)
	public @ResponseBody MedicationAlertListResponse getalertsforadminbydate(HttpServletRequest request,
			@RequestBody MedicationAlertRequestVO alertRequest) throws Exception {
		logger.info("inside getalertsforadminbydate: " + alertRequest.toString());

		MedicationAlertListResponse response = new MedicationAlertListResponse();
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {

				List<PatientAlertInfoVO> alertsList = WatchRxFactory.getAlertService().getAllAlertsByDate(
						alertRequest.getStartDate(), alertRequest.getEndDate(), alertRequest.getIndex(),
						alertRequest.getPageSize());

				Long count = WatchRxFactory.getAlertService().getAlertsCountForAdmin(alertRequest.getStartDate(),
						alertRequest.getEndDate(), alertRequest.getIndex(), alertRequest.getPageSize());

				response.setMedicationalertList(alertsList);
				response.setCount(count);
				response.setSuccess(true);
				response.setResponseCode(Constants.ResponseCode.MEDICATIONALERTSFOUND);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.MEDICATIONALERTSFOUND;
				response.setMessages(messages);
				return response;
			} else {
				logger.info("Unauthorized access");

				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}
	}

	@RequestMapping(value = "/getalertsforadminbytype", method = RequestMethod.POST)
	public @ResponseBody MedicationAlertListResponse getAlertsByTypeforAdmin(HttpServletRequest request,
			@RequestBody AlertRequestVO alertRequest) throws Exception {
		logger.info("inside getalertsforadminbydate: " + alertRequest.toString());

		MedicationAlertListResponse response = new MedicationAlertListResponse();
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null && user.getRoleType() == Constants.UserType.ADMIN) {

				List<PatientAlertInfoVO> alertsList = WatchRxFactory.getAlertService().getAllAlertsByType(alertRequest);

				Long count = WatchRxFactory.getAlertService().getAllAlertsCountByType(alertRequest);

				response.setMedicationalertList(alertsList);
				response.setCount(count);
				response.setSuccess(true);
				response.setResponseCode(Constants.ResponseCode.MEDICATIONALERTSFOUND);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.MEDICATIONALERTSFOUND;
				response.setMessages(messages);
				return response;
			} else {
				logger.info("Unauthorized access");
				if (user != null) {
					logger.info("Unauthorized access by user with username " + user.getUserName());
				}
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}
	}

	@RequestMapping(value = "/getalldataforfilter", method = RequestMethod.GET)
	public @ResponseBody DataFilterResponse getAllDataForDataManagementFilter(HttpServletRequest request)
			throws Exception {
		DataFilterResponse response = new DataFilterResponse();
		response = WatchRxFactory.getAlertService().getAllDetailsForFilter();
		response.setSuccess(true);
		return response;

	}

	@RequestMapping(value = "/downloadAdminAlertsXLS", method = RequestMethod.POST)
	public @ResponseBody List<MedicationAlert> downloadXLSForAdmin(HttpServletRequest request,
			HttpServletResponse response, @RequestBody AlertRequestVO alertRequest) throws IOException {
		List<MedicationAlert> data;
		data = WatchRxFactory.getAlertService().getAllAlertsForCSVandXLS(alertRequest.getStartDate(),
				alertRequest.getEndDate());
		return data;
	}

	@RequestMapping(value = "/downloadAdminAlertsCSV", method = RequestMethod.POST)
	public void downloadCSVForAdmin(HttpServletRequest request, HttpServletResponse response,
			@RequestBody AlertRequestVO alertReq) throws IOException {

		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Date date = new Date();

		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		String fileName = user.getFirstName() + "_" + sdf.format(date) + ".csv";

		response.setContentType("text/csv");
		response.setHeader("Content-disposition", "attachment;filename=" + fileName);

		ArrayList<String> rows = new ArrayList<>();
		rows.add("Date & Time,Severity,Patient Name,Alert Description");
		rows.add("\n");

		List<MedicationAlert> tmpdata = WatchRxFactory.getAlertService()
				.getAllAlertsForCSVandXLS(alertReq.getStartDate(), alertReq.getEndDate());

		for (MedicationAlert medAlert : tmpdata) {

			String dateTime = medAlert.getDateTime();
			String severity = medAlert.getAlertType();
			String patientName = medAlert.getPatientName();
			String description = medAlert.getAlertDescription();
			String row = " " + dateTime + "," + severity + "," + patientName + "," + description;
			rows.add(row);
			rows.add("\n");
		}

		for (String outputString : rows) {
			PrintWriter out = response.getWriter();
			out.print(outputString);
		}

	}

	@RequestMapping(value = "/savesPatientQuestions", method = RequestMethod.POST)
	public @ResponseBody StatusVO savePatientQuestions(HttpServletRequest request,
			@RequestBody PatientTextMessageVO requestBody) throws Exception {

		logger.info("inside savePatientQuestions:::: " + requestBody.toString());
		StatusVO response = null;
		response = patientService.savePatientQuestionsMultiple(requestBody);
		return response;
	}

	@RequestMapping(value = "/getTextMessageResponseByPatient/{patientId}/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody ViewTextMessageResponse getTextMessageResponse(HttpServletRequest request,
			@PathVariable Long patientId, @PathVariable Integer index, @PathVariable Integer pageSize) {
		logger.info("inside getHeartRatesByPatientId" + patientId);
		ViewTextMessageResponse viewTextMessageResponse = new ViewTextMessageResponse();
		Long count = null;
		List<ViewTextMessage> data = patientService.getPatientTextMessageResponse(patientId, index, pageSize,
				viewTextMessageResponse);
		count = patientService.getTextMessageOnlyReceivedByPatientIdCount(patientId);
		viewTextMessageResponse.setResultCount(count);
		viewTextMessageResponse.setViewMessages(data);
		viewTextMessageResponse.setStatus(true);
		List<Map<String, Object>> programs = patientService.getPatientProgramsListByPId(patientId);
		viewTextMessageResponse.setPrograms(programs);
		return viewTextMessageResponse;
	}

	@RequestMapping(value = "/updateInsuranceDetails", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveInsuranceDetails(HttpServletRequest request, @RequestBody PatientVO requestBody)
			throws Exception {
		StatusVO response = new StatusVO();
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			boolean success = patientService.saveInsuranceDetails(requestBody);
			if (success) {
				response.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				response.setMessages(messages);
			} else {
				response.setSuccess(false);
				String messages[] = new String[1];
				messages[0] = "Insurance already exist";
				response.setMessages(messages);
			}
		}
		return response;
	}

	@RequestMapping(value = "/updateChronicCondition", method = RequestMethod.POST)
	public @ResponseBody StatusVO updateChronicConditions(HttpServletRequest request,
			@RequestBody CreatePatientRequestChronicConditionsVO chronicConditionsVO) throws Exception {
		StatusVO response = new StatusVO();
		response.setSuccess(patientService.updateChronicConditions(chronicConditionsVO));
		return response;
	}

	@RequestMapping(value = "/saveNewChronicCondition", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveChronicConditions(HttpServletRequest request,
			@RequestBody CreatePatientRequestChronicConditionsListVO chronicConditionsVO) throws Exception {
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		StatusVO response = new StatusVO();
		if (user != null) {
			if (!chronicConditionsVO.getChronicConditions().isEmpty()) {
				patientService.saveChronicCondition(chronicConditionsVO);
				response.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				response.setMessages(messages);
			} else {
				response.setSuccess(false);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.INSUFFICIENTARGUMENTS;
				response.setMessages(messages);
			}
		} else {
			response.setSuccess(false);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
			response.setMessages(messages);
		}
		return response;
	}

	@RequestMapping(value = "/saveEncounter", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveEncounter(HttpServletRequest request, @RequestBody EncounterNewVO encounterNewVO)
			throws Exception {
		UserVO user = null;
		StatusVO statusVO = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		try {
			if (user != null) {
				encounterNewVO.setUserId(user.getUserId());
				patientService.saveEncountersNew(encounterNewVO);

				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				statusVO.setMessages(messages);
			}
		} catch (WatchRxServiceException e) {
			statusVO.setSuccess(false);
			statusVO.setMessages(new String[]{e.getMessage()});
		} catch (Exception e) {
			statusVO.setSuccess(false);
			String messages[] = new String[1];
			messages[0] = e.getMessage();
			statusVO.setMessages(messages);
		}
		return statusVO;
	}

	@RequestMapping(value = "/saveDraft", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveDraft(HttpServletRequest request, @RequestBody EncounterNewVO encounterNewVO) throws Exception {
		UserVO user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		StatusVO statusVO = new StatusVO();
		try {
			if (user != null) {
				encounterNewVO.setUserId(user.getUserId());
				patientService.saveEncounterDraft(encounterNewVO);
				statusVO.setSuccess(true);
				statusVO.setMessages(new String[]{"Operation Successful"});
			}
		} catch (Exception e) {
			statusVO.setSuccess(false);
			statusVO.setMessages(new String[]{e.getMessage()});
		}
		return statusVO;
	}

	@RequestMapping(value = "/submitDraft", method = RequestMethod.POST)
	public @ResponseBody StatusVO submitDraftAsEncounter(@RequestBody EncounterNewVO encounterNewVO, HttpServletRequest request) throws Exception {
		UserVO user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		StatusVO statusVO = new StatusVO();
		try {
			if (user != null) {
				encounterNewVO.setUserId(user.getUserId());
				if (encounterNewVO.getDraftId() != null) {
					patientService.submitDraftAsEncounter(encounterNewVO);
				} else {
					patientService.saveEncountersNew(encounterNewVO);
				}
				statusVO.setSuccess(true);
				statusVO.setMessages(new String[]{"Operation Successful"});
			}
		} catch (Exception e) {
			statusVO.setSuccess(false);
			statusVO.setMessages(new String[]{e.getMessage()});
		}
		return statusVO;
	}

	@RequestMapping(value = "/deleteDraft", method = RequestMethod.POST)
	public @ResponseBody StatusVO deleteDraft(@RequestBody EncounterNewVO encounterNewVO) throws Exception {
		StatusVO statusVO = new StatusVO();
		try {
			if (encounterNewVO.getDraftId() == null) {
				throw new IllegalArgumentException("Draft ID is required");
			}
			patientService.deleteEncounterDraft(encounterNewVO.getDraftId());
			statusVO.setSuccess(true);
			statusVO.setMessages(new String[]{"Operation Successful"});
		} catch (Exception e) {
			statusVO.setSuccess(false);
			statusVO.setMessages(new String[]{e.getMessage()});
		}
		return statusVO;
	}

	@RequestMapping(value = "/getDrafts/{patientId}/{index}/{pageSize}/{startDate}/{endDate}", method = RequestMethod.GET)
	public @ResponseBody DraftListVO getEncounterDrafts(HttpServletRequest request, @PathVariable Long patientId,
						@PathVariable Integer index, @PathVariable Integer pageSize, @PathVariable String startDate, @PathVariable String endDate) throws Exception {

		UserVO user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		Date startDt = formatter.parse(startDate);
		Date endDt = formatter.parse(endDate);
		List<WatchRxEncounterDrafts> drafts = encounterDraftsDAO
				.getAllDraftsByPatientIdAndDatePagination(patientId, startDt, endDt, index, pageSize);
		Long totalCount = encounterDraftsDAO.countAllDraftsByPatientIdAndDate(patientId, startDt, endDt);

		List<DraftVO> draftVOs = new ArrayList<>();
		for (WatchRxEncounterDrafts draft : drafts) {
			DraftVO vo = new DraftVO();
			vo.setDraftId(draft.getDraftId());
			vo.setPatientId(draft.getWatchrxPatient().getPatientId());
			vo.setEncounterReason(draft.getEncounterReason());
			vo.setEncounterDescription(draft.getEncounterDescription());
			vo.setEncounterDateTime(formatter.format(draft.getEncounterDatetime()));
			vo.setEncounterEndDateTime(formatter.format(draft.getEncounterEndDatetime()));
			vo.setDraftCreatedDate(formatter.format(draft.getDraftCreatedDate()));
			vo.setDraftModifiedDate(formatter.format(draft.getDraftModifiedDate()));
			vo.setDuration(draft.getDuration());
			vo.setReview(draft.getReview());
			vo.setUserId(draft.getWatchrxUser().getUserId());
			vo.setAddedByUser(draft.getWatchrxUser().getFirstName() + " " + draft.getWatchrxUser().getLastName());
			draftVOs.add(vo);
		}

		DraftListVO response = new DraftListVO();
		response.setDraftList(draftVOs);
		response.setResultCount(totalCount);
		response.setSuccess(true);
		response.setMessages(new String[]{"Operation Successful"});
		return response;
	}



	@RequestMapping(value = "/getEncounters/{patientId}/{index}/{pageSize}/{startDate}/{endDate}", method = RequestMethod.GET)
	public @ResponseBody EncounterNewListVO getEncounters(HttpServletRequest request, @PathVariable Long patientId,
			@PathVariable Integer index, @PathVariable Integer pageSize, @PathVariable String startDate,
			@PathVariable String endDate) throws Exception {
		UserVO user = null;
		List<EncounterNewVO> encounterNewList = null;
		Integer resultCount = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		EncounterNewListVO encounters = new EncounterNewListVO();
		if (user != null) {
			PatientVO patientVO = new PatientVO();
			patientVO.setPatientId(patientId);
			encounterNewList = patientService.getEncounterNewV2(patientVO, index, pageSize, startDate, endDate);
			resultCount = patientService.getEncounterNewResultCount(patientVO, startDate, endDate);
			List<String> pList = patientService.getPatientProgramsList(patientId);
			String minsStr = "";
			if (pList != null && pList.size() > 0) {
				if (pList.contains("RPM")) {
					minsStr = minsStr
							.concat("<b>RPM : <mark>" + getMinsSecs(patientVO.getRpmMins()) + "</mark></b>&nbsp;");
				}
				if (pList.contains("CCM")) {
					minsStr = minsStr
							.concat("<b>CCM : <mark>" + getMinsSecs(patientVO.getCcmMins()) + "</mark></b>&nbsp;");
				}
				if (pList.contains("PCM")) {
					minsStr = minsStr
							.concat("<b>PCM : <mark>" + getMinsSecs(patientVO.getPcmMins()) + "</mark></b>&nbsp;");
				}
			}
			encounters.setPrograms(patientService.getPatientProgramsListByPId(patientId));
			encounters.setMinsDetails(minsStr);
			encounters.setEnMins(patientVO.getEnMins());
			encounters.setCcmMins(patientVO.getCcmMins());
			encounters.setRpmMins(patientVO.getRpmMins());
			encounters.setPcmMins(patientVO.getPcmMins());
		}
		encounters.setResultCount(resultCount.longValue());
		encounters.setEncounterNewList(encounterNewList);
		encounters.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		encounters.setMessages(messages);
		return encounters;
	}

	@RequestMapping(value = "/deleteEncounter", method = RequestMethod.POST)
	public @ResponseBody StatusVO deleteEncounter(HttpServletRequest request,
			@RequestBody EncounterNewVO encounterNewVO) throws Exception {
		StatusVO statusVO = null;
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			encounterNewVO.setUserId(user.getUserId());
			statusVO = patientService.deleteEncounterNew(encounterNewVO);
		}
		return statusVO;
	}

	@RequestMapping(value = "/saveTarget", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveTarget(HttpServletRequest request, @RequestBody TargetsVO targetsVO)
			throws Exception {
		UserVO user = null;
		StatusVO statusVO = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			targetsVO.setUserId(user.getUserId());

			patientService.saveTargets(targetsVO);
			statusVO.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			statusVO.setMessages(messages);
		}

		return statusVO;
	}

	@RequestMapping(value = "/getTargets/{patientId}", method = RequestMethod.GET)
	public @ResponseBody TargetListVO getTargets(HttpServletRequest request, @PathVariable Long patientId)
			throws Exception {
		UserVO user = null;
		List<TargetsVO> targetList = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			PatientVO patientVO = new PatientVO();
			patientVO.setPatientId(patientId);

			targetList = patientService.getTargets(patientVO);

		}
		TargetListVO targets = new TargetListVO();
		targets.setTargetVOList(targetList);
		targets.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		targets.setMessages(messages);
		return targets;
	}

	@RequestMapping(value = "/deleteTarget", method = RequestMethod.POST)
	public @ResponseBody StatusVO deleteTarget(HttpServletRequest request, @RequestBody TargetsVO targetsVO)
			throws Exception {
		StatusVO statusVO = null;
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			targetsVO.setUserId(user.getUserId());
			statusVO = patientService.deleteTarget(targetsVO);
		}
		return statusVO;
	}

	@RequestMapping(value = "/saveVital", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveVital(HttpServletRequest request, @RequestBody VitalVO vitalVO) throws Exception {
		UserVO user = null;
		StatusVO statusVO = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			vitalVO.setUserId(user.getUserId());

			patientService.saveVital(vitalVO);
			statusVO.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			statusVO.setMessages(messages);
		}

		return statusVO;
	}

	@RequestMapping(value = "/getVitals/{patientId}/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody VitalListVO getVitals(HttpServletRequest request, @PathVariable Long patientId,
			@PathVariable Integer index, @PathVariable Integer pageSize) throws Exception {
		UserVO user = null;
		List<VitalVO> vitalList = null;
		Long vitalcount = 0l;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			PatientVO patientVO = new PatientVO();
			patientVO.setPatientId(patientId);

			vitalList = patientService.getVitals(patientVO, index, pageSize);
			vitalcount = patientService.getVitalResultCount(patientVO);

		}
		VitalListVO vitals = new VitalListVO();
		vitals.setVitalVOList(vitalList);
		vitals.setResultCount(vitalcount);
		vitals.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		vitals.setMessages(messages);
		return vitals;
	}

	/*
	 * @RequestMapping(value = "/deleteVital", method = RequestMethod.POST)
	 * public @ResponseBody StatusVO deleteVital(HttpServletRequest
	 * request, @RequestBody VitalVO vitalVO) throws Exception { StatusVO statusVO =
	 * null; UserVO user = null; user = (UserVO)
	 * request.getSession().getAttribute("user"); if (user != null) {
	 * vitalVO.setUserId(user.getUserId()); statusVO =
	 * patientService.deleteVital(vitalVO); } return statusVO; }
	 */

	@RequestMapping(value = "/deleteVital", method = RequestMethod.POST)
	public @ResponseBody StatusVO deleteVital(HttpServletRequest request, @RequestBody VitalReadingVO vitalReadingVO)
			throws Exception {
		System.out.println("Received Vitals to detele : " + vitalReadingVO.toString());
		StatusVO statusVO = null;
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			statusVO = patientService.deleteVital(vitalReadingVO);
		}
		return statusVO;
	}

	@RequestMapping(value = "/savePhoneCommunication", method = RequestMethod.POST)
	public @ResponseBody StatusVO savePhoneCommunication(HttpServletRequest request,
			@RequestBody PhoneCommunicationVO phoneCommunicationVO) throws Exception {
		UserVO user = null;
		StatusVO statusVO = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			patientService.savePhoneCommunication(phoneCommunicationVO);
			statusVO.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			statusVO.setMessages(messages);
		}

		return statusVO;
	}

	@RequestMapping(value = "/getPhoneCommunications/{patientId}/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody PhoneCommunicationListVO getPhoneCommunications(HttpServletRequest request,
			@PathVariable Long patientId, @PathVariable Integer index, @PathVariable Integer pageSize)
			throws Exception {
		UserVO user = null;
		PhoneCommunicationListVO phoneCommunicationListVO = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		Long count = 0L;
		if (user != null) {
			PatientVO patientVO = new PatientVO();
			patientVO.setPatientId(patientId);
			phoneCommunicationListVO = patientService.getPhoneCommunication(patientVO, index, pageSize);
			count = patientService.getPhoneCommunicationCount(patientVO);
		}
		phoneCommunicationListVO.setSuccess(true);
		phoneCommunicationListVO.setCount(count);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		phoneCommunicationListVO.setMessages(messages);
		return phoneCommunicationListVO;
	}

	@RequestMapping(value = "/deletePhoneCommunication", method = RequestMethod.POST)
	public @ResponseBody StatusVO deletePhoneCommunication(HttpServletRequest request,
			@RequestBody PhoneCommunicationVO phoneCommunicationVO) throws Exception {
		StatusVO statusVO = null;
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			// phoneCommunicationVO.setUserId(user.getUserId());
			statusVO = patientService.deletePhoneCommunication(phoneCommunicationVO);
		}
		return statusVO;
	}

	@RequestMapping(value = "/getAlertsByPatientId/{index}/{pageSize}/{alertType}/{patientId}", method = RequestMethod.GET)
	public @ResponseBody PatientAlertResponse getAlertsByPhysicianId(HttpServletRequest request,
			@PathVariable Integer index, @PathVariable Integer pageSize, @PathVariable String alertType,
			@PathVariable Long patientId) throws Exception {
		logger.info("inside getAlertByClinician:::: ");
		PatientAlertResponse patientAlerts = new PatientAlertResponse();

		List<PatientWatchCaregiverInfoVO> data = new ArrayList<>();
		List<PatientAlertInfoVO> alertInfoList = new ArrayList<>();
		try {
			GetAlerts alert = new GetAlerts();
			alert.setAlertType(alertType);
			PatientVO patientVO = patientService.getPatient(patientId);
			List<PatientVO> patientList = new ArrayList<>();
			patientList.add(patientVO);
			List<Long> listofpatinet = new ArrayList<>();
			for (PatientVO patient : patientList) {
				listofpatinet.add(patient.getPatientId());
			}
			if (listofpatinet.size() > 0) {
				List<MedicationAlert> tmpdata = WatchRxFactory.getAlertService().getAllAlertsByPatientId(listofpatinet,
						index, pageSize, alert);
				patientAlerts.setTotalCount(
						WatchRxFactory.getAlertService().getTotalCountOfAlertsByPatientId(listofpatinet, alert));

				for (PatientVO patient : patientList) {
					logger.info("Alert data patient id" + patient.getPatientId());
					PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
					List<ClinicianVO> clinicianList = new ArrayList<>();
					clinicianList = clinicianService.getCaseManagersByPatient(patient.getPatientId());
					String names = new String();

					if (clinicianList.size() > 0 && clinicianList.size() == 1) {
						pwcInfo.setCaregiverName(
								clinicianList.get(0).getFirstName() + " " + clinicianList.get(0).getLastName());
					} else {
						for (int i = 0; i < clinicianList.size(); i++) {
							if (i == 0) {
								names = clinicianList.get(i).getFirstName() + " " + clinicianList.get(i).getLastName();
							} else {
								names = names + " /" + clinicianList.get(i).getFirstName() + " "
										+ clinicianList.get(i).getLastName();
							}
						}
						pwcInfo.setCaregiverName(names);

					}
					pwcInfo.setPatientId(patient.getPatientId().toString());
					String name = patient.getFirstName() + " " + patient.getLastName();
					pwcInfo.setPatientNamePhone(name + ", " + patient.getPhoneNumber());
					patient.getAddress();
					// String patientAdd = address.getAddress1() + ", " +
					// address.getCity() + ", " + address.getState();
					pwcInfo.setPatientAddress(patient.getPatientAddress());
					if (patient.getWatch() != null) {
						if (patient.getWatch().getWatchMake() != null) {
							pwcInfo.setAssignedWatch(patient.getWatch().getWatchMake());
						}
					}
					// pwcInfo.setAssignedWatch(patient.getWatch().getModel());
					data.add(pwcInfo);
					alert.setPatientId(patient.getPatientId().toString());
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
					Calendar calendar = Calendar.getInstance();
					alert.setEndDate(sdf.format(calendar.getTime()));
					// alert.setStartDate(sdf.format(calendar.getTime()));
					System.out.println("End Date: " + alert.getEndDate());
					calendar.add(Calendar.MONTH, -5);
					System.out.println("Start Date : " + sdf.format(calendar.getTime()));
					alert.setStartDate(sdf.format(calendar.getTime()));
					// alert.setEndDate(sdf.format(calendar.getTime()));

				}

				logger.info("Alert data patient id size " + tmpdata.size());
				for (MedicationAlert medAlert : tmpdata) {
					PatientAlertInfoVO alertInfo = new PatientAlertInfoVO();
					alertInfo.setDescription(medAlert.getAlertDescription());

					SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

					String dateTime = dateFormat.format(medAlert.getCreatedDate());

					alertInfo.setDateTime(dateTime);

					alertInfo.setSeverity(medAlert.getAlertType());
					alertInfo.setPatientInfo(medAlert.getPatientName());
					alertInfoList.add(alertInfo);
				}

				patientAlerts.setPatientAlert(alertInfoList);
				// System.out.println("Alert Info: "+ alertInfoList.toString());
				// System.out.println("Patient Watch Caregiver Info:
				// "+data.toString());
				patientAlerts.setPatientWatchCaregiver(data);
				logger.info("Alertr data" + data.toString());
				logger.info("Alertr data size" + data.size());
				patientAlerts.setSuccess(true);
				patientAlerts.setResponseCode(Constants.ResponseCode.MEDICATIONALERTSFOUND);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.MEDICATIONALERTSFOUND;
				patientAlerts.setMessages(messages);
				return patientAlerts;
			} else {
				patientAlerts.setResponseCode(Constants.ResponseCode.PATIENTNOTFOUND);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.PATIENTNOTFOUND;
				patientAlerts.setMessages(messages);
				patientAlerts.setSuccess(false);
				return patientAlerts;
			}

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			patientAlerts.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			patientAlerts.setMessages(messages);
			patientAlerts.setSuccess(false);
			return patientAlerts;

		}
	}

	@RequestMapping(value = "/updateCptDetails", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveCptDetails(HttpServletRequest request, @RequestBody PatientVO requestBody)
			throws Exception {
		UserVO user = null;
		StatusVO response = new StatusVO();
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				if (!requestBody.getCptVOList().isEmpty()) {
					response.setSuccess(patientService.saveCptDetails(requestBody));
					response.setSuccess(true);
					String messages[] = new String[1];
					messages[0] = "Operation Successful";
					response.setMessages(messages);
				} else {
					String messages[] = new String[1];
					messages[0] = Constants.ResponseString.INSUFFICIENTARGUMENTS;
					response.setMessages(messages);
					response.setSuccess(false);
				}
			} else {
				response.setSuccess(false);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
			}
			return response;
		} catch (WatchRxServiceException e) {
			response.setSuccess(false);
			String messages[] = new String[1];
			messages[0] = e.getMessage();
			response.setMessages(messages);
			return response;
		} catch (Exception e) {
			throw new WatchRxServiceException("Error while updating the CPT details.");
		}
	}

	@RequestMapping(value = "/getAlertsReports", method = RequestMethod.POST)
	public @ResponseBody VitalWeeklyGraphListVO getAlertsCount(HttpServletRequest request,
			@RequestBody AlertsCountRequestVO requestBody) throws Exception {
		UserVO user = null;
		VitalWeeklyGraphListVO response = new VitalWeeklyGraphListVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		logger.info("Request for the Alerts Report Patient Id : " + requestBody.getPatientId() + " Format : "
				+ requestBody.getFormat() + " StartDate : " + requestBody.getStartDate() + " EndDate : "
				+ requestBody.getEndDate());
		if (user != null) {

			// response = patientService.getAlertsCount(requestBody);
			response = patientService.getAlertsGraph(requestBody);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);

		}
		return response;
	}

	@RequestMapping(value = "/getAlertsReportsXls", method = RequestMethod.POST)
	public @ResponseBody List<AlertVO> getAlerts(HttpServletRequest request,
			@RequestBody AlertsCountRequestVO requestBody) throws Exception {
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		List<AlertVO> response = null;
		if (user != null) {

			response = patientService.getAlertsExcel(requestBody);

		}
		return response;
	}

	@RequestMapping(value = "/getContactMinutesReports", method = RequestMethod.POST)
	public @ResponseBody CumulativeMinutesListVO getContactMinutesCount(HttpServletRequest request,
			@RequestBody AlertsCountRequestVO requestBody) throws Exception {
		UserVO user = null;
		CumulativeMinutesListVO response = new CumulativeMinutesListVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);

		if (user != null) {

			response = patientService.getCumulativeMinutesCount(requestBody);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		}
		return response;
	}

	@RequestMapping(value = "/getContactMinutesReportsXls", method = RequestMethod.POST)
	public @ResponseBody List<PhoneCommunicationXlsVO> getContactMinutes(HttpServletRequest request,
			@RequestBody AlertsCountRequestVO requestBody) throws Exception {
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		List<PhoneCommunicationXlsVO> response = null;
		if (user != null) {
			response = patientService.getCumulativeMinutesExcel(requestBody);
		}
		return response;
	}

	@RequestMapping(value = "/getCollectedHeartRateReports", method = RequestMethod.POST)
	public @ResponseBody CollectedHeartRateCountListVO getCollectedHeartRateCount(HttpServletRequest request,
			@RequestBody AlertsCountRequestVO requestBody) throws Exception {
		UserVO user = null;
		CollectedHeartRateCountListVO response = new CollectedHeartRateCountListVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);

		if (user != null) {

			response = patientService.getCollectedHeartRateCount(requestBody);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		}
		return response;
	}

	@RequestMapping(value = "/getCollectedHeartRateReportsXls", method = RequestMethod.POST)
	public @ResponseBody List<CollectedHeartRateXlsVO> getCollectedHeartRate(HttpServletRequest request,
			@RequestBody AlertsCountRequestVO requestBody) throws Exception {
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		List<CollectedHeartRateXlsVO> response = null;
		if (user != null) {
			response = patientService.getCollectedHeartRateExcel(requestBody);
		}
		return response;
	}

	@RequestMapping(value = "/getVitalsReports", method = RequestMethod.POST)
	public @ResponseBody VitalsCountListVO getVitalsCount(HttpServletRequest request,
			@RequestBody AlertsCountRequestVO requestBody) throws Exception {
		UserVO user = null;
		VitalsCountListVO response = new VitalsCountListVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		logger.info("Request for the Vital Reports Patient Id : " + requestBody.getPatientId() + " Format : "
				+ requestBody.getFormat() + " StartDate : " + requestBody.getStartDate() + " EndDate : "
				+ requestBody.getEndDate());
		if (user != null) {

			response = patientService.getVitalsCount(requestBody);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		}
		return response;
	}

	@RequestMapping(value = "/getPatientByPhysicianCaseManager/{clinicianId}/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody PatientsAssignedClinicianWCListVO getPatientByPhysicianCaseManager(HttpServletRequest request,
			@PathVariable Long clinicianId, @PathVariable Integer index, @PathVariable Integer pageSize)
			throws Exception {
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		PatientsAssignedClinicianWCListVO response = new PatientsAssignedClinicianWCListVO();
		List<PatientWatchCaregiverInfoVO> patientWatchCaregiverInfoVOList = new ArrayList<>();
		if (user != null) {
			PatientsAssignedClinicianListVO patientsAssignedClinicianListVO = patientService
					.getPatientsPhysicianCaseManager(user.getUserId(), (long) user.getRoleType(), clinicianId, index,
							pageSize);
			List<PatientVO> patientVOList = patientsAssignedClinicianListVO.getPatients();
			for (PatientVO patient : patientVOList) {
				PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
				List<ClinicianVO> clinicianList = new ArrayList<>();
				clinicianList = clinicianService.getCaseManagersByPatient(patient.getPatientId());
				String names = new String();

				if (clinicianList.size() > 0 && clinicianList.size() == 1) {
					pwcInfo.setCaregiverName(
							clinicianList.get(0).getFirstName() + " " + clinicianList.get(0).getLastName());
				} else {
					for (int i = 0; i < clinicianList.size(); i++) {
						if (i == 0) {
							names = clinicianList.get(i).getFirstName() + " " + clinicianList.get(0).getLastName();
						} else {
							names = names + " / " + clinicianList.get(i).getFirstName() + " "
									+ clinicianList.get(i).getLastName();
						}
					}
					pwcInfo.setCaregiverName(names);

				}
				pwcInfo.setimgPath(patient.getPicPath());
				pwcInfo.setPatientId(patient.getPatientId().toString());
				String name = patient.getFirstName() + " " + patient.getLastName();
				pwcInfo.setPatientNamePhone(name + " " + patient.getPhoneNumber());
				pwcInfo.setPatientName(name);
				pwcInfo.setPatientPhoneNumber(patient.getPhoneNumber());
				AddressVO address = patient.getAddress();
				String patientAdd = address.getAddress1() + ", " + address.getCity() + ", " + address.getState();
				pwcInfo.setPatientAddress(patientAdd);
				if (patient.getWatch() != null) {
					if (patient.getWatch().getWatchMake() != null) {
						String assignedWatch = patient.getWatch().getWatchMake();
						if (patient.getWatch().getWatchModel() != null) {
							assignedWatch = assignedWatch + " " + patient.getWatch().getWatchModel();
						}
						pwcInfo.setAssignedWatch(assignedWatch);
					}
				}
				pwcInfo.setCaregiverRelationship(patient.getcaregiverRelationship());
				patientWatchCaregiverInfoVOList.add(pwcInfo);
			}
			response.setClinicianName(patientsAssignedClinicianListVO.getClinicianName());
			response.setPatientsInfo(patientWatchCaregiverInfoVOList);
		}
		return response;
	}

	@RequestMapping(value = "/deleteCptDetails", method = RequestMethod.POST)
	public @ResponseBody StatusVO deleteCptDetails(HttpServletRequest request, @RequestBody CptVO cptVO)
			throws Exception {
		StatusVO statusVO = new StatusVO();
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			patientService.deleteCptDetails(cptVO.getCptId());
			String[] messages = { "Opertion Successfull" };
			statusVO.setMessages(messages);
			statusVO.setSuccess(true);
		}
		return statusVO;
	}

	@RequestMapping(value = "/deleteInsuranceDetails", method = RequestMethod.POST)
	public @ResponseBody StatusVO deleteInsuranceDetails(HttpServletRequest request,
			@RequestBody InsuranceVO insuranceVO) throws Exception {
		StatusVO statusVO = new StatusVO();
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			patientService.deleteInsuranceDetails(insuranceVO.getInsuranceId());
			String[] messages = { "Opertion Successfull" };
			statusVO.setMessages(messages);
			statusVO.setSuccess(true);
		}
		return statusVO;
	}

	@RequestMapping(value = "/getPatientCount", method = RequestMethod.GET)
	public @ResponseBody ValidationCountVO getValidationPendingCount(HttpServletRequest request) throws Exception {
		ValidationCountVO result = new ValidationCountVO();
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				Long currentMonthPatient = patientService.patientCountMonth(user.getOrgId());

				List<Object[]> patientsList = new ArrayList<>();
				Long patientsCount = patientService.getAllPatientsCountByOrgId(user.getRoleType(), user.getOrgId(),
						user.getUserId());
				patientsList = patientService.getPatientBillingAddressInfo(user.getOrgId(), 0, patientsCount.intValue(),
						user.getUserId());

				LocalDate currentDateLdt = LocalDate.now();
				Date endDate = Date.from(currentDateLdt.atStartOfDay(ZoneId.systemDefault()).toInstant());

				LocalDate startDateLdt = currentDateLdt.with(TemporalAdjusters.firstDayOfMonth());
				Date startDate = Date.from(startDateLdt.atStartOfDay(ZoneId.systemDefault()).toInstant());

				for (Object[] patient : patientsList) {
					Map<String, Integer> dataCnt = patientService
							.validationInfoByStartAndEndDate(((BigInteger) patient[0]).longValue(), startDate, endDate);
					if (dataCnt != null && (dataCnt.get("rpmMins") >= 20 || dataCnt.get("ccmMins") >= 20)
							&& dataCnt.get("daysCounted") >= 16) {
						result.setValidationPendingCount(
								result.getValidationPendingCount() != null ? result.getValidationPendingCount() + 1L
										: 1);
					} else {
						result.setValidationSubmittedCount(
								result.getValidationSubmittedCount() != null ? result.getValidationSubmittedCount() + 1L
										: 1);
					}
				}
				result.setTotalPatientCount(patientsCount);
				result.setTotalPatientCountLastMonth(currentMonthPatient);
				String[] messages = { "Opertion Successfull" };
				result.setMessages(messages);
				result.setSuccess(true);
			}
			return result;
		} catch (WatchRxServiceException e) {
			e.printStackTrace();
			logger.info("Service Exception");
			result.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = e.getMessage();
			result.setMessages(messages);
			result.setSuccess(false);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("Service Exception");
			result.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = e.getMessage();
			result.setMessages(messages);
			result.setSuccess(false);
			return result;
		}
	}

	@RequestMapping(value = "/getValidationForPatient/{patientId}/{date}", method = RequestMethod.GET)
	public @ResponseBody ValidationVO getValidationForPatient(HttpServletRequest request, @PathVariable Long patientId,
			@PathVariable String date) throws Exception {
		ValidationVO result = new ValidationVO();
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			result = patientService.getValidationForPatient(patientId, date);
			result.setCountInfo(patientService.validationInfo(patientId, date));
			if (result.getLastUpdatedBy() == null) {
				result.setLastUpdatedBy(user.getFirstName() + " " + user.getLastName());
				result.setLastUpdatedByUserId(user.getUserId());
			}
			String[] messages = { "Opertion Successfull" };
			result.setMessages(messages);
			result.setSuccess(true);
		}
		return result;
	}

	@RequestMapping(value = "/saveValidationForPatient", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveValidationForPatient(HttpServletRequest request,
			@RequestBody ValidationVO validationVO) throws Exception {
		StatusVO statusVO = new StatusVO();
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			validationVO.setLastUpdatedByUserId(user.getUserId());
			boolean result = patientService.updateValidationForPatient(validationVO);
			if (result) {
				String[] messages = { "Opertion Successfull" };
				statusVO.setMessages(messages);
				statusVO.setSuccess(true);
			} else {
				String[] messages = { "Failed to Update Validation" };
				statusVO.setMessages(messages);
				statusVO.setSuccess(false);
			}
		}
		return statusVO;
	}

	@RequestMapping(value = "/getLatestTextMessages", method = RequestMethod.GET)
	public @ResponseBody TextMessageResponseListVO getLatestTextMessagesAllPatients(HttpServletRequest request)
			throws Exception {
		TextMessageResponseListVO result = new TextMessageResponseListVO();
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			result = patientService.getLatestTextMessagesAllPatients(user.getUserId(), user.getRoleType());
			String[] messages = { "Opertion Successfull" };
			result.setMessages(messages);
			result.setSuccess(true);
		}
		return result;
	}

	@RequestMapping(value = "/saveThresholdConfig", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveThresholdConfig(HttpServletRequest request,
			@RequestBody ThresholdConfigVO thresholdConfigVO) throws Exception {
		StatusVO resp = new StatusVO();
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			boolean result = patientService.saveThresholdConfig(thresholdConfigVO);

			if (result) {
				resp.setSuccess(true);
				String[] messages = { "Opertion Successfull" };
				resp.setMessages(messages);
			} else {
				resp.setSuccess(true);
				String[] messages = { "Could Not Save Threshold" };
				resp.setMessages(messages);
			}
		}
		return resp;
	}

	@RequestMapping(value = "/getThresholdConfig/{patientId}", method = RequestMethod.GET)
	public @ResponseBody ThresholdConfigVO getThresholdConfig(HttpServletRequest request, @PathVariable Long patientId)
			throws Exception {
		ThresholdConfigVO result = new ThresholdConfigVO();
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			result = patientService.getThresholdConfig(patientId);
			if (!result.isSuccess()) {
				return result;
			}
			String[] messages = { "Opertion Successfull" };
			result.setMessages(messages);
			result.setSuccess(true);
		}
		return result;
	}

	@RequestMapping(value = "/getAllScreeningConditions", method = RequestMethod.GET)
	public @ResponseBody ScreeningConditionListVO getAllScreeningConditions(HttpServletRequest request)
			throws Exception {
		ScreeningConditionListVO result = new ScreeningConditionListVO();
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user == null) {
				result.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				result.setMessages(messages);
				result.setSuccess(false);
				return result;
			}
			result = patientService.getAllScreeningConditions();
			if (!result.isSuccess()) {
				return result;
			}
			String[] messages = { "Opertion Successfull" };
			result.setMessages(messages);
			result.setSuccess(true);
			return result;
		} catch (WatchRxServiceException e) {
			logger.info("Service Exception");
			result.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = e.getMessage();
			result.setMessages(messages);
			result.setSuccess(false);
			return result;
		} catch (Exception e) {
			logger.info("Exception : " + e.getMessage());
			result.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			result.setMessages(messages);
			result.setSuccess(false);
			return result;
		}
	}

	@RequestMapping(value = "/saveScreeningCondition", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveScreeningCondition(HttpServletRequest request,
			@RequestBody ScreeningConditionVO screeningConditionVO) throws Exception {
		StatusVO result = new StatusVO();
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user == null) {
				result.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				result.setMessages(messages);
				result.setSuccess(false);
				return result;
			}
			boolean resp = patientService.saveScreeningCondition(screeningConditionVO);
			if (resp) {
				result.setSuccess(true);
				String[] messages = { "Opertion Successfull" };
				result.setMessages(messages);
			} else {
				result.setResponseCode(Constants.ResponseCode.INTERNALERROR);
				String[] messages = new String[1];
				messages[0] = "Failed to saved screening condition";
				result.setMessages(messages);
				result.setSuccess(false);
			}
			return result;
		} catch (WatchRxServiceException e) {
			logger.info("Service Exception");
			result.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = e.getMessage();
			result.setMessages(messages);
			result.setSuccess(false);
			return result;
		} catch (Exception e) {
			logger.info("Exception : " + e.getMessage());
			result.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			result.setMessages(messages);
			result.setSuccess(false);
			return result;
		}
	}

	@RequestMapping(value = "/saveScreeningInfo", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveScreeningInfo(HttpServletRequest request,
			@RequestBody ScreeningListVO screeningListVO) throws Exception {
		StatusVO resp = new StatusVO();
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user == null) {
				resp.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				resp.setMessages(messages);
				resp.setSuccess(false);
				return resp;
			}
			boolean result = patientService.saveScreeningInfo(screeningListVO);
			if (result) {
				resp.setSuccess(true);
				String[] messages = { "Screening info saved succeffuly." };
				resp.setMessages(messages);
			} else {
				resp.setSuccess(false);
				String[] messages = { "Failed to save screening info." };
				resp.setMessages(messages);
			}
			return resp;
		} catch (WatchRxServiceException e) {
			logger.info("Service Exception");
			resp.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = e.getMessage();
			resp.setMessages(messages);
			resp.setSuccess(false);
			return resp;
		} catch (Exception e) {
			logger.info("Exception : " + e.getMessage());
			resp.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			resp.setMessages(messages);
			resp.setSuccess(false);
			return resp;
		}
	}

	@RequestMapping(value = "/getScreeningInfo/{patientId}", method = RequestMethod.GET)
	public @ResponseBody ScreeningListVO getScreeningInfo(HttpServletRequest request, @PathVariable Long patientId)
			throws Exception {
		ScreeningListVO result = new ScreeningListVO();
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user == null) {
				result.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				result.setMessages(messages);
				result.setSuccess(false);
				return result;
			}
			result = patientService.getScreeningInfo(patientId);
			if (!result.isSuccess()) {
				return result;
			}
			String[] messages = { "Opertion Successfull" };
			result.setMessages(messages);
			result.setSuccess(true);
			return result;
		} catch (WatchRxServiceException e) {
			logger.info("Service Exception");
			result.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = e.getMessage();
			result.setMessages(messages);
			result.setSuccess(false);
			return result;
		} catch (Exception e) {
			logger.info("Exception : " + e.getMessage());
			result.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			result.setMessages(messages);
			result.setSuccess(false);
			return result;
		}
	}

	@RequestMapping(value = "/doScreening", method = RequestMethod.GET)
	public @ResponseBody void getScreeningInfo(HttpServletRequest request) throws Exception {
		patientService.runScreening();
	}

	@RequestMapping(value = "/saveScreeningValues", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveScreeningValues(HttpServletRequest request,
			@RequestBody ScreeningValuesListVO screeningValuesListVO) throws Exception {
		StatusVO resp = new StatusVO();
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			boolean result = patientService.saveScreeningValues(screeningValuesListVO);

			if (result) {
				resp.setSuccess(true);
				String[] messages = { "Opertion Successfull" };
				resp.setMessages(messages);
			} else {
				resp.setSuccess(true);
				String[] messages = { "Could Not Save ScreeningInfo" };
				resp.setMessages(messages);
			}
		}
		return resp;
	}

	@RequestMapping(value = "/getAllPatientsMinimal/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody BaseListWithCountVO<PatientMinimal2VO, Long> getAllPatientsMinimal(HttpServletRequest request,
			@PathVariable Integer index, @PathVariable Integer pageSize) throws Exception {
		UserVO user = null;
		BaseListWithCountVO<PatientMinimal2VO, Long> response = new BaseListWithCountVO<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			List<PatientMinimal2VO> patienMinimal2VOList = patientService.getAllPatientsMinimal(index, pageSize);
			response.seteList(patienMinimal2VOList);
			response.setCount(patientService.getAllPatientsCount());
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		}
		return response;
	}

	@RequestMapping(value = "/verifyValidationForPatient", method = RequestMethod.POST)
	public @ResponseBody StatusVO verifyValidationForPatient(HttpServletRequest request,
			@RequestBody ValidationListVO validationVO) throws Exception {
		StatusVO statusVO = new StatusVO();
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		logger.info("Request for the verifyValidationForPatient ");

		if (validationVO.getPatientId() != null) {
			for (Long patient : validationVO.getPatientId()) {
				logger.info(" Patient Id : " + patient);
			}
		}

		if (user != null) {
			validationVO.setLastUpdatedByUserId(user.getUserId());
			boolean result = patientService.verifyValidation(validationVO);
			if (result) {
				String[] messages = { "Opertion Successfull" };
				statusVO.setMessages(messages);
				statusVO.setSuccess(true);
			} else {
				String[] messages = { "Failed to Update Validation" };
				statusVO.setMessages(messages);
				statusVO.setSuccess(false);
			}
		}
		return statusVO;
	}

	@RequestMapping(value = "/resetStatusMonthBegining", method = RequestMethod.GET)
	public @ResponseBody void resetStatusMonthBegining(HttpServletRequest request) throws Exception {
		logger.info("BEGIN: Reseting patient Validation and StateTable Reseting ");
		patientService.resetStatusMonthBegining();
		logger.info("END: Reseting patient Validation and StateTable Reseting ");
	}

	@RequestMapping(value = "/getPatientBillingStatus/{patientId}", method = RequestMethod.GET)
	public @ResponseBody PatientBillingStatusVO getPatientBillingStatus(HttpServletRequest request,
			@PathVariable Long patientId) throws Exception {
		UserVO user = null;
		PatientBillingStatusVO response = new PatientBillingStatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			response = patientService.patientBillingStatus(patientId);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		}
		return response;
	}

	@RequestMapping(value = "/getTextMessageAlertsCount", method = RequestMethod.GET)
	public @ResponseBody TextMessageAlertsCountVO getTextMessageAlertsCount(HttpServletRequest request)
			throws Exception {
		UserVO user = null;
		TextMessageAlertsCountVO response = new TextMessageAlertsCountVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response.setTestMessageResponseCount(
					patientService.getRespondedTextMessageCount(user.getUserId(), (long) user.getRoleType()));
			response.setCriticalAlertsCount(
					patientService.getCriticalAlertCount(user.getUserId(), (long) user.getRoleType()));
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		}
		return response;
	}

	private void sendScheduledMessageInfoByPatientId(Long patientId, Long scheduledTextMessageId, String status) {
		logger.info("Sending Scheduled Message info GCM::");
		CareGiverTest sdetails = new CareGiverTest();
		RegisterWatch watch = new RegisterWatch();
		PatientVO pdvo = WatchRxFactory.getPatientService().getPatient(patientId);
		logger.info("Sending Scheduled Message info patient id::" + patientId);
		logger.info("Sending Scheduled Message info scheduledTextMessage id::" + scheduledTextMessageId);
		logger.info("Sending Scheduled Message info status ::" + status);
		if (pdvo != null && pdvo.getWatch() != null) {
			String imeiNo = pdvo.getWatch().getImeiNumber();
			if (imeiNo != null) {
				logger.info("Sending Scheduled Message info GCM IMEI NO::" + imeiNo);
				watch.setImeiNo(imeiNo);

				WatchRxFactory.getPatientService().getPatientDetailsByImei(watch);

				PatientInfo gcmId = WatchRxFactory.getPatientService().getPatientById(patientId);
				logger.info("Sending Scheduled Message info GCM ID::" + gcmId);
				if (gcmId.getGcmId() != null && gcmId.getGcmId().trim().length() > 0) {
					JsonObject jo = new JsonObject();
					jo.addProperty("messageType", "ScheduledMessageUpdated");

					List<ScheduledTextMessageInfo> scheduledTextMessageInfoList = patientService
							.getScheduledTextMessageWatchByScheduledTextMessageId(patientId, scheduledTextMessageId,
									status);
					if (status.equalsIgnoreCase("delete")) {
						ScheduledTextMessageInfo scheduledTextMessageInfo = new ScheduledTextMessageInfo();
						scheduledTextMessageInfo.setScheduledTextMessagesId(scheduledTextMessageId);
						scheduledTextMessageInfo.setStatus("delete");
						scheduledTextMessageInfoList.add(scheduledTextMessageInfo);
					}
					jo.add("scheduledTextMessageInfoList", new Gson().toJsonTree(scheduledTextMessageInfoList));
					Gson gson = new Gson();
					String scheduleTextMessageVOListString = gson.toJson(scheduledTextMessageInfoList);
					System.out.println(scheduleTextMessageVOListString);

					String jsonStr = gson.toJson(jo);

					System.out.println(jsonStr);
					try {
						sdetails.sendMessageToPatient("message", jsonStr, gcmId.getGcmId(), gcmId.getPlatform());
						logger.info("Sending medication info GCM ID Send::");

					} catch (IOException e) {
						// TODO Auto-generated catch block
						logger.error("Something went wrong while sending GCM: " + e.getMessage());
						e.printStackTrace();
					}

					//// jo.addProperty("patientDetails", pd.toString());
					// logger.info("Sending medication info GCM ID details::" + pd.toString());
					//
					// List<ScheduledTextMessageInfo> scheduledTextMessageInfoList = patientService
					// .getScheduledTextMessageWatch(patientId);
					// Gson gson = new Gson();
					// String scheduleTextMessageVOListString =
					//// gson.toJson(scheduledTextMessageInfoList);
					// System.out.println(scheduleTextMessageVOListString);
					// jo.add("scheduledTextMessageInfoList", new
					//// Gson().toJsonTree(scheduledTextMessageInfoList));
					//
					// String jsonStr = gson.toJson(jo);
					//
					// System.out.println(jsonStr);
					// try {
					// sdetails.sendMessageToPatient("message", jsonStr, gcmId);
					// logger.info("Sending medication info GCM ID Send::");
					//
					// } catch (IOException e) {
					// // TODO Auto-generated catch block
					// logger.error("Something went wrong while sending GCM: " + e.getMessage());
					// e.printStackTrace();

				}
			}
		}
	}

	@RequestMapping(value = "/saveScheduledTextMessage", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveScheduledMessages(HttpServletRequest request,
			@RequestBody ScheduleTextMessageVO scheduleTextMessageVO) throws Exception {
		StatusVO resp = new StatusVO();
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			String status = scheduleTextMessageVO.getScheduledTextMessagesId() == null ? "save" : "update";
			boolean result = patientService.saveScheduledTextMessage(scheduleTextMessageVO);
			if (result) {
				System.out.println(scheduleTextMessageVO.getScheduledTextMessagesId());
				sendScheduledMessageInfoByPatientId(scheduleTextMessageVO.getPatientId(),
						scheduleTextMessageVO.getScheduledTextMessagesId(), status);

				resp.setSuccess(true);

				String[] messages = { "Opertion Successfull" };
				resp.setMessages(messages);
			} else {
				resp.setSuccess(false);
				String[] messages = { "Could Not Save Scheduled Message" };
				resp.setMessages(messages);
			}
		}
		return resp;
	}

	@RequestMapping(value = "/deleteScheduledTextMessage", method = RequestMethod.POST)
	public @ResponseBody StatusVO deleteScheduledTextMessage(HttpServletRequest request,
			@RequestBody ScheduleTextMessageVO scheduleTextMessageVO) throws Exception {
		StatusVO resp = new StatusVO();
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			boolean result = patientService.deleteScheduledTextMessage(scheduleTextMessageVO);

			if (result) {
				sendScheduledMessageInfoByPatientId(scheduleTextMessageVO.getPatientId(),
						scheduleTextMessageVO.getScheduledTextMessagesId(), "delete");
				resp.setSuccess(true);

				String[] messages = { "Opertion Successfull" };
				resp.setMessages(messages);
			} else {
				resp.setSuccess(true);
				String[] messages = { "Could Not Save Scheduled Message" };
				resp.setMessages(messages);
			}
		}
		return resp;
	}

	@RequestMapping(value = "/getScheduledTextMessage/{patientId}/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody BaseListWithCountVO<ScheduleTextMessageVO, Long> getScheduledTextMessage(
			HttpServletRequest request, @PathVariable Long patientId, @PathVariable Integer index,
			@PathVariable Integer pageSize) throws Exception {
		UserVO user = null;
		BaseListWithCountVO<ScheduleTextMessageVO, Long> response = new BaseListWithCountVO<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			List<ScheduleTextMessageVO> scheduleTextMessageVOList = patientService.getScheduledTextMessage(patientId,
					index, pageSize);
			response.seteList(scheduleTextMessageVOList);
			response.setCount(patientService.getScheduledTextMessageCount(patientId));
			response.setSuccess(true);

			PatientDocumentationListVO rpmcmcmData = patientService.getPatientRpmCcmPcmMins(patientId);
			response.setRpmMins(rpmcmcmData.getRpmCount());
			response.setCcmMins(rpmcmcmData.getCcmCount());
			response.setPcmMins(rpmcmcmData.getPcmCount());

			String minsStr = getMinsStr(patientId, rpmcmcmData);
			response.setMinsDetails(minsStr);
			List<Map<String, Object>> programs = patientService.getPatientProgramsListByPId(patientId);
			response.setPrograms(programs);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		}
		return response;
	}

	private String getMinsStr(Long patientId, PatientDocumentationListVO rpmcmcmData) {
		List<String> pList = patientService.getPatientProgramsList(patientId);
		String minsStr = "";
		if (pList != null && pList.size() > 0) {
			if (pList.contains("RPM")) {
				minsStr = minsStr
						.concat("<b>RPM : <mark>" + getMinsSecs(rpmcmcmData.getRpmCount()) + "</mark></b>&nbsp;");
			}
			if (pList.contains("CCM")) {
				minsStr = minsStr
						.concat("<b>CCM : <mark>" + getMinsSecs(rpmcmcmData.getCcmCount()) + "</mark></b>&nbsp;");
			}
			if (pList.contains("PCM")) {
				minsStr = minsStr
						.concat("<b>PCM : <mark>" + getMinsSecs(rpmcmcmData.getPcmCount()) + "</mark></b>&nbsp;");
			}
		}
		return minsStr;
	}

	@RequestMapping(value = "/savePatientDocumentation", method = RequestMethod.POST)
	public @ResponseBody StatusVO savePatientDocumentation(HttpServletRequest request,
			@RequestBody PatientDocumentationVO patientDocumentationVO) throws Exception {
		UserVO user = null;
		StatusVO statusVO = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			// phoneCommunicationVO.setUserId(user.getUserId());

			patientService.savePatientDocumentation(patientDocumentationVO);
			statusVO.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			statusVO.setMessages(messages);
		}

		return statusVO;
	}

	@RequestMapping(value = "/getPatientDocumentation/{patientId}/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody PatientDocumentationListVO getPatientDocumentation(HttpServletRequest request,
			@PathVariable Long patientId, @PathVariable Integer index, @PathVariable Integer pageSize)
			throws Exception {
		UserVO user = null;
		PatientDocumentationListVO patientDocumentationListVO = new PatientDocumentationListVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		Long count = 0L;
		if (user != null) {
			PatientVO patientVO = new PatientVO();
			patientVO.setPatientId(patientId);
			patientDocumentationListVO = patientService.getPatientDocumentation(patientVO, index, pageSize);
			count = patientService.getPatientDocumentationCount(patientVO);
		}
		patientDocumentationListVO.setSuccess(true);
		patientDocumentationListVO.setCount(count);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		patientDocumentationListVO.setMessages(messages);
		return patientDocumentationListVO;
	}

	@RequestMapping(value = "/deletePatientDocumentation", method = RequestMethod.POST)
	public @ResponseBody StatusVO deletePatientDocumentation(HttpServletRequest request,
			@RequestBody PatientDocumentationVO patientDocumentationVO) throws Exception {
		StatusVO statusVO = null;
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			statusVO = patientService.deletePatientDocumentation(patientDocumentationVO);
		}
		return statusVO;
	}

	@RequestMapping(value = "/updatePhysician", method = RequestMethod.POST)
	public @ResponseBody StatusVO updatePhysician(HttpServletRequest request,
			@RequestBody PatientWatchCaregiverInfoVO patientWatchCaregiverInfoVO) throws Exception {
		StatusVO statusVO = new StatusVO();
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			boolean success = patientService.updatePhysician(patientWatchCaregiverInfoVO);
			if (success) {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				statusVO.setMessages(messages);
			} else {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation failed";
				statusVO.setMessages(messages);
			}
		}
		return statusVO;
	}

	@RequestMapping(value = "/saveSleepMonitoringConfiguration", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveSleepMonitoringConfiguration(HttpServletRequest request,
			@RequestBody SleepMonitoringConfigurationVO sleepMonitoringConfigurationVO) throws Exception {
		UserVO user = null;
		StatusVO statusVO = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			boolean success = patientService.saveSleepMonitoringConfiguration(sleepMonitoringConfigurationVO);
			if (success) {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				statusVO.setMessages(messages);
			} else {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation failed";
				statusVO.setMessages(messages);
			}
		}

		return statusVO;
	}

	@RequestMapping(value = "/enableDisablePedometer", method = RequestMethod.POST)
	public @ResponseBody StatusVO enableDisablePedometer(HttpServletRequest request,
			@RequestBody PedometerConfigurationVO pedometerConfigurationVO) throws Exception {
		UserVO user = null;
		StatusVO statusVO = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			boolean success = patientService.enableDisablePedometer(pedometerConfigurationVO);
			if (success) {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				statusVO.setMessages(messages);
			} else {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation failed";
				statusVO.setMessages(messages);
			}
		}

		return statusVO;
	}

	@RequestMapping(value = "/resetPedometer", method = RequestMethod.POST)
	public @ResponseBody StatusVO resetPedometer(HttpServletRequest request,
			@RequestBody PedometerConfigurationVO pedometerConfigurationVO) throws Exception {
		UserVO user = null;
		StatusVO statusVO = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			boolean success = patientService.resetPedometer(pedometerConfigurationVO.getPatientId());
			if (success) {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				statusVO.setMessages(messages);
			} else {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation failed";
				statusVO.setMessages(messages);
			}
		}

		return statusVO;
	}

	@RequestMapping(value = "/getPedometerReadingCountNow", method = RequestMethod.POST)
	public @ResponseBody StatusVO getPedometerReadingCountNow(HttpServletRequest request,
			@RequestBody PedometerConfigurationVO pedometerConfigurationVO) throws Exception {
		UserVO user = null;
		StatusVO statusVO = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			boolean success = patientService.getPedometerReadingCountNow(pedometerConfigurationVO.getPatientId());
			if (success) {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				statusVO.setMessages(messages);
			} else {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation failed";
				statusVO.setMessages(messages);
			}
		}

		return statusVO;
	}

	@RequestMapping(value = "/getPedometerConfiguration/{patientId}", method = RequestMethod.GET)
	public @ResponseBody PedometerConfigurationVO getPedometerConfiguration(HttpServletRequest request,
			@PathVariable Long patientId) throws Exception {
		UserVO user = null;
		PedometerConfigurationVO result = new PedometerConfigurationVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			PatientVO patientVO = new PatientVO();
			patientVO.setPatientId(patientId);
			result = patientService.getPedometerConfiguration(patientId);

		}
		result.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		result.setMessages(messages);
		return result;
	}

	@RequestMapping(value = "/getPedometerReadingList/{patientId}/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody BaseListWithCountVO<PedometerReadingVO, Long> getPedometerReadingList(
			HttpServletRequest request, @PathVariable Long patientId, @PathVariable Integer index,
			@PathVariable Integer pageSize) throws Exception {
		UserVO user = null;
		BaseListWithCountVO<PedometerReadingVO, Long> response = new BaseListWithCountVO<>();

		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			List<PedometerReadingVO> pedometerReadingVOList = patientService.getPedometerReadingList(patientId, index,
					pageSize);
			response.seteList(pedometerReadingVOList);
			response.setCount(patientService.getPedometerReadingCount(patientId));

		}
		response.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		response.setMessages(messages);
		return response;
	}

	@RequestMapping(value = "/getPedometerReports", method = RequestMethod.POST)
	public @ResponseBody BaseListVO<PedometerReadingVO> getPedometerReports(HttpServletRequest request,
			@RequestBody AlertsCountRequestVO requestBody) throws Exception {
		UserVO user = null;
		BaseListVO<PedometerReadingVO> response = new BaseListVO<>();
		List<PedometerReadingVO> pedometerReadingListVO = new ArrayList<>();

		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);

		if (user != null) {

			pedometerReadingListVO = patientService.getPedometerReportsNew(requestBody);
			response.seteList(pedometerReadingListVO);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		}
		return response;
	}

	@RequestMapping(value = "/generateAllPatientCummulativeData", method = RequestMethod.GET)
	public @ResponseBody BaseListWithCountVO<PedometerReadingVO, Long> getPedometerReadingList(
			HttpServletRequest request) throws Exception {
		UserVO user = null;
		BaseListWithCountVO<PedometerReadingVO, Long> response = new BaseListWithCountVO<>();

		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			patientService.generateAllPatientCummulativeData();
		}
		response.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		response.setMessages(messages);
		return response;
	}

	@RequestMapping(value = "/scheduleTaskWithCronExpression", method = RequestMethod.GET)
	public @ResponseBody StatusVO scheduleTaskWithCronExpression(HttpServletRequest request) throws Exception {

		ScheduledForMessage scheduledForMessage = new ScheduledForMessage();
		scheduledForMessage.scheduleTaskWithCronExpression();
		StatusVO response = new StatusVO();
		response.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		response.setMessages(messages);
		return response;
	}

	@RequestMapping(value = "/setCptCodes", method = RequestMethod.POST)
	public @ResponseBody StatusVO getPedometerReports(HttpServletRequest request,
			@RequestBody BaseListVO<CptCodeVO> requestBody) throws Exception {
		UserVO user = null;

		List<CptCodeVO> cptCodeVOList = requestBody.geteList();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);

		StatusVO response = new StatusVO();
		if (user != null) {

			boolean success = patientService.setCptCodes(cptCodeVOList);
			if (success) {
				response.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				response.setMessages(messages);
			} else {
				response.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation failed";
				response.setMessages(messages);
			}
		}
		return response;
	}

	@RequestMapping(value = "/getAllCptCodes", method = RequestMethod.GET)
	public @ResponseBody BaseListVO<CptCodeVO> getAllCptCodes(HttpServletRequest request) throws Exception {
		UserVO user = null;
		BaseListVO<CptCodeVO> response = new BaseListVO<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);

		if (user != null) {
			List<CptCodeVO> cptCodeVOList = new ArrayList<>();

			cptCodeVOList = patientService.getAllCptCodes(false);
			response.seteList(cptCodeVOList);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);

		}
		return response;
	}

	@RequestMapping(value = "/getAllCCMCodes", method = RequestMethod.GET)
	public @ResponseBody BaseListVO<CptCodeVO> getAllCCMCodes(HttpServletRequest request) throws Exception {
		UserVO user = null;
		BaseListVO<CptCodeVO> response = new BaseListVO<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);

		if (user != null) {
			List<CptCodeVO> cptCodeVOList = new ArrayList<>();

			cptCodeVOList = patientService.getAllCptCodes(true);
			response.seteList(cptCodeVOList);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);

		}
		return response;
	}

	@RequestMapping(value = "/setChronicConditions", method = RequestMethod.POST)
	public @ResponseBody StatusVO setAllChronicConditions(HttpServletRequest request,
			@RequestBody BaseListVO<AllChronicConditionsVO> requestBody) throws Exception {
		UserVO user = null;

		List<AllChronicConditionsVO> allChronicConditionsVOList = requestBody.geteList();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);

		StatusVO response = new StatusVO();
		if (user != null) {

			boolean success = patientService.setChronicConditionsChronicConditions(allChronicConditionsVOList);
			if (success) {
				response.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				response.setMessages(messages);
			} else {
				response.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation failed";
				response.setMessages(messages);
			}
		}
		return response;
	}

	@RequestMapping(value = "/getAllChronicConditions/{type}", method = RequestMethod.GET)
	public @ResponseBody BaseListVO<AllChronicConditionsVO> getAllChronicConditions(HttpServletRequest request,
			@PathVariable String type) throws Exception {
		UserVO user = null;
		BaseListVO<AllChronicConditionsVO> response = new BaseListVO<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);

		if (user != null) {
			List<AllChronicConditionsVO> allChronicConditionsVOList = new ArrayList<>();

			allChronicConditionsVOList = patientService.getAllChronicConditions(type);
			response.seteList(allChronicConditionsVOList);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);

		}
		return response;
	}

	@RequestMapping(value = "/scheduleTextMessageAutoAddMessageIfWatchDoesNotSendCronExpression", method = RequestMethod.GET)
	public @ResponseBody StatusVO scheduleTextMessageAutoAddMessageIfWatchDoesNotSendCronExpression(
			HttpServletRequest request) throws Exception {

		patientService.addMessageNotCollectedByWatch();
		StatusVO response = new StatusVO();
		response.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		response.setMessages(messages);
		return response;
	}

	@RequestMapping(value = "/saveCollectVitalsFromMedicalDeviceConfiguration", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveVitalsFromMedicalDeviceConfiguration(HttpServletRequest request,
			@RequestBody CollectVitalsFromMedicalDeviceConfigurationVO configVO) throws Exception {
		UserVO user = null;
		StatusVO statusVO = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			statusVO = patientService.saveVitalsFromMedicalDeviceConfiguration(configVO);
			if (statusVO.isSuccess()) {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				statusVO.setMessages(messages);
			}
			// else {
			// statusVO.setSuccess(true);
			// String messages[] = new String[1];
			// messages[0] = "Operation failed";
			// statusVO.setMessages(messages);
			// }
		}

		return statusVO;
	}

	@RequestMapping(value = "/getCollectVitalsFromMedicalDeviceConfiguration/{patientId}", method = RequestMethod.GET)
	public @ResponseBody CollectVitalsFromMedicalDeviceConfigurationVO getCollectVitalsFromMedicalDeviceConfiguration(
			HttpServletRequest request, @PathVariable Long patientId) throws Exception {
		UserVO user = null;
		CollectVitalsFromMedicalDeviceConfigurationVO response = new CollectVitalsFromMedicalDeviceConfigurationVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);

		if (user != null) {

			response = patientService.getVitalsFromMedicalDeviceConfiguration(patientId);

			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);

		}
		return response;
	}

	@RequestMapping(value = "/activeCriticalAlertsCurrentMonth/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody BaseListWithCountVO<AlertResponseVO, Long> activeCriticalAlertsCurrentMonth(
			HttpServletRequest request, @PathVariable Integer index, @PathVariable Integer pageSize
			) throws Exception {
		UserVO user = null;
		BaseListWithCountVO<AlertResponseVO, Long> response = new BaseListWithCountVO<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			if (user.getRoleType() == 3) {

				List<AlertResponseVO> alertResponseVOList = new ArrayList<>();

				alertResponseVOList = patientService.activeCriticalAlertsCurrentMonthV1(user.getOrgId(), index,
						pageSize);
				response.setCount(patientService.activeCriticalAlertsCurrentMonthCountV1(user.getOrgId()));

				response.seteList(alertResponseVOList);
				response.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				response.setMessages(messages);
			} else if (user.getRoleType() == 5) {
				Long clinicianID = clinicianService.getClinicianIdForUserId(user.getUserId(),
						(long) user.getRoleType());
				List<AlertResponseVO> alertResponseVOList = new ArrayList<>();

				alertResponseVOList = patientService.activeCriticalAlertsCurrentMonth(clinicianID, user.getOrgId(), index, pageSize);
				response.setCount(patientService.activeCriticalAlertsCurrentMonthCount(clinicianID, user.getOrgId()));

				response.seteList(alertResponseVOList);
				response.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				response.setMessages(messages);
			} else {
				response.setSuccess(false);
				String messages[] = new String[1];
				messages[0] = "Operation failed";
				response.setMessages(messages);
			}
		}
		return response;
	}

	@RequestMapping(value = "/updateAlertIsAcknowledged", method = RequestMethod.POST)
	public @ResponseBody StatusVO updateAlertIsOpen(HttpServletRequest request,
			@RequestBody AlertResponseVO alertResponseVO) throws Exception {
		UserVO user = null;
		StatusVO statusVO = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			boolean success = patientService.updateAlertIsAcknowledged(alertResponseVO);
			if (success) {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				statusVO.setMessages(messages);
			} else {
				statusVO.setSuccess(false);
				String messages[] = new String[1];
				messages[0] = "Operation failed";
				statusVO.setMessages(messages);
			}
		} else {
			statusVO.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation failed";
			statusVO.setMessages(messages);
		}

		return statusVO;
	}

	@RequestMapping(value = "/numberOfPatientsCriticalAlertsCount", method = RequestMethod.GET)
	public @ResponseBody PatientCriticalAlertsCountVO updateAlertIsOpen(HttpServletRequest request) throws Exception {
		UserVO user = null;
		PatientCriticalAlertsCountVO response = new PatientCriticalAlertsCountVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			Long count = 0l;
			count = patientService.numberOfPatientsWithActiveCriticalAlertsCount(user.getOrgId());
			response.setNumberOfPatientsWithActiveCriticalAlertsCount(count);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		} else {
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation failed";
			response.setMessages(messages);
		}
		return response;
	}

	@RequestMapping(value = "/getVitalShedules", method = RequestMethod.POST)
	public @ResponseBody BaseListVO<VitalScheduleVO> getVitalShedules(HttpServletRequest request,
			@RequestBody VitalScheduleRequestVO vitalScheduleRequestVO) throws Exception {
		UserVO user = null;
		BaseListVO<VitalScheduleVO> response = new BaseListVO<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			List<VitalScheduleVO> vitalScheduleVOList = patientService.getVitalShedules(vitalScheduleRequestVO);
			response.seteList(vitalScheduleVOList);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);

		} else {
			response.setSuccess(true);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/getVitalReadings", method = RequestMethod.POST)
	public @ResponseBody BaseListWithCountVO<VitalReadingVO, Long> getVitalReadings(HttpServletRequest request,
			@RequestBody VitalScheduleRequestVO vitalScheduleRequestVO)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		BaseListWithCountVO<VitalReadingVO, Long> response = new BaseListWithCountVO<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			List<VitalReadingVO> vitalReadingVOList = patientService.getVitalReadings(vitalScheduleRequestVO);
			response.seteList(vitalReadingVOList);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);

		} else {
			response.setSuccess(true);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/saveVitalReadings", method = RequestMethod.POST)
	public @ResponseBody StatusVO getVitalReadings(HttpServletRequest request,
			@RequestBody VitalReadingVO vitalReadingVO) throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		StatusVO response = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			vitalReadingVO.setUserId(user.getUserId());
			response = patientService.saveVitalReadings(vitalReadingVO);

		} else {
			response.setSuccess(true);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/saveVitalSchedule", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveVitalSchedule(HttpServletRequest request,
			@RequestBody VitalScheduleVO vitalScheduleVO) throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		StatusVO response = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			StatusVO result = patientService.saveVitalShedules(vitalScheduleVO);

			return result;

		} else {
			response.setSuccess(true);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/deleteVitalSchedule", method = RequestMethod.POST)
	public @ResponseBody StatusVO deleteVitalSchedule(HttpServletRequest request,
			@RequestBody VitalScheduleVO vitalScheduleVO) throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		StatusVO response = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			Boolean result = patientService.deleteVitalShedules(vitalScheduleVO);
			if (result) {
				response.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				response.setMessages(messages);
			} else {
				response.setSuccess(false);
				String messages[] = new String[1];
				messages[0] = "Operation failed";
				response.setMessages(messages);
			}

		} else {
			response.setSuccess(true);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/enableVitalCollection", method = RequestMethod.POST)
	public @ResponseBody ThresholdConfigurationVO enableVitalCollection(HttpServletRequest request,
			@RequestBody VitalScheduleRequestVO vitalScheduleRequestVO)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		ThresholdConfigurationVO response = new ThresholdConfigurationVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.enableVitalCollection(vitalScheduleRequestVO);

		} else {
			response.setSuccess(true);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/getThresholdConfiguration", method = RequestMethod.POST)
	public @ResponseBody BaseListVO<ThresholdConfigurationVO> getThresholdConfiguration(HttpServletRequest request,
			@RequestBody BaseListVO<VitalScheduleRequestVO> vitalScheduleRequestListVO)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		BaseListVO<ThresholdConfigurationVO> response = new BaseListVO<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.getThresholdConfiguration(vitalScheduleRequestListVO);

		} else {
			response.setSuccess(true);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/saveThresholdConfiguration", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveThresholdConfiguration(HttpServletRequest request,
			@RequestBody BaseListVO<ThresholdConfigurationVO> thresholdConfigurationListVO)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		StatusVO response = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.saveThresholdConfiguration(thresholdConfigurationListVO);

		} else {
			response.setSuccess(true);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/getEnableStatus", method = RequestMethod.POST)
	public @ResponseBody ThresholdConfigurationVO getEnableStatus(HttpServletRequest request,
			@RequestBody VitalScheduleRequestVO vitalScheduleRequestVO)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		ThresholdConfigurationVO response = new ThresholdConfigurationVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.getEnableStatus(vitalScheduleRequestVO);

		} else {
			response.setSuccess(true);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/vitalGraph", method = RequestMethod.POST)
	public synchronized @ResponseBody VitalsCountGraphListVO vitalGraph(HttpServletRequest request,
			@RequestBody VitalRequestListVO vitalRequestListVO) throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		logger.info("Request for the Vital Daily Graph Patient Id : " + vitalRequestListVO.getPatientId()
				+ " ,PeriodType : " + vitalRequestListVO.getPeriodType() + " ,RequestedDate : "
				+ vitalRequestListVO.getRequestedDate());
		BaseListVO<VitalScheduleRequestVO> vitalReq = new BaseListVO<>();
		List<VitalScheduleRequestVO> vsrList = new ArrayList<>();
		if (vitalRequestListVO.getVitalTypeNameList() != null) {
			for (String vitalList : vitalRequestListVO.getVitalTypeNameList()) {
				logger.info(" Vital Type Name : " + vitalList);
				VitalScheduleRequestVO vsr = new VitalScheduleRequestVO();
				vsr.setVitalTypeName(vitalList);
				vsr.setPatientId(vitalRequestListVO.getPatientId());
				vsrList.add(vsr);
			}
		}
		vitalReq.seteList(vsrList);
		VitalsCountGraphListVO response = new VitalsCountGraphListVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.vitalGraphNew(vitalRequestListVO);
			response.setVitalCollnStatus(patientService.vitalStatus(vitalRequestListVO));
			response.setThresholdConfig(patientService.getThresholdConfiguration(vitalReq));
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		} else {
			response.setSuccess(false);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/vitalGraphWeekly", method = RequestMethod.POST)
	public @ResponseBody VitalWeeklyGraphListVO vitalGraphWeekly(HttpServletRequest request,
			@RequestBody VitalRequestListVO vitalRequestListVO) throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		VitalWeeklyGraphListVO response = new VitalWeeklyGraphListVO();

		logger.info("Request for the Vital Weekly/Monthly Graph Patient Id : " + vitalRequestListVO.getPatientId()
				+ " ,PeriodType : " + vitalRequestListVO.getPeriodType() + " ,RequestedDate : "
				+ vitalRequestListVO.getRequestedDate());
		BaseListVO<VitalScheduleRequestVO> vitalReq = new BaseListVO<>();
		List<VitalScheduleRequestVO> vsrList = new ArrayList<>();
		if (vitalRequestListVO.getVitalTypeNameList() != null) {
			for (String vitalList : vitalRequestListVO.getVitalTypeNameList()) {
				logger.info(" Vital Type Name : " + vitalList);
				VitalScheduleRequestVO vsr = new VitalScheduleRequestVO();
				vsr.setVitalTypeName(vitalList);
				vsr.setPatientId(vitalRequestListVO.getPatientId());
				vsrList.add(vsr);
			}
		}
		vitalReq.seteList(vsrList);
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);

		if (user != null) {
			if (vitalRequestListVO.getPeriodType() != null) {
				logger.info("Vital Graph requested for : " + vitalRequestListVO.getPeriodType());
				vitalRequestListVO.getPeriodType();
				response = patientService.getVitalGraphDetails(vitalRequestListVO);
				response.setThresholdConfig(patientService.getThresholdConfiguration(vitalReq));
			} else {
				response.setSuccess(false);
				String messages[] = new String[2];
				messages[0] = "Operation failed";
				messages[1] = "Period Type for Vital Request is not provided";
				response.setMessages(messages);
			}

			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		} else {

			response.setSuccess(false);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "Period Type for Vital Request is not provided";
			response.setMessages(messages);
		}

		response.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		response.setMessages(messages);
		/*
		 * } else { response.setSuccess(false); String messages[] = new String[2];
		 * messages[0] = "Operation failed"; messages[1] = "User Not Logged in";
		 * response.setMessages(messages); }
		 */

		return response;
	}

	@RequestMapping(value = "/vitalPedometerDailyGraph", method = RequestMethod.POST)
	public @ResponseBody VitalsCountGraphListVO vitalPedometerDailyGraph(HttpServletRequest request,
			@RequestBody VitalRequestListVO vitalRequestListVO) throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		VitalsCountGraphListVO response = new VitalsCountGraphListVO();
		logger.info("inside the PedometerDailyGraph");
		logger.info("Request for the Pedometer Daily Graph Patient Id : " + vitalRequestListVO.getPatientId()
				+ " ,PeriodType : " + vitalRequestListVO.getPeriodType() + " ,RequestedDate : "
				+ vitalRequestListVO.getRequestedDate());

		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.getDailyPedometerDetails(vitalRequestListVO);
			response.setThresholdConfigVO(patientService.getThresholdConfig(vitalRequestListVO.getPatientId()));
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
			/*
			 * } else { response.setSuccess(false); String messages[] = new String[2];
			 * messages[0] = "Operation failed"; messages[1] = "User Not Logged in";
			 * response.setMessages(messages); }
			 */
		}
		return response;

	}

	@RequestMapping(value = "/vitalPedometerWeeklyGraph", method = RequestMethod.POST)
	public @ResponseBody VitalsCountGraphListVO vitalPedometerWeeklyGraph(HttpServletRequest request,
			@RequestBody VitalRequestListVO vitalRequestListVO) throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		VitalsCountGraphListVO response = new VitalsCountGraphListVO();
		logger.info("inside the PedometerWeeklyGraph");
		logger.info("Request for the Pedometer Weekly/Monthly Graph Patient Id : " + vitalRequestListVO.getPatientId()
				+ " ,PeriodType : " + vitalRequestListVO.getPeriodType() + " ,RequestedDate : "
				+ vitalRequestListVO.getRequestedDate());

		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			if (vitalRequestListVO.getPeriodType() != null) {
				response = patientService.getPedometerDetails(vitalRequestListVO);
				response.setThresholdConfigVO(patientService.getThresholdConfig(vitalRequestListVO.getPatientId()));
			} else {
				response.setSuccess(false);
				String messages[] = new String[2];
				messages[0] = "Operation failed";
				messages[1] = "Period Type for Vital Request is not provided";
				response.setMessages(messages);
			}

			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);

		} else {
			response.setSuccess(false);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/getAllDeviceTypes", method = RequestMethod.GET)
	public @ResponseBody BaseListVO<DeviceTypeVO> getAllDeviceTypes(HttpServletRequest request)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		BaseListVO<DeviceTypeVO> response = new BaseListVO<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			List<DeviceTypeVO> deviceTypeVOList = new ArrayList<>();
			deviceTypeVOList = patientService.getDeviceTypeList();

			response.seteList(deviceTypeVOList);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		} else {
			response.setSuccess(true);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/saveDeviceType", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveDeviceType(HttpServletRequest request, @RequestBody DeviceTypeVO deviceTypeVO)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		StatusVO response = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.saveDeviceType(deviceTypeVO);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		} else {
			response.setSuccess(true);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/deleteDeviceType", method = RequestMethod.POST)
	public @ResponseBody StatusVO deleteDeviceType(HttpServletRequest request, @RequestBody DeviceTypeVO deviceTypeVO)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		StatusVO response = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.deleteDeviceType(deviceTypeVO);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		} else {
			response.setSuccess(true);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/getPatientEncountersByReason/{patientId}/{reason}/{startDate}/{endDate}", method = RequestMethod.GET)
	public @ResponseBody EncounterNewListVO getPatientEncountersByReason(HttpServletRequest request,
			@PathVariable Long patientId, @PathVariable String reason, @PathVariable String startDate,
			@PathVariable String endDate) throws Exception {
		UserVO user = null;
		List<EncounterNewVO> encounterNewList = null;
		Long resultCount = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			encounterNewList = patientService.getEncounterByReasonAndPatient(patientId, reason, startDate, endDate);
			resultCount = Long.valueOf(encounterNewList.size());
		}
		EncounterNewListVO encounters = new EncounterNewListVO();
		encounters.setResultCount(resultCount);
		encounters.setEncounterNewList(encounterNewList);
		encounters.setSuccess(true);

		PatientDocumentationListVO rpmcmcmData = patientService.getPatientRpmCcmPcmMins(patientId);

		encounters.setPcmMins(rpmcmcmData.getPcmCount());
		encounters.setRpmMins(rpmcmcmData.getRpmCount());
		encounters.setCcmMins(rpmcmcmData.getCcmCount());

		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		encounters.setMessages(messages);
		return encounters;
	}

	@RequestMapping(value = "/getLatestVital", method = RequestMethod.POST)
	public @ResponseBody Map<String, Object> getLatestVitalDataByPatientId(
			@RequestBody Map<String, String> patientData) {
		Map<String, Object> response = new HashMap<>();
		try {
			if (patientData.get("patientId") != null) {
				response.put("status", true);
				response.put("data",
						patientService.getLatestVitalDataByPatientId(Long.valueOf(patientData.get("patientId"))));
				return response;
			} else {
				response.put("status", false);
			}
		} catch (Exception e) {
			response.put("status", true);
			response.put("data", "No data available.");
		}
		return response;
	}

	@RequestMapping(value = "/getVitalByDate", method = RequestMethod.POST)
	public @ResponseBody Map<String, Object> getVitalDataByPatientIdAndDate(
			@RequestBody Map<String, String> patientData) {
		Map<String, Object> response = new HashMap<>();
		try {
			if (patientData.get("patientId") != null) {
				response.put("status", true);
				response.put("data", patientService.getVitalDataByPatientIdAndDate(patientData));
				return response;
			} else {
				response.put("status", false);
			}
		} catch (Exception e) {
			response.put("status", true);
			response.put("data", "No data available.");
		}
		return response;
	}

	@RequestMapping(value = "/getTextMessageByPatientId", method = RequestMethod.POST)
	public @ResponseBody Map<String, Object> getTextMessageByPatientId(@RequestBody Map<String, String> patientData) {
		Map<String, Object> response = new HashMap<>();
		try {
			if (patientData.get("patientId") != null) {
				List<Map<String, String>> dat = patientService
						.getTextMessageByPatientId(Long.valueOf(patientData.get("patientId")));
				response.put("status", true);
				response.put("data", dat);
				response.put("totalMessages", dat != null ? dat.size() : 0);
				return response;
			} else {
				response.put("status", false);
			}
		} catch (Exception e) {
			response.put("status", true);
			response.put("data", "No data available.");
		}
		return response;
	}

	@RequestMapping(value = "/getimei", method = RequestMethod.POST)
	public @ResponseBody PatientDetails registerWatch(@RequestBody RegisterWatch watch) {
		if (watch.getOtp() == null) {
			WatchRxFactory.getPatientService().updateWatchAndroidApkInformation(watch);
			WatchRxFactory.getWatchService().processHeartBeatResponse(watch.getImeiNo());
			PatientDetails pd = WatchRxFactory.getPatientService().getPatientDetailsByImei(watch);
			return pd;
		} else {
			PatientDetails pd = WatchRxFactory.getPatientService().getPatientDetailsByEmail(watch);
			return pd;
		}
	}

	@RequestMapping(value = "/resendOTP", method = RequestMethod.POST)
	public @ResponseBody String resendOTP(HttpServletRequest request, @RequestBody PatientWatchCaregiverInfoVO patient)
			throws Exception {
		logger.info("resendOTP:::: ");
		WatchRxFactory.getPatientService().saveResentOTPByPatnId(Long.parseLong(patient.getPatientId()));
		return "success";
	}

	@RequestMapping(value = "/getSleepMonitoringReadingList/{patientId}/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody BaseListWithCountVO<SleepMonitoringVO, Long> getSleepMonitoringReadingList(
			HttpServletRequest request, @PathVariable Long patientId, @PathVariable Integer index,
			@PathVariable Integer pageSize) throws Exception {
		UserVO user = null;
		BaseListWithCountVO<SleepMonitoringVO, Long> response = new BaseListWithCountVO<>();

		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			List<SleepMonitoringVO> sleepMonitoringVOList = patientService.getSleepMonitoringReadings(patientId, index,
					pageSize);
			response.seteList(sleepMonitoringVOList);
			response.setCount(patientService.getSleepMonitoringReadingCount(patientId));

		}
		response.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		response.setMessages(messages);
		return response;
	}

	@RequestMapping(value = "/savePedometerConfiguration", method = RequestMethod.POST)
	public @ResponseBody StatusVO savePedometerConfiguration(HttpServletRequest request,
			@RequestBody PedometerConfigurationVO pedometerConfigurationVO) throws Exception {
		UserVO user = null;
		StatusVO statusVO = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			boolean success = patientService.savePedometerConfiguration(pedometerConfigurationVO);
			if (success) {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				statusVO.setMessages(messages);
			} else {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation failed";
				statusVO.setMessages(messages);
			}
		}

		return statusVO;
	}

	@RequestMapping(value = "/enableDisableSleepMonitoring", method = RequestMethod.POST)
	public @ResponseBody StatusVO enableDisableSleepMonitoring(HttpServletRequest request,
			@RequestBody SleepMonitoringConfigurationVO sleepMonitoringConfigurationVO) throws Exception {
		UserVO user = null;
		StatusVO statusVO = new StatusVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			boolean success = patientService.saveSleepMonitoringConfiguration(sleepMonitoringConfigurationVO);
			if (success) {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation Successful";
				statusVO.setMessages(messages);
			} else {
				statusVO.setSuccess(true);
				String messages[] = new String[1];
				messages[0] = "Operation failed";
				statusVO.setMessages(messages);
			}
		}

		return statusVO;
	}

	@RequestMapping(value = "/getSleepMonitoringConfiguration/{patientId}", method = RequestMethod.GET)
	public @ResponseBody SleepMonitoringConfigurationVO getSleepMonitoringConfiguration(HttpServletRequest request,
			@PathVariable Long patientId) throws Exception {
		UserVO user = null;
		SleepMonitoringConfigurationVO result = new SleepMonitoringConfigurationVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			PatientVO patientVO = new PatientVO();
			patientVO.setPatientId(patientId);
			result = patientService.getSleepMonitoringConfigurationConfiguration(patientId);

		}
		result.setSuccess(true);
		String messages[] = new String[1];
		messages[0] = "Operation Successful";
		result.setMessages(messages);
		return result;
	}

	@RequestMapping(value = "/getSleepMonitoringReports", method = RequestMethod.POST)
	public @ResponseBody BaseListVO<SleepMonitoringVO> getSleepMonitoringReports(HttpServletRequest request,
			@RequestBody AlertsCountRequestVO requestBody) throws Exception {
		UserVO user = null;
		BaseListVO<SleepMonitoringVO> response = new BaseListVO<>();
		List<SleepMonitoringVO> pedometerReadingListVO = new ArrayList<>();

		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);

		if (user != null) {

			pedometerReadingListVO = patientService.getSleepMonitoringReports(requestBody);
			response.seteList(pedometerReadingListVO);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
		}
		return response;
	}

	@RequestMapping(value = "/vitalSleepMonitorDailyGraph", method = RequestMethod.POST)
	public @ResponseBody VitalsCountGraphListVO vitalSleepMonitorDailyGraph(HttpServletRequest request,
			@RequestBody VitalRequestListVO vitalRequestListVO) throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		VitalsCountGraphListVO response = new VitalsCountGraphListVO();
		logger.info("inside the SMDailyGraph");
		logger.info("Request for the SM Daily Graph Patient Id : " + vitalRequestListVO.getPatientId()
				+ " ,PeriodType : " + vitalRequestListVO.getPeriodType() + " ,RequestedDate : "
				+ vitalRequestListVO.getRequestedDate());

		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.getDailySleepMonitorDetails(vitalRequestListVO);
			response.setThresholdConfigVO(patientService.getThresholdConfig(vitalRequestListVO.getPatientId()));
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);
			/*
			 * } else { response.setSuccess(false); String messages[] = new String[2];
			 * messages[0] = "Operation failed"; messages[1] = "User Not Logged in";
			 * response.setMessages(messages); }
			 */
		}
		return response;

	}

	@RequestMapping(value = "/vitalSleepMonitorWeeklyGraph", method = RequestMethod.POST)
	public @ResponseBody VitalsCountGraphListVO vitalSleepMonitorWeeklyGraph(HttpServletRequest request,
			@RequestBody VitalRequestListVO vitalRequestListVO) throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		VitalsCountGraphListVO response = new VitalsCountGraphListVO();
		logger.info("inside the SMWeeklyGraph");
		logger.info("Request for the SM Weekly/Monthly Graph Patient Id : " + vitalRequestListVO.getPatientId()
				+ " ,PeriodType : " + vitalRequestListVO.getPeriodType() + " ,RequestedDate : "
				+ vitalRequestListVO.getRequestedDate());

		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {

			if (vitalRequestListVO.getPeriodType() != null) {
				response = patientService.getSleepMonitorDetails(vitalRequestListVO);
				response.setThresholdConfigVO(patientService.getThresholdConfig(vitalRequestListVO.getPatientId()));
			} else {
				response.setSuccess(false);
				String messages[] = new String[2];
				messages[0] = "Operation failed";
				messages[1] = "Period Type for Vital Request is not provided";
				response.setMessages(messages);
			}

			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);

		} else {
			response.setSuccess(false);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "User Not Logged in";
			response.setMessages(messages);
		}

		return response;
	}

	@RequestMapping(value = "/scheduleNotificationForIOS", method = RequestMethod.GET)
	public @ResponseBody void scheduleNotificationIOS(HttpServletRequest request) throws Exception {
		patientService.sendIOSSilentNotificationTask();
	}

	@RequestMapping(value = "/triggerTask", method = RequestMethod.GET)
	public @ResponseBody void triggerTask(HttpServletRequest request) throws Exception {
		patientService.generateTask();
	}

	@RequestMapping(value = "/physicianBilling", method = RequestMethod.GET)
	public @ResponseBody List<BillingDetails> physicianBilling(HttpServletRequest request) throws Exception {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		logger.info("UserID::::::----------->" + user.getUserId() + " OrgID:" + user.getOrgId());
		return patientService.physicianPatientBilling(user);
	}

	@RequestMapping(value = "/patientDiary", method = RequestMethod.POST)
	public @ResponseBody PatientDiaryResponseVOList patientDiary(
			@RequestBody PatientDiaryRequestVO patientDiaryRequestVO, HttpServletRequest request)
			throws JsonMappingException, JsonProcessingException {
		logger.info("Patient Service invoked the method PatientDiaryResponseVOList");
		PatientDiaryResponseVOList diaryList = new PatientDiaryResponseVOList();
		List<PatientDiaryResponseVO> vos = new ArrayList<>();

		UserVO user = null;

		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			List<WatchRxPatientDiares> diaries = WatchRxFactory.getPatientService().fullPatientDiaries(
					patientDiaryRequestVO.getIndex(), patientDiaryRequestVO.getPageSize(),
					patientDiaryRequestVO.getPatientId());
			if (diaries != null) {
				SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				for (WatchRxPatientDiares watchRxPatientDiares : diaries) {
					PatientDiaryResponseVO vo = new PatientDiaryResponseVO();
					vo.setCreatedDate(format1.format(watchRxPatientDiares.getCreatedDate()));
					vo.setDiaryDescription(watchRxPatientDiares.getDiaryDescription());
					vo.setMealTime(watchRxPatientDiares.getMealTime());
					vo.setMedication(watchRxPatientDiares.getMedication());
					vo.setMedicationTime(watchRxPatientDiares.getMedicationTime());
					vos.add(vo);
				}
				diaryList.setStatus(true);
				diaryList.setPatientDiaryResponseVOs(vos);
				diaryList.setResponseCode("200");
				diaryList.setResponseMessage("Success");
				return diaryList;
			} else {
				diaryList.setStatus(true);
				diaryList.setPatientDiaryResponseVOs(vos);
				diaryList.setResponseCode("200");
				diaryList.setResponseMessage("Success");
				return diaryList;
			}

		} else {
			diaryList.setStatus(false);
			diaryList.setResponseCode("500");
			diaryList.setResponseMessage("User not loggedin");
			return diaryList;
		}
	}

	@PostMapping("/patientBulkInsert")
	public Map<String, Object> uploadCSVFile(@RequestParam("file") MultipartFile file, HttpServletRequest request)
			throws IOException, ParseException {
		UserVO user = null;
		Map<String, Object> response = new HashMap<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		System.out.println(file.getOriginalFilename() + "**********");
		if (file.getOriginalFilename().toLowerCase().contains("bmc")) {
			try {
				System.out.println(file.getOriginalFilename() + "**********");
				bmcPatientScreening(file, request);
				response.put("status", true);
				response.put("message", "Operation Successful");
				return response;
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
			System.out.println(file.getOriginalFilename() + "**********");
			if (user != null) {
				List<PatientVO> patientList = new ArrayList<>();
				XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
				XSSFSheet worksheet = workbook.getSheetAt(0);
				DataFormatter formatter = new DataFormatter();
				List<Integer> missingRow = new ArrayList<>();
				for (int i = 1; i < worksheet.getPhysicalNumberOfRows(); i++) {
					PatientVO vo = new PatientVO();
					vo.setOrgId(user.getOrgId());
					XSSFRow row = worksheet.getRow(i);

					if (!formatter.formatCellValue(row.getCell(0)).isEmpty()) {
						if (formatter.formatCellValue(row.getCell(0)).isEmpty()
								|| formatter.formatCellValue(row.getCell(1)).isEmpty()
								|| formatter.formatCellValue(row.getCell(3)).isEmpty()) {
							missingRow.add(i);
						} else {
							vo.setFirstName(formatter.formatCellValue(row.getCell(0)).trim());
							vo.setLastName(formatter.formatCellValue(row.getCell(1)).trim());
							vo.setStatus("N");
							vo.setDob(
									row.getCell(3) != null ? formatter.formatCellValue(row.getCell(3)) != null
											? new SimpleDateFormat("MM-dd-yyyy").parse(
													formatter.formatCellValue(row.getCell(3)))
											: null : null);
							AddressVO add = new AddressVO();
							add.setAddress1(formatter.formatCellValue(row.getCell(4)).trim());
							add.setCity(formatter.formatCellValue(row.getCell(5)).trim());
							add.setState(formatter.formatCellValue(row.getCell(6)).trim());
							add.setZip(formatter.formatCellValue(row.getCell(7)).trim());
							add.setCountry(formatter.formatCellValue(row.getCell(8)).trim());
							if ((add.getCountry().equalsIgnoreCase("USA")||add.getCountry().equalsIgnoreCase("United States"))
							&& !formatter.formatCellValue(row.getCell(2)).trim().isEmpty()){
								vo.setPhoneNumber(row.getCell(2) != null
										? "+1" + formatter.formatCellValue(row.getCell(2)).replaceAll("-", "")
										: null);
							} else if (add.getCountry().equalsIgnoreCase("INDIA")) {
								vo.setPhoneNumber(row.getCell(2) != null
										? "+91" + formatter.formatCellValue(row.getCell(2)).replaceAll("-", "")
										: null);
							} else {
								vo.setPhoneNumber(row.getCell(2) != null && !formatter.formatCellValue(row.getCell(2)).trim().isEmpty()
										? formatter.formatCellValue(row.getCell(2)).replaceAll("-", "")
										: null);

							}
							vo.setAddress(add);
							try {
								List<CreatePatientRequestChronicConditionsVO> vos = new ArrayList<>();
								if (row.getCell(9) != null && !formatter.formatCellValue(row.getCell(9)).trim().isEmpty()) {
									List<String> listName = Stream
											.of(formatter.formatCellValue(row.getCell(9)).split(","))
											.collect(Collectors.toList());

									for (int j = 0; j < listName.size(); j++) {
										CreatePatientRequestChronicConditionsVO voCode = new CreatePatientRequestChronicConditionsVO();
										voCode.setChronicConditionName(listName.get(j));
										vos.add(voCode);
									}
								}
								if (row.getCell(10) != null && !formatter.formatCellValue(row.getCell(10)).trim().isEmpty()) {

									List<String> listcode = Stream
											.of(formatter.formatCellValue(row.getCell(10)).split(","))
											.collect(Collectors.toList());
									for (int j = 0; j < listcode.size(); j++) {
										try {
											if (vos.get(j) != null) {
												vos.get(j).setIcdCode(listcode.get(j));
											}
										} catch (Exception e) {
											e.printStackTrace();
										}
									}
								}
								vo.setChronicConditions(vos);

								if (row.getCell(11) != null) {
									try {
										vo.setPhysicianId(physicianDAO
												.findByProperty("watchrxUser.email",
														formatter.formatCellValue(row.getCell(11)))
												.get(0).getPhysicianId());
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								if (row.getCell(12) != null && !formatter.formatCellValue(row.getCell(12)).trim().isEmpty()) {
									try {
										vo.setCasemanagerId(clinicianDAO
												.findByProperty("watchrxUser.email",
														formatter.formatCellValue(row.getCell(12)))
												.get(0).getClinicianId());
									} catch (Exception e) {
										vo.setCasemanagerId(clinicianDAO.findByProperty("watchrxUser.userId", user.getUserId())
												.get(0).getClinicianId());
										e.printStackTrace();
									}
								} else {
									vo.setCasemanagerId(clinicianDAO.findByProperty("watchrxUser.userId", user.getUserId())
											.get(0).getClinicianId());
								}
							} catch (Exception e) {
								e.printStackTrace();
							}
							vo.setGender(
									row.getCell(13) != null ? formatter.formatCellValue(row.getCell(13)).trim() : null);
							patientList.add(vo);
						}
					} else {
						missingRow.add(i);
					}
				}
				patientService.savePatientsByBulk(patientList, user.getOrgId());
				if (missingRow.size() > 0) {
					response.put("status", true);
					response.put("message", "Row nos: " + missingRow.toString() + " has missing values.");
				} else {
					response.put("status", true);
					response.put("message", "Operation Successful");
				}
			}
		}
		return response;

	}

	@RequestMapping(value = "/patientCarePlanDetails/{patientId}/{carePlanId}", method = RequestMethod.GET)
	public @ResponseBody List<PatientCarePlanVO> patientCarePlanDetails(HttpServletRequest request,
			@PathVariable Long patientId, @PathVariable Long carePlanId) throws Exception {
		return patientCarePlanService.getCarePlanByPatientIdAmdCarePlanId(patientId, carePlanId);
	}

	@RequestMapping(value = "/savePatientCarePlanDetails", method = RequestMethod.POST)
	public @ResponseBody StatusVO savePatientCarePlanDetails(HttpServletRequest request,
			@RequestBody PatientCarePlanVO vo) throws Exception {
		StatusVO vos = new StatusVO();
		String[] messages = new String[1];
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			try {
				logger.info("Inside savePatientCarePlanDetails :" + vo.toString());
				patientCarePlanService.savePatientCarePlans(vo, user.getUserId());
				vos.setSuccess(true);
				messages[0] = "success";
				vos.setMessages(messages);
			} catch (Exception e) {
				logger.error("Error:" + e.getMessage());
				vos.setSuccess(true);
				messages[0] = "failed";
				vos.setMessages(messages);
			}
		} else {
			logger.error("Error: User Session not found.");
			vos.setSuccess(true);
			messages[0] = "failed";
			vos.setMessages(messages);
		}
		return vos;
	}

	@RequestMapping(value = "/deletepatientCarePlanDetails/{patientId}", method = RequestMethod.GET)
	public StatusVO deletepatientCarePlanDetails(HttpServletRequest request, @PathVariable Long carePlanId)
			throws Exception {
		StatusVO vos = new StatusVO();
		String[] messages = new String[1];
		try {
			patientCarePlanService.deletePcatientCarePlanById(carePlanId);
			vos.setSuccess(true);
			messages[0] = "success";
			vos.setMessages(messages);
		} catch (Exception e) {
			vos.setSuccess(true);
			messages[0] = "failed";
			vos.setMessages(messages);
		}
		return vos;
	}

	@RequestMapping(value = "/getLatestCarePlanDetails/{patientId}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> getLatesCarePlanDetails(HttpServletRequest request,
			@PathVariable Long patientId) throws Exception {
		return patientCarePlanService.getLatestCarePlanDetails(patientId);
	}

	@RequestMapping(value = "/getCarePlanDetailsForXLS/{patientId}/{startDate}/{endDate}", method = RequestMethod.GET)
	public @ResponseBody List<PatientCarePlanVO> getCarePlanDetailsForXLS(HttpServletRequest request,
			@PathVariable Long patientId, @PathVariable String startDate, @PathVariable String endDate)
			throws Exception {
		return patientCarePlanService.getCarePlanDetailsForXLS(patientId, startDate, endDate);
	}

	@RequestMapping(value = "/deleteAlert/{alertId}", method = RequestMethod.GET)
	public @ResponseBody StatusVO deleteAlert(HttpServletRequest request, @PathVariable Long alertId) throws Exception {
		StatusVO statusVO = null;
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			statusVO = patientService.deleteAlert(alertId);
		}
		return statusVO;
	}

	@RequestMapping(value = "/caseMgrpatientIncompleteTask", method = RequestMethod.GET)
	public @ResponseBody Void notifiyCaseManagerForPatientIncomplete(HttpServletRequest request) throws Exception {
		patientService.notifiyCaseManagerForPatientIncomplete();
		return null;
	}

	@RequestMapping(value = "/dialogsByUser", method = RequestMethod.POST)
	public @ResponseBody List<DiyvaDialog> dialogsByUser(HttpServletRequest request) throws Exception {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		return dService.getDialogsByUserId(user.getUserName());
	}

	@RequestMapping(value = "/assignDialogToPatient", method = RequestMethod.POST)
	public @ResponseBody String assignDialogToPatient(HttpServletRequest request, @RequestBody DiyvaDialog dialog)
			throws Exception {
		return dService.assignDialogToPatient(dialog);
	}

	@RequestMapping(value = "/dialogCommByPatient", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, String>> dialogCommByPatient(HttpServletRequest request,
			@RequestBody DiyvaDialog dialog) throws Exception {
		return dService.patientCommunication(dialog);
	}

	@RequestMapping(value = "/deleteDialogCommById/{dialogCommId}", method = RequestMethod.GET)
	public @ResponseBody String deleteDialogCommById(HttpServletRequest request, @PathVariable String dialogCommId)
			throws Exception {
		dService.deltePatientCommunication(dialogCommId);
		return "success";
	}

	@RequestMapping(value = "/getDialogByPatId/{patId}", method = RequestMethod.GET)
	public @ResponseBody List<DiyvaDialog> getDialogByPatId(HttpServletRequest request, @PathVariable Long patId)
			throws Exception {
		return dService.getDialogAssignedToPatient(patId);
		// return "success";
	}

	@RequestMapping(value = "/sendDialog/{patId}/{dlgId}", method = RequestMethod.GET)
	public @ResponseBody String sendDialog(HttpServletRequest request, @PathVariable Long patId,
			@PathVariable Long dlgId) throws Exception {
		return dService.sendDialogToPatient(dlgId, patId);
		// return "success";
	}

	@RequestMapping(value = "/medicationAdherence", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public @ResponseBody String getMissedMedication() throws Exception {
		try {
			dataTransformService.createCriticalAlertforMMByWeek();
		} catch (Exception e) {
		}
		return "SUCCESS";
	}

	@RequestMapping(value = "/alertsCntByDate", method = RequestMethod.GET)
	public Map<String, List<Object>> alertsCntByDate(HttpServletRequest request) throws Exception {
		Map<String, Long> patientData = new HashMap<>();
		Map<String, List<Object>> chartData = new HashMap<>();
		try {
			UserVO user = new ObjectMapper()
					.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
			if (user != null) {
				List<WatchrxPatientAlert> watchrxPatientAlertList = patientAlertDAO
						.activeAllAlertsCurrentMonthV1(user.getOrgId());
				patientData = watchrxPatientAlertList.stream()
						.collect(Collectors.groupingBy(WatchrxPatientAlert::getAlertSeverity, Collectors.counting()));
				List<Object> labels = new ArrayList<>(patientData.keySet());
				List<Object> series = patientData.values().stream().collect(Collectors.toList());
				chartData.put("labels", labels);
				chartData.put("series", series);
			}
			return chartData;
		} catch (Exception e) {
			return chartData;
		}
	}

	@RequestMapping(value = "/patientBillingChart", method = RequestMethod.GET)
	public @ResponseBody Map<String, List<Object>> getPatientAddressBillingStatus(HttpServletRequest request)
			throws Exception {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		Map<String, List<Object>> chartData = new HashMap<>();
		if (user != null) {
			List<Object[]> patientsList = new ArrayList<>();
			Long patientsCount = patientService.getAllPatientsCountByOrgId(user.getRoleType(), user.getOrgId(),
					user.getUserId());
			patientsList = patientService.getPatientBillingAddressInfo(user.getOrgId(), 0, patientsCount.intValue(),
					user.getUserId());
			Map<String, Object> data = new LinkedHashMap<>();
			data.put("IN PROGRESS", 0);
			data.put("VALIDATION PENDING", 0);

			if (user != null && patientsList.size() > 0) {
				for (Object[] patient : patientsList) {

					LocalDate currentDateLdt = LocalDate.now();
					Date endDate = Date.from(currentDateLdt.atStartOfDay(ZoneId.systemDefault()).toInstant());

					LocalDate startDateLdt = currentDateLdt.with(TemporalAdjusters.firstDayOfMonth());
					Date startDate = Date.from(startDateLdt.atStartOfDay(ZoneId.systemDefault()).toInstant());

					Map<String, Integer> dataCnt = patientService
							.validationInfoByStartAndEndDate(((BigInteger) patient[0]).longValue(), startDate, endDate);
					if (dataCnt != null && (dataCnt.get("rpmMins") >= 20 || dataCnt.get("ccmMins") >= 20)
							&& dataCnt.get("daysCounted") >= 16) {
						data.put("VALIDATION PENDING", (int) data.get("VALIDATION PENDING") + 1);
					} else {
						data.put("IN PROGRESS", (int) data.get("IN PROGRESS") + 1);
					}
				}
			}
			List<Object> keys = new ArrayList<>(data.keySet());
			List<Object> values = new ArrayList<>(data.values());
			chartData.put("labels", keys);
			chartData.put("series", values);
			return chartData;
		} else {
			return chartData;
		}
	}

	@RequestMapping(value = "/getAlertsCountReports", method = RequestMethod.POST)
	public @ResponseBody VitalWeeklyGraphListVO getAlertsCountReports(HttpServletRequest request,
			@RequestBody AlertsCountRequestVO requestBody) throws Exception {
		UserVO user = null;
		VitalWeeklyGraphListVO response = new VitalWeeklyGraphListVO();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		logger.info("Request for the Alerts Report Patient Id : " + requestBody.getPatientId() + " Format : "
				+ requestBody.getFormat() + " StartDate : " + requestBody.getStartDate() + " EndDate : "
				+ requestBody.getEndDate());
		if (user != null) {
			response = patientService.getAlertsCountGraph(requestBody);
			response.setSuccess(true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.setMessages(messages);

		}
		return response;
	}

	@RequestMapping(value = "/getPatientAlertsByPatientId", method = RequestMethod.POST)
	public @ResponseBody PatientAlertResponse getPatientAlertsByPatientId(HttpServletRequest request,
			@RequestBody AlertRequestVO alertsRequestBody) throws Exception {
		logger.info("Inside getPatientAlerts:::: ");
		PatientAlertResponse patientAlerts = new PatientAlertResponse();
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		logger.info("RoleType : " + user.getRoleType());
		List<PatientAlertInfoVO> alertInfoList = new ArrayList<>();
		try {
			if (user.getRoleType() == 6) {
				List<MedicationAlert> alertsData = WatchRxFactory.getAlertService()
						.getPatientAlertsByPatientId(alertsRequestBody);
				Long count = WatchRxFactory.getAlertService().getPatientAlertsCountByPatientId(alertsRequestBody);
				for (MedicationAlert medAlert : alertsData) {
					PatientAlertInfoVO alertInfo = new PatientAlertInfoVO();
					alertInfo.setDescription(medAlert.getAlertDescription());
					alertInfo.setDateTime(medAlert.getDateTime());
					alertInfo.setSeverity(medAlert.getAlertType());
					alertInfo.setPatientInfo(medAlert.getPatientName());
					alertInfo.setPatientId(Long.parseLong(medAlert.getPatientId()));
					alertInfo.setAcknowledgementStatus(medAlert.getAcknowledgementStatus());
					alertInfo.setAlertId(Long.valueOf(medAlert.getAlertId()));
					alertInfoList.add(alertInfo);
				}
				patientAlerts.setPatientAlert(alertInfoList);
				patientAlerts.setTotalCount(count);
				patientAlerts.setSuccess(true);
				patientAlerts.setResponseCode(Constants.ResponseCode.MEDICATIONALERTSFOUND);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.MEDICATIONALERTSFOUND;
				patientAlerts.setMessages(messages);
				return patientAlerts;
			} else {
				logger.info("Unauthorized access");
				if (user != null) {
					logger.info("Unauthorized access by user with username " + user.getUserName());
				}
				patientAlerts.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				patientAlerts.setMessages(messages);
				patientAlerts.setSuccess(false);
				return patientAlerts;
			}
		} catch (Exception e) {
			logger.error("Message is .... : " + e.getMessage(), e);
			patientAlerts.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			patientAlerts.setMessages(messages);
			patientAlerts.setSuccess(false);
			return patientAlerts;
		}
	}

	@RequestMapping(value = "/patientAddressBillingInfo/{index}/{pageSize}/{status}/{timeDuration}/{type}", method = RequestMethod.GET)
	public @ResponseBody PatientWatchCaregiverListVO getPatientAddressBillingStatusByTimeDuration(
			HttpServletRequest request, @PathVariable Integer index, @PathVariable Integer pageSize,
			@PathVariable String status, @PathVariable String timeDuration, @PathVariable String type)
			throws Exception {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<Object[]> patientsList = new ArrayList<>();
		Long patientsCount = 0l;
		if (user != null) {
			patientsCount = patientService.getAllPatientsCountByOrgId(user.getRoleType(), user.getOrgId(),
					user.getUserId());
			patientsList = patientService.getPatientBillingAddressInfo(user.getOrgId(), index, pageSize,
					user.getUserId());
		} else {
			logger.info("Unauthorized access");
			PatientWatchCaregiverListVO response = new PatientWatchCaregiverListVO();
			response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

		List<PatientWatchCaregiverInfoVO> data = new ArrayList<>();
		PatientWatchCaregiverListVO patientsInfo = new PatientWatchCaregiverListVO();
		String msg[] = new String[1];
		if (user != null && patientsList.size() > 0) {
			for (Object[] patient : patientsList) {
				Map<String, Object> dataCnt = patientService
						.validationInfoByTimeDuration(((BigInteger) patient[0]).longValue(), timeDuration);
				PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
				pwcInfo.setTotalMins(((Double) dataCnt.get("phMins")).intValue());
				pwcInfo.setNoOfDays((Integer) dataCnt.get("daysCounted"));
				pwcInfo.setTotalDaysLooking((Integer) dataCnt.get("totalDaysLooking"));
				int missedDays = (Integer) dataCnt.get("totalDaysLooking") - (Integer) dataCnt.get("daysCounted");
				pwcInfo.setMissedDays(missedDays);
				pwcInfo.setPatientId(patient[0].toString());
				pwcInfo.setPatientName(patient[1].toString());
				if (patient[4] != null && patient[4].toString() != null) {
					pwcInfo.setPatientPhoneNumber(patient[4].toString());
				} else {
					pwcInfo.setPatientPhoneNumber("");
				}
				data.add(pwcInfo);
			}
			patientsInfo.setPatientsInfo(data);
			patientsInfo.setCount(patientsCount);
			patientsInfo.setSuccess(true);
			msg[0] = new String("Operation Successful");
			patientsInfo.setMessages(msg);
		} else {
			logger.info("PatientList size: " + patientsList.size());
			if (user != null && patientsList.size() == 0) {
				logger.info("PatientList size by user with username " + user.getUserName());
			}
			WatchRxException excep = new WatchRxException(WatchRxExceptionCodes.EMPTY_PATIENTLIST);
			msg[0] = new String(excep.getErrDesc());
			patientsInfo.setResponseCode(Integer.parseInt(excep.getErrCode()));
			patientsInfo.setMessages(msg);
			patientsInfo.setSuccess(false);
		}
		return patientsInfo;
	}

	@RequestMapping(value = "/downloadRPMCCMReport", method = RequestMethod.POST)
	public void downloadCSV(HttpServletRequest request, HttpServletResponse response,
			@RequestBody RpmCcmReportVO alertReq) throws IOException, WatchRxServiceException {

		try {
//			DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//			Date date = new Date();
//
//			String timeDueration = "";
//			if (alertReq != null && alertReq.getTimeDuration() != null
//					&& alertReq.getTimeDuration().equalsIgnoreCase("DAILY")) {
//				timeDueration = "daily";
//			} else if (alertReq != null && alertReq.getTimeDuration() != null
//					&& alertReq.getTimeDuration().equalsIgnoreCase("MID_WEEK")) {
//				timeDueration = "week";
//			} else if (alertReq != null && alertReq.getTimeDuration() != null
//					&& alertReq.getTimeDuration().equalsIgnoreCase("MID_MONTH")) {
//				timeDueration = "month";
//			}
//
//			UserVO user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),UserVO.class);
//			if (user != null) {
//				String fileName = "RPM_CCM_REPORT_" + sdf.format(date) + "_" + timeDueration + ".csv";
//
//				response.setContentType("text/csv");
//				response.setHeader("Content-disposition", "attachment;filename=" + fileName);
//				response.setCharacterEncoding(StandardCharsets.UTF_8.name());
//
//				ArrayList<String> rows = new ArrayList<String>();
//				rows.add("Patient Name, Phone Number, Measured Days, Missed Days, Phone Minutes");
//				rows.add("\n");
//
//				List<Object[]> patientsList = new ArrayList<>();
//				Long patientsCount = 0l;
//
//				patientsCount = patientService.getAllPatientsCountByOrgId(user.getRoleType(), user.getOrgId(),
//						user.getUserId());
//				patientsList = patientService.getPatientBillingAddressInfo(user.getOrgId(), 0, patientsCount.intValue(),
//						user.getUserId());
//
//				List<PatientWatchCaregiverInfoVO> data = new ArrayList<PatientWatchCaregiverInfoVO>();
//				logger.debug("RPM CCM Report Patients List:" + patientsList.size());
//				if (patientsList.size() > 0) {
//					for (Object[] patient : patientsList) {
//						Map<String, Integer> dataCnt = patientService.validationInfoByTimeDuration(
//								((BigInteger) patient[0]).longValue(), alertReq.getTimeDuration());
//						PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
//						pwcInfo.setTotalMins(dataCnt.get("phMins"));
//						pwcInfo.setNoOfDays(dataCnt.get("daysCounted"));
//						pwcInfo.setTotalDaysLooking(dataCnt.get("totalDaysLooking"));
//						int missedDays = dataCnt.get("totalDaysLooking") - dataCnt.get("daysCounted");
//						pwcInfo.setMissedDays(missedDays);
//						pwcInfo.setPatientId(patient[0].toString());
//						pwcInfo.setPatientName(patient[1].toString());
//						if (patient[4] != null && patient[4].toString() != null) {
//							pwcInfo.setPatientPhoneNumber(patient[4].toString());
//						}
//						data.add(pwcInfo);
//					}
//				}
//				logger.debug("RPM CCM Report Data:" + data.size());
//				for (PatientWatchCaregiverInfoVO infoVO : data) {
//					String patientName = infoVO.getPatientName();
//					String patinetPhone = infoVO.getPatientPhoneNumber() == null ? ""
//							: infoVO.getPatientPhoneNumber() + "\t";
//					String measuredDays = "" + infoVO.getNoOfDays();
//					String missedDays = "" + infoVO.getMissedDays();
//					String commMinutes = "" + infoVO.getTotalMins();
//
//					String row = " " + patientName + "," + " " + patinetPhone + "," + measuredDays + "," + missedDays
//							+ "," + commMinutes;
//					rows.add(row);
//					rows.add("\n");
//				}
//
//				Iterator<String> iter = rows.iterator();
//				while (iter.hasNext()) {
//					String outputString = (String) iter.next();
//					PrintWriter out = response.getWriter();
//					out.print(outputString);
//				}
//			}
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("While downloading CSV: Erro-->" + e.getMessage());
		}
	}

	@RequestMapping(value = "/rpmCcmReportChart", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> rpmCcmReportChart(HttpServletRequest request) throws Exception {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		Map<String, Object> resp = new HashMap<>();
		resp.put("totalPatients", 0);
		resp.put("todayMissed", 0);
		resp.put("weekMissed", 0);
		resp.put("monthMissed", 0);
		resp.put("morethan16Days", 0);
		if (user != null) {
			logger.info("API CALL: OrgID:" + user.getOrgId() + "-" + user.toString());
			List<Object[]> patientsList = new ArrayList<>();
			Long patientsCount = patientService.getAllPatientsCountByOrgId(user.getRoleType(), user.getOrgId(),
					user.getUserId());
			patientsList = patientService.getPatientBillingAddressInfo(user.getOrgId(), 0, patientsCount.intValue(),
					user.getUserId());

			int todayMissed = 0;
			int weekMissed = 0;
			int monthMissed = 0;
			int morethan16Days = 0;

			if (patientsList.size() > 0) {
				for (Object[] patient : patientsList) {
					Map<String, Integer> todayMissedData = patientService
							.getVitalsMeasuredDayByPatientId(((BigInteger) patient[0]).longValue(), "DAILY");
					if (todayMissedData.get("daysCounted") == 0) {
						todayMissed += 1;
					}

					Map<String, Integer> weekMissedData = patientService
							.getVitalsMeasuredDayByPatientId(((BigInteger) patient[0]).longValue(), "MID_WEEK");
					if (weekMissedData.get("daysCounted") == 0) {
						weekMissed += 1;
					}

					Map<String, Integer> monthMissedData = patientService
							.getVitalsMeasuredDayByPatientId(((BigInteger) patient[0]).longValue(), "MID_MONTH");
					if (monthMissedData.get("daysCounted") == 0) {
						monthMissed += 1;
					}

					if (monthMissedData.get("daysCounted") >= 16) {
						morethan16Days += 1;
					}
				}
			}
			resp.put("totalPatients", patientsList.size());
			resp.put("todayMissed", todayMissed);
			resp.put("weekMissed", weekMissed);
			resp.put("monthMissed", monthMissed);
			resp.put("morethan16Days", morethan16Days);
		}
		return resp;
	}

	private Set<Long> getUsersByOrg(long loginUserId) {
		Set<Long> userId = new HashSet<>();
		List<WatchrxGroupUserAssignment> grps = grpUsrDao.findByProperty("watchrxUser.userId", loginUserId);
		grps.forEach(u -> {
			// grpsNam.add(u.getWatchrxGroup().getGroupId());
			List<WatchrxGroupUserAssignment> users = grpUsrDao.findByProperty("watchrxGroup.groupId",
					u.getWatchrxGroup().getGroupId());
			users.forEach(u1 -> {
				// if(u1.get)
				userId.add(u1.getWatchrxUser().getUserId());
				System.out.println(u1.getWatchrxUser().getEmail());
			});
		});
		return userId;
	}

	@RequestMapping(value = "/autoValidate/{date}", method = RequestMethod.GET)
	public @ResponseBody StatusVO autoValidate(HttpServletRequest request, @PathVariable String date) throws Exception {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		StatusVO response = new StatusVO();
		if (user != null) {
			List<Object[]> patientsList = new ArrayList<>();
			Long patientsCount = patientService.getAllPatientsCountByOrgId(user.getRoleType(), user.getOrgId(),
					user.getUserId());
			patientsList = patientService.getPatientBillingAddressInfo(user.getOrgId(), 0, patientsCount.intValue(),
					user.getUserId());

			if (patientsList.size() > 0) {
				if (date != null) {

					Date startDate = null;
					Date endDate = null;

					Calendar calendar = Calendar.getInstance();
					Calendar cal = Calendar.getInstance();
					String[] dateParts = date.split("-");
					calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
					calendar.set(Calendar.MONTH, Integer.valueOf(dateParts[1]) - 1);
					calendar.set(Calendar.YEAR, Integer.valueOf(dateParts[0]));
					calendar.set(Calendar.HOUR_OF_DAY, 00);
					calendar.set(Calendar.MINUTE, 00);
					calendar.set(Calendar.SECOND, 00);
					System.out.println(calendar.get(Calendar.DAY_OF_MONTH));

					cal.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
					cal.set(Calendar.MONTH, Integer.valueOf(dateParts[1]) - 1);
					cal.set(Calendar.YEAR, Integer.valueOf(dateParts[0]));
					cal.set(Calendar.HOUR_OF_DAY, 23);
					cal.set(Calendar.MINUTE, 59);
					cal.set(Calendar.SECOND, 59);

					startDate = calendar.getTime();
					endDate = cal.getTime();

					System.out.println("Start Date at auto validation: " + startDate + " endDate: " + endDate);

					for (Object[] patient : patientsList) {
						Map<String, Integer> dataCnt = patientService.validationInfoByStartAndEndDate(
								((BigInteger) patient[0]).longValue(), startDate, endDate);
						patientService.autoValidationForPatient(((BigInteger) patient[0]).longValue(),
								dataCnt.get("rpmMins"), dataCnt.get("ccmMins"), dataCnt.get("daysCounted"),
								user.getUserId(), date);
					}
				}
			}
			response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
			String messages[] = new String[1];
			messages[0] = "Auto validated successfully.";
			response.setMessages(messages);
			response.setSuccess(false);
		} else {
			logger.info("Unauthorized access");
			response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
			response.setMessages(messages);
			response.setSuccess(false);
		}
		return response;
	}

	public List<Date> getDatesBetweenUsingJava7(String date) {
		List<Date> datesInRange = new ArrayList<>();

		Date startDate = null;
		Date endDate = new Date();

		Calendar calendar = Calendar.getInstance();
		String[] dateParts = date.split("-");
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
		calendar.set(Calendar.MONTH, Integer.valueOf(dateParts[1]) - 1);
		calendar.set(Calendar.YEAR, Integer.valueOf(dateParts[0]));
		calendar.set(Calendar.HOUR_OF_DAY, 00);
		calendar.set(Calendar.MINUTE, 00);
		calendar.set(Calendar.SECOND, 00);

		startDate = calendar.getTime();

		Calendar calendar1 = getCalendarWithoutTime(startDate);
		Calendar endCalendar1 = getCalendarWithoutTime(endDate);

		while (calendar1.before(endCalendar1)) {
			Date result = calendar1.getTime();
			datesInRange.add(result);
			calendar1.add(Calendar.DATE, 1);
		}
		return datesInRange;
	}

	private Calendar getCalendarWithoutTime(Date date) {
		Calendar calendar = new GregorianCalendar();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR, 10);
		calendar.set(Calendar.HOUR_OF_DAY, 10);
		calendar.set(Calendar.MINUTE, 15);
		calendar.set(Calendar.SECOND, 35);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar;
	}

	@RequestMapping(value = "/allPatientBillingInfo/{orgId}/{index}/{pageSize}/{startDate}/{endDate}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> getAllPatientsBillingData(HttpServletRequest request,
			@PathVariable Long orgId, @PathVariable Integer index, @PathVariable Integer pageSize,
			@PathVariable String startDate, @PathVariable String endDate) throws Exception {

		Map<String, Object> result = new HashMap<>();
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<Object[]> patientsList = new ArrayList<>();
		List<Object[]> allPatientsList = new ArrayList<>();
		Long patientsCount = 0l;
		if (user != null) {
			patientsCount = patientService.getAllPatientsCountByOrgIdV3(user.getRoleType(), orgId, user.getUserId());
			logger.info("Patients Count for orgId:" + orgId);
			patientsList = patientService.getPatientBillingAddressInfoV3(orgId, index, pageSize);
			allPatientsList = patientService.getPatientBillingAddressInfoV3(orgId, 0, patientsCount.intValue());

			DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Date sDate = sdf.parse(startDate);

			Calendar startDateCal = Calendar.getInstance();
			startDateCal.setTime(sDate);
			startDateCal.set(Calendar.HOUR_OF_DAY, 00);
			startDateCal.set(Calendar.MINUTE, 00);
			startDateCal.set(Calendar.SECOND, 00);

			Date eDate = sdf.parse(endDate);
			Calendar endDateCal = Calendar.getInstance();
			endDateCal.setTime(eDate);
			endDateCal.set(Calendar.HOUR_OF_DAY, 23);
			endDateCal.set(Calendar.MINUTE, 59);
			endDateCal.set(Calendar.SECOND, 59);

			if (patientsList != null && patientsList.size() > 0) {
				List<Map<String, Object>> response = new ArrayList<>();
				for (Object[] patient : patientsList) {
					Map<String, Integer> dataCnt = patientService.validationInfoByStartAndEndDate(
							((BigInteger) patient[0]).longValue(), startDateCal.getTime(), endDateCal.getTime());
					Map<String, Object> data = new HashMap<>();
					data.put("patientId", patient[0].toString());
					data.put("patientName", patient[1].toString());

					if (patient[4] != null && patient[4].toString() != null) {
						data.put("patientPhone", patient[4].toString());
					} else {
						data.put("patientPhone", "");
					}

					data.put("ccmMins", dataCnt.get("ccmMins"));
					data.put("rpmMins", dataCnt.get("rpmMins"));
					data.put("pcmMins", dataCnt.get("pcmMins"));
					data.put("daysCounted", dataCnt.get("daysCounted"));

					if (dataCnt != null && (dataCnt.get("rpmMins") >= 20 || dataCnt.get("ccmMins") >= 20)
							&& dataCnt.get("daysCounted") >= 16) {
						data.put("billingStatus", "VALIDATION PENDING");
					} else {
						data.put("billingStatus", "IN PROGRESS");
					}

					if (patient[3] != null) {
						data.put("caseManagerId", Long.parseLong(patient[3].toString()));
					}
					if (patient[5] != null) {
						data.put("caseManagerName", patient[5].toString());
					}
					response.add(data);
				}

				if (allPatientsList != null && allPatientsList.size() > 0) {
					int _20MinCompleted = 0;
					int _40MinCompleted = 0;
					int _60MinCompleted = 0;
					int _16DaysCompleted = 0;
					Map<String, Object> allPatientsData = new HashMap<>();
					for (Object[] patient : allPatientsList) {
						Map<String, Integer> dataCnt = patientService
								.validationInfoByStartAndEndDate(((BigInteger) patient[0]).longValue(), sDate, eDate);

						if (dataCnt != null && (dataCnt.get("rpmMins") >= 20 || dataCnt.get("ccmMins") >= 20)
								|| dataCnt.get("phMins") >= 20) {
							_20MinCompleted += 1;
						}

						if (dataCnt != null && (dataCnt.get("rpmMins") >= 40 || dataCnt.get("ccmMins") >= 40)
								|| dataCnt.get("phMins") >= 40) {
							_40MinCompleted += 1;
						}

						if (dataCnt != null && (dataCnt.get("rpmMins") >= 60 || dataCnt.get("ccmMins") >= 60)
								|| dataCnt.get("phMins") >= 60) {
							_60MinCompleted += 1;
						}

						if (dataCnt != null && dataCnt.get("daysCounted") >= 16) {
							_16DaysCompleted += 1;
						}

					}
					allPatientsData.put("_20MinCompleted", _20MinCompleted);
					allPatientsData.put("_40MinCompleted", _40MinCompleted);
					allPatientsData.put("_60MinCompleted", _60MinCompleted);
					allPatientsData.put("_16DaysCompleted", _16DaysCompleted);
					allPatientsData.put("totalPatients", patientsCount);
					result.put("allPatientData", allPatientsData);
				}
				result.put("data", response);
				result.put("success", true);
				result.put("totalCount", patientsCount);
			} else {
				result.put("responsecode", Constants.ResponseCode.UNAUTHORIZEDACCESS);
				result.put("success", false);
				return result;
			}
		} else {
			result.put("responsecode", Constants.ResponseCode.UNAUTHORIZEDACCESS);
			result.put("success", false);
			return result;
		}
		return result;
	}

	@RequestMapping(value = "/getAllOrgs", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> getAllOrgsForAdmin(HttpServletRequest request) throws Exception {
		Map<String, Object> response = new HashMap<>();
		try {
			UserVO user = new ObjectMapper()
					.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
			if (user != null) {
				List<WatchrxGroup> grps = groupDAO.getAll();
				List<Map<String, Object>> result = new ArrayList<>();
				if (grps != null && grps.size() > 0) {
					grps.forEach(grp -> {
						Map<String, Object> gp = new HashMap<>();
						gp.put("groupId", grp.getGroupId());
						gp.put("groupName", grp.getGroupName());
						result.add(gp);
					});
					response.put("data", result);
					response.put("success", true);
					return response;
				}
			} else {
				response.put("responsecode", Constants.ResponseCode.UNAUTHORIZEDACCESS);
				response.put("success", false);
				return response;
			}
		} catch (Exception e) {
			response.put("responsecode", Constants.ResponseCode.UNAUTHORIZEDACCESS);
			response.put("success", false);
			return response;
		}
		return response;
	}

	@RequestMapping(value = "/allPatientBillingInfoReport/{orgId}/{startDate}/{endDate}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> allPatientBillingInfoReport(HttpServletRequest request,
			@PathVariable Long orgId, @PathVariable String startDate, @PathVariable String endDate) throws Exception {

		Map<String, Object> result = new HashMap<>();
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<Object[]> allPatientsList = new ArrayList<>();
		Long patientsCount = 0l;
		if (user != null) {

			patientsCount = patientService.getAllPatientsCountByOrgId(5, orgId, user.getUserId());
			logger.info("Patients Count for orgId:" + orgId);
			allPatientsList = patientService.getPatientBillingAddressInfo(orgId, 0, patientsCount.intValue(),
					user.getUserId());

			DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Date sDate = sdf.parse(startDate);

			Calendar startDateCal = Calendar.getInstance();
			startDateCal.setTime(sDate);
			startDateCal.set(Calendar.HOUR_OF_DAY, 00);
			startDateCal.set(Calendar.MINUTE, 00);
			startDateCal.set(Calendar.SECOND, 00);

			Date eDate = sdf.parse(endDate);
			Calendar endDateCal = Calendar.getInstance();
			endDateCal.setTime(eDate);
			endDateCal.set(Calendar.HOUR_OF_DAY, 23);
			endDateCal.set(Calendar.MINUTE, 59);
			endDateCal.set(Calendar.SECOND, 59);

			if (allPatientsList != null && allPatientsList.size() > 0) {
				List<Map<String, Object>> response = new ArrayList<>();
				for (Object[] patient : allPatientsList) {
					Map<String, Integer> dataCnt = patientService.validationInfoByStartAndEndDate(
							((BigInteger) patient[0]).longValue(), startDateCal.getTime(), endDateCal.getTime());

					Map<String, Object> data = new HashMap<>();
					data.put("patientId", patient[0].toString());
					data.put("patientName", patient[1].toString());

					if (patient[4] != null && patient[4].toString() != null) {
						data.put("patientPhone", patient[4].toString());
					} else {
						data.put("patientPhone", "");
					}

					data.put("ccmMins", dataCnt.get("ccmMins"));
					data.put("rpmMins", dataCnt.get("rpmMins"));
					data.put("pcmMins", dataCnt.get("pcmMins"));
					data.put("daysCounted", dataCnt.get("daysCounted"));

					if (dataCnt != null && (dataCnt.get("rpmMins") >= 20 || dataCnt.get("ccmMins") >= 20)
							&& dataCnt.get("daysCounted") >= 16) {
						data.put("billingStatus", "VALIDATION PENDING");
					} else {
						data.put("billingStatus", "IN PROGRESS");
					}

					if (patient[3] != null) {
						data.put("caseManagerId", Long.parseLong(patient[3].toString()));
					}
					if (patient[5] != null) {
						data.put("caseManagerName", patient[5].toString());
					}
					response.add(data);
				}
				result.put("data", response);
				result.put("success", true);
				result.put("totalCount", patientsCount);
			} else {
				result.put("responsecode", Constants.ResponseCode.UNAUTHORIZEDACCESS);
				result.put("success", false);
				return result;
			}
		} else {
			result.put("responsecode", Constants.ResponseCode.UNAUTHORIZEDACCESS);
			result.put("success", false);
			return result;
		}
		return result;
	}

	@RequestMapping(value = "/setOrgId", method = RequestMethod.POST)
	public @ResponseBody LoginStatusVO setOrgId(HttpServletRequest request, @RequestBody OTPDetails details)
			throws Exception {
		LoginStatusVO loginStatusVO = new LoginStatusVO();
		try {
			UserVO user = userService.getUserById(details.getUserId());
			if (user != null) {
				if (user.getOtpEnabled().equals("Y") && user.getOtp() != null && details.getOtp() != null) {
					if (details.getOtp().equals(user.getOtp())) {
						userService.saveUserOTPValidatedStatus(user.getUserName(), "OTP_VALIDATED");
					} else {
						loginStatusVO.setSuccess(false);
						loginStatusVO.setLoggedinUser(null);
						String messages[] = new String[1];
						messages[0] = "Please enter valid OTP recived on your registerd email id.";
						loginStatusVO.setMessages(messages);
						return loginStatusVO;
					}
				}
				user.setOrgId(details.getOrgId());
				request.getSession().setAttribute("user", user);

				UserVO updateUser = new ObjectMapper().readValue(
						AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
				loginStatusVO.setSuccess(true);
				loginStatusVO.setLoggedinUser(updateUser);
			} else {
				loginStatusVO.setSuccess(false);
				loginStatusVO.setLoggedinUser(null);
			}
		} catch (Exception e) {
			logger.error("Error while setting OrgID:" + e.getMessage());
			loginStatusVO.setSuccess(false);
			loginStatusVO.setLoggedinUser(null);
		}
		return loginStatusVO;
	}

	@RequestMapping(value = "/patientAddressBillingInfo/{startDate}/{endDate}/{search}", method = RequestMethod.GET)
	public @ResponseBody PatientWatchCaregiverListVO getPatientAddressBillingStatusBySearch(HttpServletRequest request,
			@PathVariable String startDate, @PathVariable String endDate, @PathVariable String search)
			throws Exception {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<Object[]> patientsList = new ArrayList<>();
		Long patientsCount = 0l;
		if (user != null) {
			patientsCount = patientService.getPatientsCountByOrgIdBySearch(user.getOrgId(), search, user.getUserId());
			patientsList = patientService.getPatientBillingAddressInfoBySearch(user.getOrgId(), 0,
					patientsCount.intValue(), search);
		} else {
			logger.info("Unauthorized access");
			PatientWatchCaregiverListVO response = new PatientWatchCaregiverListVO();
			response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

		List<PatientWatchCaregiverInfoVO> data = new ArrayList<>();
		PatientWatchCaregiverListVO patientsInfo = new PatientWatchCaregiverListVO();
		String msg[] = new String[1];
		if (patientsList.size() > 0) {
			Set<String> dataId = new HashSet<>();

			DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Date sDate = sdf.parse(startDate);

			Calendar startDateCal = Calendar.getInstance();
			startDateCal.setTime(sDate);
			startDateCal.set(Calendar.HOUR_OF_DAY, 00);
			startDateCal.set(Calendar.MINUTE, 00);
			startDateCal.set(Calendar.SECOND, 00);

			Date eDate = sdf.parse(endDate);
			Calendar endDateCal = Calendar.getInstance();
			endDateCal.setTime(eDate);
			endDateCal.set(Calendar.HOUR_OF_DAY, 23);
			endDateCal.set(Calendar.MINUTE, 59);
			endDateCal.set(Calendar.SECOND, 59);

			for (Object[] patient : patientsList) {
				if (dataId.add(patient[0].toString())) {

					Map<String, Integer> dataCnt = patientService.validationInfoByStartAndEndDate(
							((BigInteger) patient[0]).longValue(), startDateCal.getTime(), endDateCal.getTime());

					PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
					pwcInfo.setRpmCcMMins("RPM: " + dataCnt.get("rpmMins") + "\nCCM: " + dataCnt.get("ccmMins")
							+ "\nPCM: " + dataCnt.get("pcmMins"));
					pwcInfo.setNoOfDays(dataCnt.get("daysCounted"));
					pwcInfo.setPatientId(patient[0].toString());
					pwcInfo.setPatientName(patient[1].toString());
					if (patient[2] != null) {
						pwcInfo.setdob(patient[2].toString());
					}

					if (dataCnt != null && (dataCnt.get("rpmMins") >= 20 || dataCnt.get("ccmMins") >= 20)
							&& dataCnt.get("daysCounted") >= 16) {
						pwcInfo.setBillingStatus("VALIDATION PENDING");
					} else {
						pwcInfo.setBillingStatus("IN PROGRESS");
					}

					List<String> cptCodeList = new ArrayList<>();
					cptCodeList = patientDAO.getCptCodeForPatient(Long.parseLong(patient[0].toString()));
					pwcInfo.setcptCode(cptCodeList);

					if (patient[3] != null) {
						pwcInfo.setCaseManagerId(Long.parseLong(patient[3].toString()));
					}
					if (patient[5] != null) {
						pwcInfo.setCaseManagerName(patient[5].toString());
					}
					data.add(pwcInfo);
				}
			}
			patientsInfo.setPatientsInfo(data);
			patientsInfo.setCount(patientsCount);
			patientsInfo.setSuccess(true);
			msg[0] = new String("Operation Successful");
			patientsInfo.setMessages(msg);
		} else {
			WatchRxException excep = new WatchRxException(WatchRxExceptionCodes.EMPTY_PATIENTLIST);
			msg[0] = new String(excep.getErrDesc());
			patientsInfo.setResponseCode(Integer.parseInt(excep.getErrCode()));
			patientsInfo.setMessages(msg);
			patientsInfo.setSuccess(false);
		}
		return patientsInfo;
	}

	public Map<String, Object> bmcPatientScreening(MultipartFile file, HttpServletRequest request)
			throws IOException, ParseException {
		UserVO user = null;
		Map<String, Object> response = new HashMap<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			List<PatientVO> patientList = new ArrayList<>();
			XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
			XSSFSheet worksheet = workbook.getSheetAt(0);
			DataFormatter formatter = new DataFormatter();

			// List<Integer> missingRow = new ArrayList<>();
			for (int i = 1; i < worksheet.getPhysicalNumberOfRows(); i++) {
				PatientVO vo = new PatientVO();

				XSSFRow row = worksheet.getRow(i);

				vo.setFirstName(formatter.formatCellValue(row.getCell(1)).trim());
				vo.setLastName("Test" + i);
				vo.setCasemanagerId(user.getUserId());
				vo.setDob(new SimpleDateFormat("MM-dd-yyyy")
						.parse(new SimpleDateFormat("MM-dd-yyyy").format(new Date())));
				AddressVO add = new AddressVO();
				add.setAddress1("address");
				add.setCity("city");
				add.setState("state");
				add.setZip("12345");
				add.setCountry("USA");
				if (add.getCountry().equalsIgnoreCase("USA")) {
					vo.setPhoneNumber(row.getCell(2) != null
							? "+1" + formatter.formatCellValue(row.getCell(2)).replaceAll("-", "")
							: null);
				}
				vo.setAddress(add);
				List<ScreeningRequestVO> screeningRequestVOList = new ArrayList<>();
				setScreeningInfo(formatter.formatCellValue(row.getCell(4)).trim(),
						formatter.formatCellValue(row.getCell(9)).trim(),
						formatter.formatCellValue(row.getCell(1)).trim(), screeningRequestVOList, "Cancer (Breast)");
				setScreeningInfo(formatter.formatCellValue(row.getCell(5)).trim(),
						formatter.formatCellValue(row.getCell(9)).trim(),
						formatter.formatCellValue(row.getCell(1)).trim(), screeningRequestVOList, "Cancer (Colon)");
				setScreeningInfo(formatter.formatCellValue(row.getCell(6)).trim(),
						formatter.formatCellValue(row.getCell(9)).trim(),
						formatter.formatCellValue(row.getCell(1)).trim(), screeningRequestVOList,
						"Controlling High BP");
				setScreeningInfo(formatter.formatCellValue(row.getCell(7)).trim(),
						formatter.formatCellValue(row.getCell(9)).trim(),
						formatter.formatCellValue(row.getCell(1)).trim(), screeningRequestVOList, "DM  Eye Exam");
				setScreeningInfo(formatter.formatCellValue(row.getCell(8)).trim(),
						formatter.formatCellValue(row.getCell(9)).trim(),
						formatter.formatCellValue(row.getCell(1)).trim(), screeningRequestVOList, "DM  Hb A1c");
				vo.setScreeningRequestVOList(screeningRequestVOList);
				patientList.add(vo);
			}
			patientService.savePatientsByBulk(patientList, user.getOrgId());

		}
		response.put("status", true);
		response.put("message", "Operation Successful");
		return response;
	}

	private void setScreeningInfo(String appReq, String ScheduleDate, String patientId, List<ScreeningRequestVO> srVOs,
			String conName) throws ParseException {
		ScreeningRequestVO srvo = new ScreeningRequestVO();
		if (appReq.equals("")) {
			System.out.println("No Data Available");
		} else {
			if (appReq.equals("0")) {
				srvo.setAppointmentScheduled(true);
				srvo.setIsEligible(true);
			}
			if (appReq.equals("1")) {
				srvo.setAppointmentScheduled(false);
				srvo.setIsEligible(true);
			}
			System.out.println(ScheduleDate + "*******");
			if (!ScheduleDate.equals("NULL") && !ScheduleDate.isEmpty()) {
				try {
					srvo.setDueScheduledDate(new SimpleDateFormat("yyyy-MM-dd")
							.format(new SimpleDateFormat("yyyy/MM/dd").parse(ScheduleDate)));
					srvo.setLastScheduledDate(new SimpleDateFormat("yyyy-MM-dd")
							.format(new SimpleDateFormat("yyyy/MM/dd").parse(ScheduleDate)));
				} catch (Exception e) {
					srvo.setDueScheduledDate(new SimpleDateFormat("yyyy-MM-dd")
							.format(new SimpleDateFormat("yyyy/MM/dd").format(new Date())));
					srvo.setLastScheduledDate(new SimpleDateFormat("yyyy-MM-dd")
							.format(new SimpleDateFormat("yyyy/MM/dd").format(new Date())));
				}
			}
			srvo.setPatientId(Long.valueOf(patientId));
			srvo.setScreeningConditionId(screeningConditionDAO.findByProperty("conditionName", conName).get(0).getId());
			srVOs.add(srvo);
		}

	}

	@RequestMapping(value = "/bmcGraphData", method = RequestMethod.GET)
	public @ResponseBody Map<String, Map<String, Integer>> getBMCGraphData(HttpServletRequest request)
			throws WatchRxServiceException, JsonMappingException, JsonProcessingException {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<PatientVO> patientsList = new ArrayList<>();
		if (user != null) {
			System.out.println(user.getOrgId() + "  " + user.getRoleType());
			patientsList = patientService.getAllPatientsByOrgId(user.getOrgId(), user.getRoleType(), 0, 10000,
					user.getUserId());
			System.out.println(patientsList.size());
			List<Long> ids = patientsList.stream().map(a -> a.getPatientId()).collect(Collectors.toList());
			System.out.println(ids.size());
			return patientService.getScreeningPatientDuesForGraph(ids);
		}
		return null;
	}

	@RequestMapping(value = "/bmcDuesData", method = RequestMethod.GET)
	public @ResponseBody Map<String, Integer> getBMCDuesData(HttpServletRequest request)
			throws WatchRxServiceException, JsonMappingException, JsonProcessingException {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<PatientVO> patientsList = new ArrayList<>();
		if (user != null) {
			patientsList = patientService.getAllPatientsByOrgId(user.getOrgId(), user.getRoleType(), 0, 10000,
					user.getUserId());
			List<Long> ids = patientsList.stream().map(a -> a.getPatientId()).collect(Collectors.toList());
			return patientService.getScreeningPatientDues(ids);
		}
		return null;
	}

	@RequestMapping(value = "/screeningPatientDuesForGraph/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> getScreeningPatientDuesForGraph(HttpServletRequest request,
			@PathVariable Integer index, @PathVariable Integer pageSize)
			throws WatchRxServiceException, JsonMappingException, JsonProcessingException {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		List<PatientVO> patientsList = new ArrayList<>();
		Long patientsCount = 0l;
		if (user != null) {
			patientsCount = patientService.getAllPatientsCountByOrgId(5, user.getOrgId(), user.getUserId());
			patientsList = patientService.getAllPatientsByOrgId(user.getOrgId(), user.getRoleType(), index, pageSize,
					user.getUserId());
			List<Long> ids = patientsList.stream().map(a -> a.getPatientId()).collect(Collectors.toList());
			Map<String, Object> result = patientService.getScreeningPatientBMCDetails(ids);
			result.put("totalCount", patientsCount);
			return result;
		}
		return null;
	}

	@RequestMapping(value = "/screeningPatientDuesByCondition", method = RequestMethod.POST)
	public @ResponseBody Map<String, Object> getScreeningPatientDuesForGraph(HttpServletRequest request,
			@RequestBody Map<String, Integer> bmcSearch)
			throws WatchRxServiceException, JsonMappingException, JsonProcessingException {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		PatientGCInfos patientsList = new PatientGCInfos();
		if (user != null) {
			patientsList = patientService.getAllPatientsByOrgId(user.getOrgId(), user.getUserId());
			List<Long> ids = patientsList.getPatientInfo().stream().map(a -> Long.valueOf(a.getPatientId()))
					.collect(Collectors.toList());
			return patientService.getScreeningPatientBMCDetails(ids, bmcSearch);
		}
		return null;
	}

	@RequestMapping(value = "/screeningPatientDuesByConditionV1", method = RequestMethod.POST)
	public @ResponseBody Map<String, Object> screeningPatientDuesByConditionV1(HttpServletRequest request,
			@RequestBody Map<String, Object> bmcSearch)
			throws WatchRxServiceException, JsonMappingException, JsonProcessingException {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		PatientGCInfos patientsList = new PatientGCInfos();
		if (user != null) {
			patientsList = patientService.getAllPatientsByOrgId(user.getOrgId(), user.getUserId());
			List<Long> ids = patientsList.getPatientInfo().stream().map(a -> Long.valueOf(a.getPatientId()))
					.collect(Collectors.toList());
			return patientService.screeningPatientDuesByConditionV1(ids, bmcSearch);
		}
		return null;
	}

	@RequestMapping(value = "/getalertsforadminbydatev2", method = RequestMethod.POST)
	public @ResponseBody MedicationAlertListResponse getalertsforadminbydatev2(HttpServletRequest request,
			@RequestBody MedicationAlertRequestVO alertRequest) throws Exception {
		logger.info("inside getalertsforadminbydate: " + alertRequest.toString());

		MedicationAlertListResponse response = new MedicationAlertListResponse();
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {

				List<PatientAlertInfoVO> alertsList = WatchRxFactory.getAlertService().getAllAlertsByDateAndOrgId(
						alertRequest.getOrgId(), alertRequest.getStartDate(), alertRequest.getEndDate(),
						alertRequest.getIndex(), alertRequest.getPageSize());

				Long count = WatchRxFactory.getAlertService().getAlertsCountForAdminByOrgId(alertRequest.getOrgId(),
						alertRequest.getStartDate(), alertRequest.getEndDate(), alertRequest.getIndex(),
						alertRequest.getPageSize());

				response.setMedicationalertList(alertsList);
				response.setCount(count);
				response.setSuccess(true);
				response.setResponseCode(Constants.ResponseCode.MEDICATIONALERTSFOUND);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.MEDICATIONALERTSFOUND;
				response.setMessages(messages);
				return response;
			} else {
				logger.info("Unauthorized access");

				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}
	}

	@RequestMapping(value = "/getalldataforfilterv2/{orgId}", method = RequestMethod.GET)
	public @ResponseBody DataFilterResponse getAllDataForDataManagementFilterv2(HttpServletRequest request,
			@PathVariable int orgId) throws Exception {
		DataFilterResponse response = new DataFilterResponse();
		response = WatchRxFactory.getAlertService().getAllDetailsForFilterByOrgId(orgId);
		response.setSuccess(true);
		return response;

	}

	@RequestMapping(value = "/patientSaveStatus", method = RequestMethod.GET)
	public @ResponseBody StatusVO patientSaveStatus(@Valid @NotNull @RequestParam("patientId") Long patientId,
			@Valid @NotBlank @RequestParam("status") String status) throws Exception {
		logger.info("Saving Patient status..");

		StatusVO statusVo = patientService.patientSaveStatus(patientId, status);

		return statusVo;
	}

	@RequestMapping(value = "/savePatientCarePlanDetailsBulk", method = RequestMethod.POST)
	public @ResponseBody StatusVO savePatientCarePlanDetailsBulk(HttpServletRequest request,
			@RequestBody List<PatientCarePlanVO> vo) throws Exception {
		StatusVO vos = new StatusVO();
		String[] messages = new String[1];
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			try {
				logger.info("Inside savePatientCarePlanDetailsBulk :" + vo.toString());
				patientCarePlanService.savePatientCarePlansBulk(vo, user.getUserId());
				vos.setSuccess(true);
				messages[0] = "success";
				vos.setMessages(messages);
			} catch (Exception e) {
				logger.error("Error:" + e.getMessage());
				vos.setSuccess(true);
				messages[0] = "failed";
				vos.setMessages(messages);
			}
		} else {
			logger.error("Error: User Session not found.");
			vos.setSuccess(true);
			messages[0] = "failed";
			vos.setMessages(messages);
		}
		return vos;
	}

//	@RequestMapping(value = "/patientSummaryInfoByStatus/{index}/{pageSize}/{status}", method = RequestMethod.GET)
//	public @ResponseBody PatientWatchCaregiverListVO getPatientSummaryInfoByIndex(HttpServletRequest request,
//			@PathVariable Integer index, @PathVariable Integer pageSize,
//			@PathVariable(name = "status", required = true) String status) throws Exception {
//		logger.info(">>>>>>>>>>>>>>>>>>>>>>>>inside getPatientSummaryInfo:::: ");
//
//		Long totlaNumberOfPat = 0L;
//		List<PatientVO> patientsList = null;
//		List<PatientWatchCaregiverInfoVO> data = new ArrayList<>();
//		PatientWatchCaregiverListVO patientsInfo = new PatientWatchCaregiverListVO();
//
//		try {
//			UserVO user = new ObjectMapper()
//					.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
//			if (user == null) {
//				patientsInfo.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
//				String[] messages = new String[1];
//				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
//				patientsInfo.setMessages(messages);
//				patientsInfo.setSuccess(false);
//				return patientsInfo;
//			}
//			totlaNumberOfPat = patientService.getAllPatientsCountByOrgIdAndStatus(user.getRoleType(), user.getOrgId(),
//					status, user.getUserId(), false);
//			patientsList = patientService.getAllPatientsByOrgIdAndStatus(user.getOrgId(), user.getRoleType(), index,
//					pageSize, status, user.getUserId(), phyId,false);
//
//			String msg[] = new String[1];
//			Set<Long> pats = new HashSet<>();
//			for (PatientVO patient : patientsList) {
//				if (pats.add(patient.getPatientId())) {
//					patient.setMrn(
//							String.valueOf(patient.getMrn() == null ? patient.getPatientId() : patient.getMrn()));
//					PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
//					List<ClinicianVO> clinicianList = new ArrayList<>();
//					clinicianList = clinicianService.getCaseManagersByPatient(patient.getPatientId());
//					String names = new String();
//
//					if (clinicianList.size() > 0 && clinicianList.size() == 1) {
//						pwcInfo.setCaseManagerName(
//								clinicianList.get(0).getFirstName() + " " + clinicianList.get(0).getLastName());
//						pwcInfo.setCaseManagerId(clinicianList.get(0).getClinicianId());
//
//					} else if (clinicianList.size() > 0) {
//						for (int i = 0; i < clinicianList.size(); i++) {
//							if (i == 0) {
//								names = clinicianList.get(i).getFirstName() + " " + clinicianList.get(0).getLastName();
//							} else {
//								names = names + " / " + clinicianList.get(i).getFirstName() + " "
//										+ clinicianList.get(i).getLastName();
//							}
//						}
//						pwcInfo.setCaseManagerName(names);
//						pwcInfo.setCaseManagerId(clinicianList.get(0).getClinicianId());
//
//					}
//					List<CareGiverMinimalVO> careGiverMinimalVOList = new ArrayList<>();
//
//					clinicianList = clinicianService.getCareGiversByPatient(patient.getPatientId());
//
//					for (ClinicianVO clinicianVO : clinicianList) {
//						CareGiverMinimalVO careGiverMinimalVO = new CareGiverMinimalVO();
//						careGiverMinimalVO.setCareGiverId(clinicianVO.getClinicianId());
//						careGiverMinimalVO.setCareGiverFirstName(clinicianVO.getFirstName());
//						careGiverMinimalVO.setCareGiverLastName(clinicianVO.getLastName());
//						careGiverMinimalVOList.add(careGiverMinimalVO);
//
//					}
//					pwcInfo.setCareGiverVOList(careGiverMinimalVOList);
//					pwcInfo.setimgPath(patient.getPicPath());
//					pwcInfo.setPatientId(patient.getPatientId().toString());
//					String name = patient.getFirstName() + " " + patient.getLastName();
//					pwcInfo.setPatientNamePhone(name + " " + patient.getPhoneNumber());
//					pwcInfo.setPatientName(name);
//					pwcInfo.setPatientPhoneNumber(patient.getPhoneNumber());
//					pwcInfo.setChronicConditions(patient.getChronicConditions());
//					pwcInfo.setPhysicianName(patient.getPhysicianName());
//					pwcInfo.setMrnNo(patient.getMrn());
//					pwcInfo.setConsentStatus(patient.getConsentStatus());
//					data.add(pwcInfo);
//				}
//			}
//			// totlaNumberOfPat = (long) pats.size();
//			patientsInfo.setPatientsInfo(data);
//			patientsInfo.setCount(totlaNumberOfPat);
//			patientsInfo.setSuccess(true);
//			msg[0] = new String("Operation Successful");
//			patientsInfo.setMessages(msg);
//			return patientsInfo;
//		} catch (WatchRxServiceException e) {
//			logger.error("ERROR:{}", e);
//			logger.info("Service Exception");
//			patientsInfo.setResponseCode(Constants.ResponseCode.INTERNALERROR);
//			String[] messages = new String[1];
//			messages[0] = e.getMessage();
//			patientsInfo.setMessages(messages);
//			patientsInfo.setSuccess(false);
//			return patientsInfo;
//		} catch (Exception e) {
//			logger.error("ERROR:{}", e);
//			logger.info("Exception : " + e.getMessage());
//			patientsInfo.setResponseCode(Constants.ResponseCode.INTERNALERROR);
//			String[] messages = new String[1];
//			messages[0] = Constants.ResponseString.INTERNALERROR;
//			patientsInfo.setMessages(messages);
//			patientsInfo.setSuccess(false);
//			return patientsInfo;
//		}
//	}

	@RequestMapping(value = "/patientSummaryInfoByStatus/{pname}", method = RequestMethod.GET)
	public @ResponseBody PatientWatchCaregiverListVO getPatientSummaryInfoByNameAndStatus(HttpServletRequest request,
			@PathVariable String pname, @RequestParam(name = "ptstatus", required = true) String status)
			throws Exception {

		PatientListVO parentList = null;
		Long totlaNumberOfPat = 0L;
		List<PatientVO> patientList = new ArrayList<>();
		PatientWatchCaregiverListVO patientsInfo = new PatientWatchCaregiverListVO();
		try {
			UserVO user = new ObjectMapper()
					.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
			if (user == null) {
				patientsInfo.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				patientsInfo.setMessages(messages);
				patientsInfo.setSuccess(false);
				return patientsInfo;
			}

			if (user.getRoleType() == 1 || user.getRoleType() == 2) {
				parentList = patientService.getPatientsByName(pname);
			} else {
				parentList = patientService.getPatientsByNameAndOrgIdAndStatus(user.getOrgId(), user.getRoleType(),
						pname, status, user.getUserId());
			}
			patientList = parentList.getPatients();
			totlaNumberOfPat = (Long.valueOf(patientList.size()));

			List<PatientWatchCaregiverInfoVO> data = new ArrayList<>();

			String msg[] = new String[1];
			for (PatientVO patient : patientList) {
				patient.setMrn(String.valueOf(patient.getMrn() == null ? patient.getPatientId() : patient.getMrn()));
				PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
				List<ClinicianVO> clinicianList = new ArrayList<>();
				clinicianList = clinicianService.getCaseManagersByPatient(patient.getPatientId());
				String names = new String();

				if (clinicianList.size() > 0 && clinicianList.size() == 1) {
					pwcInfo.setCaseManagerName(
							clinicianList.get(0).getFirstName() + " " + clinicianList.get(0).getLastName());
					pwcInfo.setCaseManagerId(clinicianList.get(0).getClinicianId());

				} else if (clinicianList.size() > 0) {
					for (int i = 0; i < clinicianList.size(); i++) {
						if (i == 0) {
							names = clinicianList.get(i).getFirstName() + " " + clinicianList.get(0).getLastName();
						} else {
							names = names + " / " + clinicianList.get(i).getFirstName() + " "
									+ clinicianList.get(i).getLastName();
						}
					}
					pwcInfo.setCaseManagerName(names);
					pwcInfo.setCaseManagerId(clinicianList.get(0).getClinicianId());

				}
				List<CareGiverMinimalVO> careGiverMinimalVOList = new ArrayList<>();

				clinicianList = clinicianService.getCareGiversByPatient(patient.getPatientId());

				for (ClinicianVO clinicianVO : clinicianList) {
					CareGiverMinimalVO careGiverMinimalVO = new CareGiverMinimalVO();
					careGiverMinimalVO.setCareGiverId(clinicianVO.getClinicianId());
					careGiverMinimalVO.setCareGiverFirstName(clinicianVO.getFirstName());
					careGiverMinimalVO.setCareGiverLastName(clinicianVO.getLastName());
					careGiverMinimalVOList.add(careGiverMinimalVO);

				}
				pwcInfo.setCareGiverVOList(careGiverMinimalVOList);

				pwcInfo.setimgPath(patient.getPicPath());
				pwcInfo.setPatientId(patient.getPatientId().toString());
				String name = patient.getFirstName() + " " + patient.getLastName();
				pwcInfo.setPatientNamePhone(name + " " + patient.getPhoneNumber());
				pwcInfo.setPatientName(name);
				pwcInfo.setPatientPhoneNumber(patient.getPhoneNumber());

				AddressVO address = patient.getAddress();
				String patientAdd = address.getAddress1() + ", " + address.getCity() + ", " + address.getState();
				pwcInfo.setPatientAddress(patientAdd);
				if (patient.getWatch() != null) {
					if (patient.getWatch().getWatchMake() != null) {
						String assignedWatch = patient.getWatch().getWatchMake();
						if (patient.getWatch().getWatchModel() != null) {
							assignedWatch = assignedWatch + " " + patient.getWatch().getWatchModel();
						}
						pwcInfo.setAssignedWatch(assignedWatch);
					}
				}
				pwcInfo.setCaregiverRelationship(patient.getcaregiverRelationship());
				pwcInfo.setPhysicianId(patient.getPhysicianId());
				pwcInfo.setPhysicianName(patient.getPhysicianName());
				pwcInfo.setAlerts(patient.getAlerts());
				pwcInfo.setBillingStatus(patient.getBillingStatus());
				pwcInfo.setMrnNo(patient.getMrn());
				pwcInfo.setConsentStatus(patient.getConsentStatus());
				pwcInfo.setGroupName(patient.getGroupName());
				data.add(pwcInfo);
			}
			patientsInfo.setPatientsInfo(data);
			patientsInfo.setCount(totlaNumberOfPat);
			patientsInfo.setSuccess(true);
			msg[0] = new String("Operation Successful");
			patientsInfo.setMessages(msg);
			return patientsInfo;
		} catch (WatchRxServiceException e) {
			logger.info("Service Exception");
			patientsInfo.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = e.getMessage();
			patientsInfo.setMessages(messages);
			patientsInfo.setSuccess(false);
			return patientsInfo;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("Exception : " + e.getMessage());
			patientsInfo.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			patientsInfo.setMessages(messages);
			patientsInfo.setSuccess(false);
			return patientsInfo;
		}
	}

	@RequestMapping(value = "/dashboardAlertsCount", method = RequestMethod.GET)
	public Map<String, Object> dashboardAlertsCount(HttpServletRequest request) throws Exception {
		Map<String, Object> alertsData = new HashMap<>();
		try {
			UserVO user = new ObjectMapper()
					.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
			if (user != null) {
				Long clinicianID = clinicianService.getClinicianIdForUserId(user.getUserId(),
						(long) user.getRoleType());

				alertsData = patientService.dashboardAlertsCount(user.getOrgId(), clinicianID, user.getRoleType());
				alertsData.put("status", true);
			} else {
				alertsData.put("status", false);
			}
			return alertsData;
		} catch (Exception e) {
			alertsData.put("status", false);
			return alertsData;
		}
	}

	@RequestMapping(value = "/dashboardPatientCount/{startDate}/{endDate}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> dashboardPatientCount(HttpServletRequest request,
			@PathVariable String startDate, @PathVariable String endDate) throws Exception {
		Map<String, Object> result = new HashMap<>();
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				Long clinicianId = clinicianService.getClinicianIdForUserId(user.getUserId(),
						(long) user.getRoleType());

				result = patientService.dashboardPatientCount(user.getOrgId(), clinicianId, user.getRoleType(),
						startDate, endDate);
				result.put("status", true);
			}
			return result;
		} catch (WatchRxServiceException e) {
			result.put("status", false);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.put("status", false);
			return result;
		}
	}

	@RequestMapping(value = "/dashboardPatientGraph/{startDate}/{endDate}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> dashboardPatientGraph(HttpServletRequest request,
			@PathVariable String startDate, @PathVariable String endDate) throws Exception {
		Map<String, Object> result = new HashMap<>();
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				Long clinicianId = clinicianService.getClinicianIdForUserId(user.getUserId(),
						(long) user.getRoleType());

				result = patientService.dashboardPatientGraph(user.getOrgId(), clinicianId, user.getRoleType(),
						startDate, endDate);
				result.put("status", true);
			}
			return result;
		} catch (WatchRxServiceException e) {
			result.put("status", false);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.put("status", false);
			return result;
		}
	}

	@RequestMapping(value = "/createEmptyMasterDataForRPMCCM", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> createEmptyMasterDataForRPMCCM(HttpServletRequest request)
			throws Exception {
		Map<String, Object> result = new HashMap<>();
		try {
			patientService.createEmptyMasterDataForRPMCCM();
			result.put("status", true);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.put("status", false);
			return result;
		}
	}

	@RequestMapping(value = "/patientSummaryInfoByFilters/{index}/{pageSize}/{cmId}/{phyId}/{orgId}/{status}", method = RequestMethod.GET)
	public @ResponseBody PatientWatchCaregiverListVO getPatientSummaryInfoByIndex(HttpServletRequest request,
			@PathVariable Integer index, @PathVariable Integer pageSize,
			@PathVariable(name = "status", required = true) String status,
			@PathVariable(name = "cmId", required = false) Long cmId,
			@PathVariable(name = "phyId", required = false) Long phyId,
			@PathVariable(name = "orgId", required = false) Long orgId) throws Exception {
		logger.info(">>>>>>>>>>>>>>>>>>>>>>>>inside getPatientSummaryInfo:::: ");

		Long totlaNumberOfPat = 0L;
		List<PatientVO> patientsList = null;
		List<PatientWatchCaregiverInfoVO> data = new ArrayList<>();
		PatientWatchCaregiverListVO patientsInfo = new PatientWatchCaregiverListVO();
		boolean filterby = false;
		try {
			UserVO user = new ObjectMapper()
					.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
			System.out.println("------" + user.getRoleType());
			System.out.println("------" + user.getOrgId());
			System.out.println("------" + user.getUserId());
			if (user == null) {
				patientsInfo.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String[] messages = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				patientsInfo.setMessages(messages);
				patientsInfo.setSuccess(false);
				return patientsInfo;
			}
			if (user.getRoleType() == 1 || user.getRoleType() == 2) {
				if (cmId != 0 || orgId != 0) {
					filterby = true;
				}
				user.setUserId(cmId);
				if (orgId != 0) {
					user.setOrgId(orgId);
				}
			} else {
				if(user.getRoleType()==3) {
						user.setUserId(cmId);
				}
				filterby = true;
			}
			totlaNumberOfPat = patientService.getAllPatientsCountByOrgIdAndStatusAndFilters(user.getRoleType(), user.getOrgId(),
					status, user.getUserId(), phyId, filterby);
			patientsList = patientService.getAllPatientsByOrgIdAndStatus(user.getOrgId(), user.getRoleType(), index,
					pageSize, status, user.getUserId(), phyId, filterby);

			String msg[] = new String[1];
			Set<Long> pats = new HashSet<>();
			for (PatientVO patient : patientsList) {
				if (pats.add(patient.getPatientId())) {
					patient.setMrn(
							String.valueOf(patient.getMrn() == null ? patient.getPatientId() : patient.getMrn()));
					PatientWatchCaregiverInfoVO pwcInfo = new PatientWatchCaregiverInfoVO();
					List<ClinicianVO> clinicianList = new ArrayList<>();
					clinicianList = clinicianService.getCaseManagersByPatient(patient.getPatientId());
					String names = new String();

					if (clinicianList.size() > 0 && clinicianList.size() == 1) {
						pwcInfo.setCaseManagerName(
								clinicianList.get(0).getFirstName() + " " + clinicianList.get(0).getLastName());
						pwcInfo.setCaseManagerId(clinicianList.get(0).getClinicianId());

					} else if (clinicianList.size() > 0) {
						for (int i = 0; i < clinicianList.size(); i++) {
							if (i == 0) {
								names = clinicianList.get(i).getFirstName() + " " + clinicianList.get(0).getLastName();
							} else {
								names = names + " / " + clinicianList.get(i).getFirstName() + " "
										+ clinicianList.get(i).getLastName();
							}
						}
						pwcInfo.setCaseManagerName(names);
						pwcInfo.setCaseManagerId(clinicianList.get(0).getClinicianId());

					}
					List<CareGiverMinimalVO> careGiverMinimalVOList = new ArrayList<>();

					clinicianList = clinicianService.getCareGiversByPatient(patient.getPatientId());

					for (ClinicianVO clinicianVO : clinicianList) {
						CareGiverMinimalVO careGiverMinimalVO = new CareGiverMinimalVO();
						careGiverMinimalVO.setCareGiverId(clinicianVO.getClinicianId());
						careGiverMinimalVO.setCareGiverFirstName(clinicianVO.getFirstName());
						careGiverMinimalVO.setCareGiverLastName(clinicianVO.getLastName());
						careGiverMinimalVOList.add(careGiverMinimalVO);

					}
					pwcInfo.setCareGiverVOList(careGiverMinimalVOList);
					pwcInfo.setimgPath(patient.getPicPath());
					pwcInfo.setPatientId(patient.getPatientId().toString());
					String name = patient.getFirstName() + " " + patient.getLastName();
					pwcInfo.setPatientNamePhone(name + " " + patient.getPhoneNumber());
					pwcInfo.setPatientName(name);
					pwcInfo.setPatientPhoneNumber(patient.getPhoneNumber());
					pwcInfo.setChronicConditions(patient.getChronicConditions());
					pwcInfo.setPhysicianName(patient.getPhysicianName());
					pwcInfo.setMrnNo(patient.getMrn());
					pwcInfo.setConsentStatus(patient.getConsentStatus());
					pwcInfo.setGroupName(patient.getGroupName());
					data.add(pwcInfo);
				}
			}
			// totlaNumberOfPat = (long) pats.size();
			patientsInfo.setPatientsInfo(data);
			patientsInfo.setCount(totlaNumberOfPat);
			patientsInfo.setSuccess(true);
			msg[0] = new String("Operation Successful");
			patientsInfo.setMessages(msg);
			return patientsInfo;
		} catch (WatchRxServiceException e) {
			logger.error("ERROR:{}", e);
			logger.info("Service Exception");
			patientsInfo.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = e.getMessage();
			patientsInfo.setMessages(messages);
			patientsInfo.setSuccess(false);
			return patientsInfo;
		} catch (Exception e) {
			logger.error("ERROR:{}", e);
			logger.info("Exception : " + e.getMessage());
			patientsInfo.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			patientsInfo.setMessages(messages);
			patientsInfo.setSuccess(false);
			return patientsInfo;
		}
	}

	@RequestMapping(value = "/patientAddressBillingInfoV2/{index}/{pageSize}/{startDate}/{endDate}/{filterType}/{sortCol}/{sortDir}", method = RequestMethod.GET)
	public @ResponseBody PatientWatchCaregiverListVO patientAddressBillingInfoV2(HttpServletRequest request,
			@PathVariable Integer index, @PathVariable Integer pageSize, @PathVariable String startDate,
			@PathVariable String endDate, @PathVariable String filterType, @PathVariable String sortCol,
			@PathVariable String sortDir) throws Exception {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		if (user != null) {
			return patientService.getPatientBillingAddressInfoV2(index, pageSize, user.getOrgId(), user.getUserId(),
					user.getRoleType(), filterType == null ? "all" : filterType, startDate, endDate, sortCol, sortDir);
		} else {
			logger.info("Unauthorized access");
			PatientWatchCaregiverListVO response = new PatientWatchCaregiverListVO();
			response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}
	}

	@RequestMapping(value = "/patientAddressBillingInfoV2/{startDate}/{endDate}/{search}", method = RequestMethod.GET)
	public @ResponseBody PatientWatchCaregiverListVO patientAddressBillingInfoV2Search(HttpServletRequest request,
			@PathVariable String startDate, @PathVariable String endDate, @PathVariable String search)
			throws Exception {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		if (user != null) {
			return patientService.getPatientBillingAddressInfoV2Search(user.getOrgId(), user.getUserId(),
					user.getRoleType(), search, startDate, endDate);
		} else {
			logger.info("Unauthorized access");
			PatientWatchCaregiverListVO response = new PatientWatchCaregiverListVO();
			response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}
	}

	@RequestMapping(value = "/setUpdatedUser/{userId}", method = RequestMethod.GET)
	public @ResponseBody LoginStatusVO setUpdatedUser(HttpServletRequest request, @PathVariable Long userId)
			throws Exception {
		LoginStatusVO loginStatusVO = new LoginStatusVO();
		try {
			UserVO oldUser = new ObjectMapper()
					.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
			Long oldUserOrg = oldUser.getOrgId();
			UserVO user = userService.getUserById(userId);
			if (user != null) {
				request.getSession().removeAttribute("user");
				user.setOrgId(oldUserOrg);
				request.getSession().setAttribute("user", user);

				UserVO updateUser = new ObjectMapper().readValue(
						AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
				loginStatusVO.setSuccess(true);
				loginStatusVO.setLoggedinUser(updateUser);
			} else {
				loginStatusVO.setSuccess(false);
				loginStatusVO.setLoggedinUser(null);
			}
		} catch (Exception e) {
			logger.error("Error while setting OrgID:" + e.getMessage());
			loginStatusVO.setSuccess(false);
			loginStatusVO.setLoggedinUser(null);
		}
		return loginStatusVO;
	}

	@RequestMapping(value = "/patientsStatsCountByClinic/{orgId}/{startDate}/{endDate}/{careManagerId}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> patientsStatsCountByClinic(HttpServletRequest request,
			@PathVariable Long orgId, @PathVariable String startDate, @PathVariable String endDate,
			@PathVariable Long careManagerId) throws Exception {
		Map<String, Object> result = new HashMap<>();
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				result = patientService.patientsStatsCountByClinic(orgId, startDate, endDate, careManagerId);
				result.put("status", true);
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.put("status", false);
			return result;
		}
	}

	@RequestMapping(value = "/patientsStatsByClinic/{orgId}/{startDate}/{endDate}/{index}/{pageSize}/{careManagerId}/{filter}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> patientsStatsByClinic(HttpServletRequest request, @PathVariable Long orgId,
			@PathVariable String startDate, @PathVariable String endDate, @PathVariable Integer index,
			@PathVariable Integer pageSize, @PathVariable Long careManagerId, @PathVariable String filter)
			throws Exception {
		Map<String, Object> result = new HashMap<>();
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				result = patientService.patientsStatsByClinic(orgId, startDate, endDate, index, pageSize, careManagerId,
						filter);
				result.put("status", true);
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.put("status", false);
			return result;
		}
	}

	public @ResponseBody Map<String, Object> patientsStatsByClinicReport(HttpServletRequest request,
			@PathVariable Long orgId, @PathVariable String startDate, @PathVariable String endDate,
			@PathVariable Long careManagerId) throws Exception {
		Map<String, Object> result = new HashMap<>();
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				result = patientService.patientsStatsByClinicReport(orgId, startDate, endDate, careManagerId,
						(long) user.getRoleType());
				result.put("status", true);
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.put("status", false);
			return result;
		}
	}

	@RequestMapping(value = "/patientsStatsByClinicReport/{orgId}/{startDate}/{endDate}/{careManagerId}", method = RequestMethod.GET)
	public void downloadExcel(HttpServletResponse response, HttpServletRequest request, @PathVariable Long orgId,
			@PathVariable String startDate, @PathVariable String endDate, @PathVariable Long careManagerId)
			throws IOException {
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				HttpHeaders headers = new HttpHeaders();
				headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
				headers.setContentDispositionFormData("attachment", "workbook.xlsx");

				patientService.downloadBs(response, orgId, startDate, endDate, careManagerId,
						(long) user.getRoleType());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@RequestMapping(value = "/patientDashboardVitalData/{patientId}/{date}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> patientDashboardVitalGraph(HttpServletRequest request,
			@PathVariable Long patientId, @PathVariable String date)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		Map<String, Object> response = new HashMap<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.patientDashboardVitalGraph(patientId, date);
			response.put("status", true);
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			response.put("message", messages);
		} else {
			response.put("status", false);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "Period Type for Vital Request is not provided";
			response.put("message", messages);
		}
		return response;
	}

	@RequestMapping(value = "/updatePatientPrograms", method = RequestMethod.POST)
	public Map<String, Object> updatePatientPrograms(HttpServletRequest request,
			@RequestBody PatientProgramRequestVO req) throws IOException {
		UserVO user = null;
		Map<String, Object> response = new HashMap<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.updatePatientPrograms(req);
		} else {
			response.put("success", false);
			String messages[] = new String[2];
			messages[0] = "Operation failed";
			messages[1] = "Failed";
			response.put("message", messages);
		}
		return response;
	}

	@RequestMapping(value = "/updatePatientConsent/{patientId}/{consent}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> updatePatientConsent(HttpServletRequest request,
			@PathVariable Long patientId, @PathVariable Boolean consent)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		Map<String, Object> response = new HashMap<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.updatePatientConsent(patientId, consent);
		} else {
			response.put("success", false);
			response.put("message", "Failed to update consent");
		}
		return response;
	}

	@RequestMapping(value = "/getChronicConditionByPatientId/{patientId}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> getChronicConditionByPatientId(HttpServletRequest request,
			@PathVariable Long patientId) throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		Map<String, Object> response = new HashMap<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.getChronicConditionByPatientId(patientId);
		} else {
			response.put("status", false);
			response.put("message", "Failed to get chronic conditions");
		}
		return response;
	}

	@RequestMapping(value = "/updatesMins", method = RequestMethod.GET)
	public @ResponseBody Map<String, Double> updatesMins(HttpServletRequest request) {
//		return patientService.getOverAllMins(0l);
		return null;
	}

	@RequestMapping(value = "/latestCalledPatients/{index}/{pageSize}/{startDate}/{endDate}", method = RequestMethod.GET)
	public @ResponseBody PatientWatchCaregiverListVO latestCalledPatients(HttpServletRequest request,
			@PathVariable Integer index, @PathVariable Integer pageSize, @PathVariable String startDate,
			@PathVariable String endDate) throws Exception {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		if (user != null) {
			return patientService.latestCalledPatients(index, pageSize, user.getOrgId(), user.getUserId(),
					user.getRoleType(), startDate, endDate);
		} else {
			logger.info("Unauthorized access");
			PatientWatchCaregiverListVO response = new PatientWatchCaregiverListVO();
			response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}
	}

	@RequestMapping(value = "/getAllMins/{orgId}/{startDate}/{endDate}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> getAllMins(HttpServletRequest request, @PathVariable Long orgId,
			@PathVariable String startDate, @PathVariable String endDate) {
		return patientService.getOverAllMins(orgId, startDate, endDate);
	}

	@RequestMapping(value = "/getInhalerreadings/{patientId}/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> getInhalerreadings(HttpServletRequest request,
			@PathVariable Long patientId, @PathVariable Integer index, @PathVariable Integer pageSize)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		Map<String, Object> response = new HashMap<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.getInhalerreadings(patientId, index, pageSize);
		} else {
			response.put("status", false);
			response.put("message", "Failed to get chronic conditions");
		}
		return response;
	}

	@RequestMapping(value = "/getSpirometryReadings/{patientId}/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> getSpirometryReadings(HttpServletRequest request,
			@PathVariable Long patientId, @PathVariable Integer index, @PathVariable Integer pageSize)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		Map<String, Object> response = new HashMap<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientService.getSpirometryReadings(patientId, index, pageSize);
		} else {
			response.put("status", false);
			response.put("message", "Failed to get chronic conditions");
		}
		return response;
	}

	@RequestMapping(value = "/savePatientCarePlanQuestions", method = RequestMethod.POST)
	public @ResponseBody StatusVO savePatientCarePlanQuestions(HttpServletRequest request,
			@RequestBody PatientCarePlanVO vo) throws Exception {
		StatusVO vos = new StatusVO();
		String[] messages = new String[1];
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			try {
				String res = patientCarePlanService.savePatientCarePlanQuestionsAndAnswers(vo, user.getUserId());
				vos.setSuccess(true);
				messages[0] = res;
				vos.setMessages(messages);
			} catch (Exception e) {
				logger.error("Error:" + e.getMessage());
				vos.setSuccess(true);
				messages[0] = "failed";
				vos.setMessages(messages);
			}
		} else {
			logger.error("Error: User Session not found.");
			vos.setSuccess(true);
			messages[0] = "failed";
			vos.setMessages(messages);
		}
		return vos;
	}

	@RequestMapping(value = "/getPatientCarePlanQandAns/{patientId}/{carePlanId}", method = RequestMethod.GET)
	public @ResponseBody List<Map<String, Object>> getPatientCarePlanQandAns(HttpServletRequest request,
			@PathVariable Long patientId, @PathVariable Long carePlanId)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		List<Map<String, Object>> response = new ArrayList<>();
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		if (user != null) {
			response = patientCarePlanService.getCarePlansDataByPatientId(patientId, carePlanId);
		}
		return response;
	}

	@RequestMapping(value = "/carePlanQandAnsHistory/{date}/{careplanId}/{patientId}", method = RequestMethod.GET)
	public @ResponseBody List<Map<String, Object>> carePlanQandAnsHistory(HttpServletResponse response,
			HttpServletRequest request, @PathVariable String date, @PathVariable Long careplanId,
			@PathVariable Long patientId) throws IOException {
		UserVO user = null;
		List<Map<String, Object>> res = new ArrayList<>();
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				res = patientCarePlanService.getCarePlanHistoryByDates(patientId, careplanId, date);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return res;
	}

	@RequestMapping(value = "/dashboraReportByClinicAndCaseManager/{startDate}/{endDate}", method = RequestMethod.GET)
	public void dashboraReportByClinicAndCaseManager(HttpServletResponse response, HttpServletRequest request,
			@PathVariable String startDate, @PathVariable String endDate) throws IOException {
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				HttpHeaders headers = new HttpHeaders();
				headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
				headers.setContentDispositionFormData("attachment", "workbook.xlsx");
				patientService.downloadBs(response, user.getOrgId(), startDate, endDate, user.getUserId(),
						(long) user.getRoleType());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@RequestMapping(value = "/saveConsentData", method = RequestMethod.POST)
	public @ResponseBody Map<String, Object> saveConsentData(HttpServletRequest request,
			@RequestBody Map<String, String> requestBody) throws Exception {
		Map<String, Object> response = new HashMap<>();
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		if (user != null) {
			response = patientService.saveConsentData(requestBody);
		} else {
			logger.error("Error: User Session not found.");
			response.put("status", false);
		}
		return response;
	}

	@RequestMapping(value = "/getConsentData/{patientId}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> carePlanQandAnsHistory(HttpServletRequest request,
			@PathVariable Long patientId) throws IOException {
		UserVO user = null;
		Map<String, Object> response = new HashMap<>();
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				response = patientService.getConsentData(patientId);
			}
		} catch (Exception e) {
			e.printStackTrace();
			response.put("status", false);
		}
		return response;
	}

	@RequestMapping(value = "/downloadConsent/{patientId}", method = RequestMethod.GET)
	public void downloadConsent(HttpServletResponse response, HttpServletRequest request, @PathVariable Long patientId)
			throws IOException {
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				patientService.downloadConsent(response, patientId);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@RequestMapping(value = "/consentContact", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> consentContact(HttpServletRequest request) throws IOException {
		UserVO user = null;
		Map<String, Object> response = new HashMap<>();
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				response = patientService.consentContact(user.getOrgId(), user.getUserName());
			}
		} catch (Exception e) {
			e.printStackTrace();
			response.put("status", false);
		}
		return response;
	}

	@RequestMapping(value = "/getLatestVitalRecords/{patientId}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> getLatestVitalRecords(HttpServletRequest request,
			@PathVariable Long patientId) throws IOException {
		UserVO user = null;
		Map<String, Object> response = new HashMap<>();
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				response = patientService.getLastestVitalsByPatientId(patientId);
			}
		} catch (Exception e) {
			e.printStackTrace();
			response.put("status", false);
		}
		return response;
	}

	@RequestMapping(value = "/downloadAllPDFForEncounters", method = RequestMethod.POST)
	public @ResponseBody Map<String, String> downloadAllPDFForEncounters(HttpServletRequest request,
			@RequestBody AlertRequestVO alertsRequestBody) throws Exception {

		Date startDate = null;
		Date endDate = null;

		Calendar calendar = Calendar.getInstance();
	
		Calendar cal = Calendar.getInstance();
		String[] dateParts = alertsRequestBody.getStartDate()
				.substring(0, alertsRequestBody.getStartDate().indexOf("T")).split("-");
		calendar.set(Calendar.DAY_OF_MONTH, Integer.valueOf(dateParts[2]));
		calendar.set(Calendar.MONTH, Integer.valueOf(dateParts[1]) - 1);
		calendar.set(Calendar.YEAR, Integer.valueOf(dateParts[0]));
		calendar.set(Calendar.HOUR_OF_DAY, 00);
		calendar.set(Calendar.MINUTE, 00);
		calendar.set(Calendar.SECOND, 00);
		System.out.println(calendar.get(Calendar.DAY_OF_MONTH));
		String[] dateParts1 = alertsRequestBody.getEndDate().substring(0, alertsRequestBody.getEndDate().indexOf("T"))
				.split("-");
		cal.set(Calendar.DAY_OF_MONTH, Integer.valueOf(dateParts1[2]));
		cal.set(Calendar.MONTH, Integer.valueOf(dateParts1[1]) - 1);
		cal.set(Calendar.YEAR, Integer.valueOf(dateParts1[0]));
		cal.set(Calendar.HOUR_OF_DAY, 23);
		cal.set(Calendar.MINUTE, 59);
		cal.set(Calendar.SECOND, 59);

		startDate = calendar.getTime();
		endDate = cal.getTime();
		List<WatchrxPatient> patients = patientDAO.getAllPatientsByOrgId(alertsRequestBody.getOrgId());
		for (WatchrxPatient pat : patients) {
			List<WatchRxEncountersNew> enList = encountersNewDAO.reasonByPatientId(pat.getPatientId(),
					alertsRequestBody.getFilterType(), startDate, endDate);
			SimpleDateFormat formater1 = new SimpleDateFormat("MM-dd-yyyy HH:mm:ss");

			try {
				// creation of the document with a certain size and certain margins
				Document document = new Document(PageSize.A4, 20, 20, 20, 20);

				// creating table and set the column width
				PdfPTable table = new PdfPTable(5);
				float widths[] = { 1, 3, 6, 3, 3 };
				table.setWidths(widths);
				table.setHeaderRows(1);

				// add cell of table - header cell
				PdfPCell cell = new PdfPCell(new Phrase("No."));
				cell.setBackgroundColor(new BaseColor(0, 173, 239));
				table.addCell(cell);

				cell = new PdfPCell(new Phrase("Reason"));
				cell.setBackgroundColor(new BaseColor(0, 173, 239));
				table.addCell(cell);

				cell = new PdfPCell(new Phrase("Description"));
				cell.setBackgroundColor(new BaseColor(0, 173, 239));
				table.addCell(cell);

				cell = new PdfPCell(new Phrase("Duration"));
				cell.setBackgroundColor(new BaseColor(0, 173, 239));
				table.addCell(cell);

				cell = new PdfPCell(new Phrase("DateTime"));
				cell.setBackgroundColor(new BaseColor(0, 173, 239));
				table.addCell(cell);

				Phrase ph;
				// looping the table cell for adding definition
				String patient = null;
				int i = 0;
				for (WatchRxEncountersNew watchRxEncountersNew : enList) {
					i = i + 1;
					if (patient == null) {
						patient = watchRxEncountersNew.getWatchrxPatient().getFirstName() + "_"
								+ watchRxEncountersNew.getWatchrxPatient().getLastName();
					}
					cell = new PdfPCell();
					ph = new Phrase(String.valueOf(i));
					cell.addElement(ph);
					table.addCell(cell);

					cell = new PdfPCell();
					ph = new Phrase(watchRxEncountersNew.getEncounterReason());
					cell.addElement(ph);
					table.addCell(cell);

					cell = new PdfPCell();
					ph = new Phrase(watchRxEncountersNew.getEncounterDescription());
					cell.addElement(ph);
					table.addCell(cell);

					cell = new PdfPCell();
					ph = new Phrase(getMinsSecs(watchRxEncountersNew.getDuration()));
					cell.addElement(ph);
					table.addCell(cell);

					cell = new PdfPCell();
					ph = new Phrase(formater1.format(watchRxEncountersNew.getEncounterDatetime()));
					cell.addElement(ph);
					table.addCell(cell);
				}

				Chunk glue = new Chunk(new VerticalPositionMark());
				Paragraph p = new Paragraph("Name: " + new String(patient).replaceAll("_", " "));
				p.add(new Chunk(glue));
				p.add("Report: "
						+ alertsRequestBody.getStartDate().substring(0, alertsRequestBody.getStartDate().indexOf("T"))
						+ " to "
						+ alertsRequestBody.getEndDate().substring(0, alertsRequestBody.getEndDate().indexOf("T")));

				ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
				PdfWriter.getInstance(document, byteArrayOutputStream);
				document.open();
				document.add(p);
				document.add(Chunk.NEWLINE);
				document.add(table);
				document.close();
				GcsService gcsService = GcsServiceFactory.createGcsService(RetryParams.getDefaultInstance());
				AppIdentityService appIdentityService = AppIdentityServiceFactory.getAppIdentityService();
				String defaultBucketName = appIdentityService.getDefaultGcsBucketName();
				GcsFilename fileName = new GcsFilename(defaultBucketName + "/" + "encounter",
						"Encounter_" + patient + ".pdf");

				GcsFileOptions instance = GcsFileOptions.getDefaultInstance();
				// GcsFilename gcsFile = new GcsFilename(fileName);
				GcsOutputChannel outputChannel = gcsService.createOrReplace(fileName, instance);

				outputChannel.write(ByteBuffer.wrap(byteArrayOutputStream.toByteArray()));
				outputChannel.close();

				/*
				 * HttpHeaders headers = new HttpHeaders();
				 * headers.setContentType(MediaType.APPLICATION_PDF); // Here you have to set
				 * the actual filename of your pdf String filename = "Encounter"+patient+".pdf";
				 * headers.setContentDispositionFormData(filename, filename);
				 * headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");
				 */
				logger.info("$$$$$" + WatchRxUtils.readTextFileOnly("encounter/Encounter_" + patient + ".pdf"));
			} catch (DocumentException e) {
				e.printStackTrace();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	
	@RequestMapping(value = "/savePatientPrimaryInformation", method = RequestMethod.POST)
	public @ResponseBody CreatePatientResponseVO savePatientPrimaryInformation(HttpServletRequest request, @RequestBody CreatePatientRequestVO patientReqVo)
			throws Exception {
		
		CreatePatientResponseVO response = new CreatePatientResponseVO();	
		try {
			
			UserVO user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user == null) {
				logger.info("Unauthorized access");
				
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				return response;
			}
			
			if (user.getRoleType() == Constants.UserType.CAREGIVER || user.getRoleType() == Constants.UserType.CASEMANAGER) {				
				PatientVO patientVO = new PatientVO();
				DateFormat srcDf = new SimpleDateFormat("yyyy-MM-dd");
				
				// Populate Patient Basic Info from request
				if (patientReqVo.getBasicinfo() != null)
				{
					CreatePatientRequestBasicInformationVO basicInfoReq = patientReqVo.getBasicinfo();					
					if (basicInfoReq.getId() != null) {
						patientVO = patientService.getPatient(basicInfoReq.getId());
					}
					patientVO.setFirstName(basicInfoReq.getFirstname());
					patientVO.setLastName(basicInfoReq.getLastname());
					patientVO.setGender(basicInfoReq.getGender());
					patientVO.setDob(basicInfoReq.getDob() != null ? srcDf.parse(basicInfoReq.getDob()) : null);
					patientVO.setEmail(basicInfoReq.getEmail());
					patientVO.setConnectingDevice(basicInfoReq.getConnectingDevice());
					patientVO.setPersPhoneNumber(basicInfoReq.getPersPhonenumber());
					patientVO.setMrn(basicInfoReq.getMrn());
					patientVO.setPhoneNumber(basicInfoReq.getPhonenumber());	
					patientVO.setPhysicianId(basicInfoReq.getPhysicianId());
					patientVO.setCasemanagerId(basicInfoReq.getPrimaryCaseManagerId());
					patientVO.setConsentStatus(basicInfoReq.getConsentStatus());
					patientVO.setTimezone(basicInfoReq.getTimezone());
				}
				
				// Populate Patient Address details from request
				if(patientReqVo.getAddress() != null)
				{
					CreatePatientRequestAddressVO reqAddressVo = patientReqVo.getAddress();						
					AddressVO addressVO = patientVO.getAddress();
					addressVO.setAddressId(reqAddressVo.getAddressId());
					addressVO.setAddress1(reqAddressVo.getHousenumber());
					addressVO.setAddress2(reqAddressVo.getArea());
					addressVO.setZip(reqAddressVo.getZip());
					addressVO.setCity(reqAddressVo.getCity());
					addressVO.setState(reqAddressVo.getState());
					addressVO.setCountry(reqAddressVo.getCountry());
					patientVO.setAddress(addressVO);
				}
				
				// Save Patient details
				PatientVO patientData = patientService.savePatientAndReturnPatient(patientVO, user);
				
				//Save Patient Enrolled Programs
				if(patientReqVo.getProgramsEnrolled() != null)
				{
					patientService.updatePatientPrograms(patientReqVo.getProgramsEnrolled());
				}
				
				//Save Patient Chronic conditions
				if(patientReqVo.getChronicConditions() != null)
				{
					patientService.saveChronicConditions(patientData, patientReqVo.getChronicConditions());
				}
				
				//Save Patient Insurance details
				if(patientReqVo.getInsuranceData().getInsuranceVOList() != null)
				{
					patientData.setInsuranceVOList(patientReqVo.getInsuranceData().getInsuranceVOList());
					patientService.saveInsuranceDetails(patientData);
				}
				
				//Save Patient Preferred Contact details
				if (patientReqVo.getContactDetails() != null) {
					patientService.saveContactTimings(patientData, patientReqVo.getContactDetails().getPrefContactDetails());
				}
				
				//Save Patient cpt details
				if (patientReqVo.getEmergencyContacts() != null) {
					patientService.saveEmergencyContacts(patientData, patientReqVo.getEmergencyContacts());
				}
				
				response.setSuccess(true);
				response.setResponseCode(com.medsure.common.Constants.ResponseCode.PATIENTCREATED);
				String[] messages = new String[1];
				messages[0] = com.medsure.common.Constants.ResponseString.PATIENTCREATED;
				response.setMessages(messages);
				response.setCreatedPatientId(patientData.getPatientId());
				return response;
			} else {
				logger.info("Unauthorized access");
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}
		} catch (ParseException e) {
			logger.error( "ERROR IS ::"+ ExceptionUtils.getStackTrace(e));
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INCORRECTDATEFORMAT);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INCORRECTDATEFORMAT;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (WatchRxServiceException e) {
			logger.error( "ERROR IS ::"+ ExceptionUtils.getStackTrace(e));
			logger.info("Service Exception");
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = e.getMessage();
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (Exception e) {
			logger.error( "ERROR IS ::"+ ExceptionUtils.getStackTrace(e));
			logger.info("Exception : " + e.getMessage());
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}
	}

	@RequestMapping(value = "/savePreferedSlots", method = RequestMethod.POST)
	public @ResponseBody StatusVO savePreferedSlots(HttpServletRequest request,
			@RequestBody UpdatePatientEmergencyContactsVO emergencyContact) throws Exception {
		StatusVO resp = new StatusVO();
		emergencyContact.getEmergencyContacts();
		WatchrxPatient patient = patientDAO.findByPatientId(emergencyContact.getPatientId());
		patient.setPreferedDayOfWeek(emergencyContact.getPreferedSlots().get("preferedDayOfWeek"));
		patient.setPreferedTimeSlots(emergencyContact.getPreferedSlots().get("preferedTimeSlots"));
		patientDAO.save(patient);

		boolean success = true;
		resp.setSuccess(success);
		return resp;
	}

	@RequestMapping(value = "/needAttentionDetails", method = RequestMethod.POST, produces = "application/json")
	public @ResponseBody List<NeedAttentionVO> needAttentionDetails(HttpServletRequest request,
			@RequestBody AlertsCountRequestVO requestBody) throws Exception {
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		List<NeedAttentionVO> response = null;
		try {
			if (user != null) {
				response = dataTransformService.missedMedicationAndCriticalAlerts(requestBody);
				List<NeedAttentionVO> output = response.stream()
						.collect(Collectors.toMap(NeedAttentionVO::getAttentionType, Function.identity(),
								BinaryOperator.maxBy(Comparator.comparing(NeedAttentionVO::getSortDateTime)),
								() -> new TreeMap<>(Comparator.comparing(String::toString).reversed())))
						.values().stream().collect(Collectors.toList());
				return output;
			}
		} catch (Exception e) {
			logger.error(e);
		}
		return Collections.emptyList();
	}

	@RequestMapping(value = "/saveMedicationSummary", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveMedicationSummary(HttpServletRequest request,
			@RequestBody MedicationSummaryVO medicationSummaryVO) throws Exception {
		StatusVO statusVO = new StatusVO();
		try {
			UserVO user = new ObjectMapper()
					.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
			if (user != null) {
				statusVO = patientService.saveMedicationSummary(medicationSummaryVO, user.getUserId());
			} else {
				statusVO.setSuccess(false);
				statusVO.setMessages(new String[] { "Invalid user session" });
			}
		} catch (Exception e) {
			statusVO.setSuccess(false);
			statusVO.setMessages(new String[] { e.getMessage() });
		}
		return statusVO;
	}

	@RequestMapping(value = "/getMedicationSummary/{patientId}", method = RequestMethod.GET)
	public @ResponseBody List<MedicationSummaryVO> getMedicationSummary(HttpServletRequest request,
			@PathVariable Long patientId) throws IOException {
		UserVO user = null;
		List<MedicationSummaryVO> response = new ArrayList<>();
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				response = patientService.getMedicationSummaryByPatientId(patientId);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	@RequestMapping(value = "/deleteSummary/{summaryId}", method = RequestMethod.DELETE)
	public @ResponseBody StatusVO deleteSummary(HttpServletRequest request, @PathVariable Long summaryId)
			throws IOException {
		StatusVO statusVO = new StatusVO();
		UserVO user = null;
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				statusVO = patientService.deleteMedicationSummary(summaryId, user.getUserId());
			} else {
				statusVO.setSuccess(false);
				statusVO.setMessages(new String[] { "Invalid user session" });
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return statusVO;
	}

	@RequestMapping(value = "/patientEnrolledPrograms/{patientId}", method = RequestMethod.GET)
	public @ResponseBody Map<String, Object> getPatientEnrolledProgram(HttpServletRequest request,
			@PathVariable Long patientId) throws Exception {
		logger.info("inside patientEnrolledPrograms:::: " + patientId);
		Map<String, Object> resp = new HashMap<>();
		if (patientId > 0) {
			resp = patientService.getPatientProgramsAndChatMins(patientId);
		}
		return resp;
	}
	@RequestMapping(value = "/uploadAudio", method = RequestMethod.POST)
	public @ResponseBody StatusVO uploadAudio(HttpServletRequest request, @RequestParam("file") MultipartFile file, @RequestParam("patientId") Long patientId)
			throws Exception {
		logger.info("inside suploadAudio:::: ");
		StatusVO response = new StatusVO();
		try {
			UserVO user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
			     File originalFile = File.createTempFile("original_", ".wav");
			     try (InputStream in = file.getInputStream();
			    	     OutputStream out = new FileOutputStream(originalFile)) {
			    	    byte[] buffer = new byte[8192];
			    	    int bytesRead;
			    	    while ((bytesRead = in.read(buffer)) != -1) {
			    	    	out.write(buffer, 0, bytesRead);
			    	    }
			    	}
			     Double durations = 0.0;
				 File convertedFile = convertToMono(originalFile, durations);
		         MultipartFile convertedMultipartFile = fileToCommonsMultipartFile(convertedFile);
						patientService.uploadAudio(convertedMultipartFile, patientId, user,durations);
					response.setResponseCode(200);
					String messages[] = new String[1];
					messages[0] = "Audio upload in progress. A new encounter will be available on encounter screen for review .";
					response.setMessages(messages);
					response.setSuccess(true);
		         return response;
			} else {

				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}
	
	@RequestMapping(value = "/saveMedicationPrescription", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveMedicationPrescription(HttpServletRequest request, @RequestParam("file") MultipartFile file, @RequestParam("patientId") Long patientId)
			throws Exception {
		logger.info("inside suploadAudio:::: ");
		StatusVO response = new StatusVO();
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				return patientService.uploadMedicationPrescription(file, patientId, user);
			} else {
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}
	
	@RequestMapping(value = "/reviewMedicationPrescription/{patientId}", method = RequestMethod.GET)
	public @ResponseBody StatusVO saveMedicationPrescription(HttpServletRequest request, @PathVariable("patientId") Long patientId)
			throws Exception {
		logger.info("reviewMedicationPrescription ");
		StatusVO response = new StatusVO();
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				return patientService.reviewMedicationPrescsription(patientId);
			} else {
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}
	
	@RequestMapping(value = "/setPrescriptionReminder/{prescriptionId}/{isReminder}", method = RequestMethod.GET)
	public @ResponseBody StatusVO setPrescriptionReminder(HttpServletRequest request, @PathVariable("prescriptionId") Long prescriptionId, @PathVariable("isReminder") Boolean isReminder)
			throws Exception {
		logger.info("reviewMedicationPrescription ");
		StatusVO response = new StatusVO();
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				return patientService.setPrescriptionReminder(prescriptionId,isReminder);
			} else {
				response.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
				String messages[] = new String[1];
				messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
				response.setMessages(messages);
				response.setSuccess(false);
				return response;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}
	@RequestMapping(value = "/audiofileUrls", method = RequestMethod.GET)
	public @ResponseBody List<String> audiofileUrls(HttpServletRequest request)
			throws Exception {
		logger.info("audiofileUrls ");
		try {
			UserVO user = null;
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				return patientService.getAudioForProcessing();
			} 
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return null;

	}
	
	@RequestMapping(value = "/audioData", method = RequestMethod.PUT,consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public @ResponseBody StatusVO patientDetailsByImei(@RequestBody AudioRequest requestData, HttpServletRequest request) throws JsonProcessingException {
		StatusVO vo = new StatusVO();
		patientService.setAudioForProcessedData(requestData);
		vo.setSuccess(true);
		return vo;
	}

	@RequestMapping(value = "/getAllEncounterReasonsAndCodes", method = RequestMethod.GET)
	public @ResponseBody Map<String, String> getEncounterReasonCodes(HttpServletRequest request) throws IOException {
		UserVO user = null;
		Map<String, String> response = new LinkedHashMap<>();
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				response = patientService.getAllEncounterReasonsAndCodes();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	@RequestMapping(value = "/getDisplayableEncounterReasonsAndCodes", method = RequestMethod.GET)
	public @ResponseBody Map<String, String> getDisplayableEncounterAndCodes(HttpServletRequest request) throws IOException {
		UserVO user = null;
		Map<String, String> response = new LinkedHashMap<>();
		try {
			user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
					UserVO.class);
			if (user != null) {
				response = patientService.getAllDisplayableEncounterReasons();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}
	
	private File convertToMono(File file, Double durations ) throws Exception {
        AudioInputStream sourceStream = AudioSystem.getAudioInputStream(file);
        AudioFormat sourceFormat = sourceStream.getFormat();

        // Ensure it's PCM-signed first
        if (sourceFormat.getEncoding() != AudioFormat.Encoding.PCM_SIGNED) {
            AudioFormat pcmFormat = new AudioFormat(
                    AudioFormat.Encoding.PCM_SIGNED,
                    sourceFormat.getSampleRate(),
                    16,
                    sourceFormat.getChannels(),
                    sourceFormat.getChannels() * 2,
                    sourceFormat.getSampleRate(),
                    false
            );
            sourceStream = AudioSystem.getAudioInputStream(pcmFormat, sourceStream);
        }

        // Define target mono format
        AudioFormat monoFormat = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                16000,        // 16kHz
                16,           // 16-bit
                1,            // mono
                2,            // frame size = 16-bit mono
                16000,        // frame rate
                false         // little-endian
        );

        AudioInputStream monoStream = AudioSystem.getAudioInputStream(monoFormat, sourceStream);

        // Output file
        //File monoFile = new File("converted-mono.wav");
        File monoFile = File.createTempFile("audio_", ".wav");
        AudioSystem.write(monoStream, AudioFileFormat.Type.WAVE, monoFile);
        AudioFormat format = monoStream.getFormat();
        long frames = monoStream.getFrameLength();
        double durationInSeconds = (frames + 0.0) / format.getFrameRate();
        durations =  durationInSeconds / 60.0; // convert to minutes
        return monoFile;
    }
	
	 private MultipartFile fileToCommonsMultipartFile(File file) throws IOException {
	        DiskFileItemFactory factory = new DiskFileItemFactory();
	        DiskFileItem item = (DiskFileItem) factory.createItem(
	                "file", "audio/wav", false, file.getName());

	        try (InputStream input = new FileInputStream(file);
	             OutputStream os = item.getOutputStream()) {
	            IOUtils.copy(input, os);
	        }

	        return new CommonsMultipartFile(item);
	    }

}